from datetime import date, timedelta

from apps.planner.models import Plan, PlanTimeline

from tests.factories import PlanFactory, PlanProjectFactory, PlanTimelineFactory
from tests.base import BaseTestCase


class PlanModelTests(BaseTestCase):
    """
    Tests for the Plan model, including phases, statuses, and allocation logic.
    """

    def setUp(self):
        super().setUp()
        # Create a plan for testing
        self.plan = PlanFactory(company=self.company, year=date.today().year)

    def test_plan_creation(self):
        """Test that a Plan can be created successfully."""
        self.assertEqual(self.plan.company, self.company)
        self.assertIn(self.plan.phase, [p[0] for p in Plan.Phase.choices])
        self.assertIn(self.plan.status, [s[0] for s in Plan.Status.choices])

    def test_advance_current_phase(self):
        """Test advancing the plan through all phases."""
        initial_phase = self.plan.phase
        next_phase = self.plan.advance_current_phase()
        self.assertNotEqual(initial_phase, next_phase)
        # Continue advancing until ALLOCATED
        while next_phase != Plan.Phase.ALLOCATED:
            next_phase = self.plan.advance_current_phase()
        self.assertEqual(next_phase, Plan.Phase.ALLOCATED)

    def test_phase_choice_property(self):
        """Test that phase_choice property returns the Phase enum."""
        self.assertEqual(self.plan.phase_choice, self.plan.get_phase())

    def test_generate_timelines_creates_all_phases(self):
        """Ensure generate_timelines creates PlanTimeline instances for all phases."""
        self.plan.generate_timelines(seed_date=date.today())
        phases_created = self.plan.plantimeline_set.values_list("phase", flat=True)
        self.assertTrue(all(p[0] in phases_created for p in PlanTimeline.Phase.choices))

    def test_total_capital_and_expense_amount(self):
        """Test that total capital and expense sums work correctly."""
        # Create some plan projects
        PlanProjectFactory(plan=self.plan, capital_amount=1000, expense_amount=200)
        PlanProjectFactory(plan=self.plan, capital_amount=2000, expense_amount=300)
        self.assertEqual(self.plan.total_capital_amount(), 3000)
        self.assertEqual(self.plan.total_expense_amount(), 500)

    def test_get_current_phase_time_left(self):
        """Test that the current phase time left is correct."""
        PlanTimelineFactory(
            plan=self.plan,
            phase=self.plan.phase,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=5),
        )
        time_left = self.plan.get_current_phase_time_left()
        self.assertTrue(time_left.days <= 5)

    def test_calendars_property_returns_html(self):
        """Test that calendars property returns a list of HTML strings."""
        PlanTimelineFactory(plan=self.plan, phase=PlanTimeline.Phase.LOCATIONS_RANK)
        self.assertIsInstance(self.plan.calendars, list)
        self.assertTrue(all(isinstance(c, str) for c in self.plan.calendars))
