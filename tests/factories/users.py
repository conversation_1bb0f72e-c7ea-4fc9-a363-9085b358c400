import factory


class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = "users.User"

    username = factory.Faker("user_name")
    email = factory.LazyAttribute(lambda o: f"{o.username}@example.com")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    is_active = True


class RoleFactory(factory.django.DjangoModelFactory):
    """
    Factory for creating Role instances for testing.

    This factory automatically associates each Role with a Company
    (via CompanyFactory) unless explicitly provided.
    """

    class Meta:
        model = "users.Role"
        django_get_or_create = ("name", "company")

    name: str = factory.Sequence(lambda n: f"Role {n}")
