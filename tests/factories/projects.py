from datetime import date, <PERSON><PERSON><PERSON>
from random import randint

from django.core.management import call_command
from django.utils import timezone
from factory import SubFactory, fuzzy, post_generation, LazyAttribute
from factory.django import DjangoModelFactory

from apps.projects.models import BusinessSegment, Project
from apps.users.tests import UserFactory

from apps.projects.models import (
    Division,
    Project,
    ProjectExecutiveAction,
    ProjectHealth,
    ProjectPercentComplete,
    ProjectPlannedActivity,
    ProjectRecentAccomplishment,
    ProjectType,
    StarredProject,
    StrategicPillar,
    SubPillar,
)
from apps.projects.models.project_link import ProjectLink
from tests.factories.organizations import CompanyFactory


def choice_values(choices):
    return [value for value, label in choices]


class ProjectExecutiveActionFactory(DjangoModelFactory):
    class Meta:
        model = ProjectExecutiveAction

    text = fuzzy.FuzzyText()
    action = fuzzy.FuzzyChoice(choice_values(ProjectExecutiveAction.ACTION.choices))


class ProjectHealthFactory(DjangoModelFactory):
    class Meta:
        model = ProjectHealth

    budget_health = fuzzy.FuzzyChoice(choice_values(ProjectHealth.HEALTH.choices))
    schedule_health = fuzzy.FuzzyChoice(choice_values(ProjectHealth.HEALTH.choices))
    scope_health = fuzzy.FuzzyChoice(choice_values(ProjectHealth.HEALTH.choices))
    week = fuzzy.FuzzyInteger(1, timezone.now().isocalendar()[1])
    year = date.today().year


class ProjectLinkFactory(DjangoModelFactory):
    class Meta:
        model = ProjectLink

    name = fuzzy.FuzzyText()
    url = "http://www.example.com"


class ProjectPercentCompleteFactory(DjangoModelFactory):
    class Meta:
        model = ProjectPercentComplete

    percentage = fuzzy.FuzzyInteger(0, 100)
    week = fuzzy.FuzzyInteger(1, timezone.now().isocalendar()[1])
    year = date.today().year


class ProjectPlannedActivityFactory(DjangoModelFactory):
    class Meta:
        model = ProjectPlannedActivity

    text = fuzzy.FuzzyText()


class ProjectRecentAccomplishmentFactory(DjangoModelFactory):
    class Meta:
        model = ProjectRecentAccomplishment

    text = fuzzy.FuzzyText()


class ProjectTypeFactory(DjangoModelFactory):
    class Meta:
        model = ProjectType

    name = fuzzy.FuzzyText()


class StrategicPillarFactory(DjangoModelFactory):
    class Meta:
        model = StrategicPillar

    name = fuzzy.FuzzyText()


class StrategicSubPillarFactory(DjangoModelFactory):
    class Meta:
        model = SubPillar

    pillar = SubFactory(StrategicPillarFactory)
    name = fuzzy.FuzzyText()


class DivisionFactory(DjangoModelFactory):
    class Meta:
        model = Division

    name = fuzzy.FuzzyText()
    company = SubFactory(CompanyFactory)


class BusinessSegmentFactory(DjangoModelFactory):
    class Meta:
        model = BusinessSegment


class ProjectFactory(DjangoModelFactory):
    class Meta:
        model = Project

    name = fuzzy.FuzzyText()
    project_rigor = fuzzy.FuzzyChoice(choice_values(Project.PROJECT_RIGOR_CHOICES))
    primary_division = SubFactory(DivisionFactory)
    summary = fuzzy.FuzzyText()
    business_case = fuzzy.FuzzyText()
    funding_size = fuzzy.FuzzyChoice(choice_values(Project.MONEY_AMOUNT_CHOICES))
    annualized_savings = fuzzy.FuzzyChoice(choice_values(Project.MONEY_AMOUNT_CHOICES))
    capital_budget = fuzzy.FuzzyChoice(choice_values(Project.MONEY_AMOUNT_CHOICES))
    expense_budget = fuzzy.FuzzyChoice(choice_values(Project.MONEY_AMOUNT_CHOICES))
    funding_source = fuzzy.FuzzyChoice(choice_values(Project.FUNDING_SOURCE_CHOICES))
    payback_period = fuzzy.FuzzyChoice(choice_values(Project.PAYBACK_PERIOD_CHOICES))
    annual_savings_target = fuzzy.FuzzyInteger(100, 1_000_000)
    capital_expenditure = fuzzy.FuzzyChoice([True, False])
    car_number = fuzzy.FuzzyText()
    expense_io_number = fuzzy.FuzzyInteger(100_000, 999_999)
    capital_io_number = fuzzy.FuzzyInteger(100_000, 999_999)
    opex_expenditure = fuzzy.FuzzyChoice([True, False])
    company_code = fuzzy.FuzzyChoice(choice_values(Project.COMPANY_CODE_CHOICES))
    cost_center = fuzzy.FuzzyText()
    gl_account = fuzzy.FuzzyText()
    expected_duration = fuzzy.FuzzyChoice(choice_values(Project.DURATION_CHOICES))
    start_date = LazyAttribute(lambda o: o.created.date())
    end_date = LazyAttribute(lambda o: o.start_date + timedelta(days=120))
    technology_components = fuzzy.FuzzyText()
    priority = fuzzy.FuzzyChoice(choice_values(Project.PRIORITY_CHOICES))
    complexity = fuzzy.FuzzyChoice(choice_values(Project.COMPLEXITY_CHOICES))
    project_state = fuzzy.FuzzyChoice(choice_values(Project.PROJECT_STATE_CHOICES))
    phase = fuzzy.FuzzyChoice(choice_values(Project.PHASE_CHOICES))
    created_by = SubFactory(UserFactory)
    current_environment = fuzzy.FuzzyChoice(
        choice_values(Project.CURRENT_ENVIRONMENT_CHOICES)
    )
    failure_severity = fuzzy.FuzzyChoice(
        choice_values(Project.FAILURE_SEVERITY_CHOICES)
    )
    response_to_audit = fuzzy.FuzzyChoice(
        choice_values(Project.RESPONSE_TO_AUDIT_CHOICES)
    )
    estimation_confidence = fuzzy.FuzzyChoice(
        choice_values(Project.ESTIMATION_CONFIDENCE_CHOICES)
    )
    private = False

    created = LazyAttribute(lambda o: timezone.now() - timedelta(days=30))
    modified = LazyAttribute(lambda o: o.created)

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override the default _create to allow for custom creation logic"""
        create_related = kwargs.pop("create_related", False)
        obj = super()._create(model_class, *args, **kwargs)

        if create_related:
            from tests.factories.projects import (
                ProjectHealthFactory,
                ProjectPercentCompleteFactory,
                ProjectRecentAccomplishmentFactory,
                ProjectPlannedActivityFactory,
                ProjectExecutiveActionFactory,
                ProjectLinkFactory,
            )

            ProjectHealthFactory(project=obj)
            ProjectPercentCompleteFactory(project=obj)
            ProjectRecentAccomplishmentFactory(project=obj)
            ProjectPlannedActivityFactory(project=obj)
            ProjectExecutiveActionFactory(project=obj)
            ProjectLinkFactory(project=obj)

        return obj

    @post_generation
    def make_business_analysts(self, create, extracted, **kwargs):
        if not create:
            return
        for i in range(randint(1, 2)):
            self.business_analysts.add(UserFactory())

    @post_generation
    def make_business_leads(self, create, extracted, **kwargs):
        if not create:
            return
        for i in range(randint(1, 2)):
            self.business_leads.add(UserFactory())

    @post_generation
    def make_executive_owners(self, create, extracted, **kwargs):
        if not create:
            return
        for i in range(randint(1, 2)):
            self.executive_owners.add(UserFactory())

    @post_generation
    def make_other_involved_divisions(self, create, extracted, **kwargs):
        if not create:
            return
        if not Division.objects.exists():
            call_command("loaddata", "divisions.json", verbosity=0)
        for division in Division.objects.order_by("?")[: randint(1, 2)]:
            self.other_involved_divisions.add(division)
        # for i in range(randint(1, 2)):
        #     self.other_involved_divisions.add(DivisionFactory())

    @post_generation
    def make_other_stakeholders(self, create, extracted, **kwargs):
        if not create:
            return
        for i in range(randint(1, 2)):
            self.other_stakeholders.add(UserFactory())

    @post_generation
    def make_project_managers(self, create, extracted, **kwargs):
        if not create:
            return
        self.project_managers.add(UserFactory())


class StarredProjectFactory(DjangoModelFactory):
    class Meta:
        model = StarredProject

    project = SubFactory(ProjectFactory)
    starred_by = SubFactory(UserFactory)
