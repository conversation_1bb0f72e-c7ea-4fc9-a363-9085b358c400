from django.core.exceptions import ValidationError

from apps.users.models import RoleAssignment
from tests.factories import RoleFactory, CompanyFactory

from tests.base import BaseTestCase


class TestRoleAssignmentLifecycleHook(BaseTestCase):
    """
    Tests the lifecycle hook behavior of the RoleAssignment model to ensure
    that a user cannot be assigned a role belonging to a different company.
    """

    def setUp(self) -> None:
        """
        Sets up a company, user, and role objects for testing.
        """
        super().setUp()
        # Create a second company to test cross-company validation
        self.other_company = CompanyFactory(name="Other Company")

        # Create a role in the original company
        self.role_same_company = RoleFactory(company=self.company, name="Finance Lead")

        # Create a role in a different company
        self.role_other_company = RoleFactory(
            company=self.other_company, name="Project Manager"
        )

    def test_role_assignment_fails_for_different_companies(self) -> None:
        """
        Ensures that assigning a user to a role belonging to a different company
        raises a ValidationError before saving.
        """
        with self.assertRaises(ValidationError) as context:
            RoleAssignment.objects.create(
                role=self.role_other_company,
                user=self.regular_user,
            )

        self.assertIn(
            "User and role must belong to the same company.",
            str(context.exception),
        )

    def test_role_assignment_succeeds_for_same_company(self) -> None:
        """
        Ensures that assigning a user to a role in the same company succeeds.
        """
        assignment = RoleAssignment.objects.create(
            role=self.role_same_company,
            user=self.regular_user,
        )

        self.assertIsInstance(assignment, RoleAssignment)
        self.assertEqual(assignment.user.company, assignment.role.company)
