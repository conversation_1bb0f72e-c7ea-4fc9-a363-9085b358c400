"""
Django settings for mysite project.

For more information on this file, see
https://docs.djangoproject.com/en/1.6/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/1.6/ref/settings/
"""

import os

import django_cache_url
from pathlib import Path

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/1.6/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "tm$+l2k=s!@uxzv83qxxvvseki6lx1x5=3h!9l^_5t0t(rssam"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv("DEBUG", False)

ALLOWED_HOSTS = os.getenv("ALLOWED_HOSTS", "").split(",")
X_FRAME_OPTIONS = "SAMEORIGIN"

SITE_ID = 1
SITE_URL = os.getenv("SITE_URL", "http://127.0.0.1:8000")
IDEA_TRACKER_SITE_URL = os.getenv("IDEA_TRACKER_SITE_URL", None)

VERSION = os.getenv("VERSION", "unknown")

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.humanize",
    "django.contrib.sessions",
    "django.contrib.sites",
    "django.contrib.messages",
    "procrastinate.contrib.django",  # for pyscopg based tasks
    # auth apps
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "allauth.socialaccount.providers.google",
    "whitenoise.runserver_nostatic",
    "django.contrib.staticfiles",
    "django.forms",
    "tz_detect",
    "taggit",
    "rest_framework",
    "django_filters",
    "drf_yasg",
    "reversion",
    "waffle",
    "admin_sso",  # google login for admin
    "apps.actions",
    "apps.documents",
    "apps.ideas",
    "apps.notifications",
    "apps.planner",
    "apps.projects",
    "apps.purchases",
    "apps.reports",
    "apps.sitewide",
    "apps.slides",
    "apps.users",
    "apps.teams",
    "apps.programs",
    "apps.comments",
    "apps.attachments",
    "apps.links",
    "apps.locations",
    "apps.service_now",
    "apps.meetings",
    "apps.organizations",
    "apps.template_files",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "waffle.middleware.WaffleMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    # "apps.users.middleware.ProfileRedirectMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "tz_detect.middleware.TimezoneMiddleware",
    "allauth.account.middleware.AccountMiddleware",
]

ROOT_URLCONF = "mysite.urls"

WSGI_APPLICATION = "mysite.wsgi.application"

# Cache

CACHES = {"default": django_cache_url.config()}

# Database
# https://docs.djangoproject.com/en/1.6/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": os.getenv("DB_NAME"),
        "USER": os.getenv("DB_USER"),
        "PASSWORD": os.getenv("DB_PASS"),
        "HOST": os.getenv("DB_HOST"),
        "OPTIONS": {"sslmode": os.getenv("DATABASE_SSLMODE", "disable")},
        "ATOMIC_REQUESTS": True,
    }
}

# Hosted Email Settings

EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = "smtp.zoho.com"
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_USE_SSL = False
EMAIL_HOST_USER = os.getenv("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = os.getenv("EMAIL_HOST_PASSWORD")
DEFAULT_FROM_EMAIL = os.getenv("DEFAULT_FROM_EMAIL")
# API Settings

REST_FRAMEWORK = {
    "DEFAULT_RENDER_CLASSES": ["rest_framework.renderers.JSONRenderer"],
    "DEFAULT_PARSER_CLASSES": ["rest_framework.parsers.JSONParser"],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.SessionAuthentication"
    ],
    "DEFAULT_PERMISSION_CLASSES": ["rest_framework.permissions.IsAuthenticated"],
    "DEFAULT_FILTER_BACKENDS": ["django_filters.rest_framework.DjangoFilterBackend"],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": 100,
}

# Authentication
# https://docs.djangoproject.com/en/1.6/ref/settings/#auth

AUTHENTICATION_BACKENDS = (
    "django.contrib.auth.backends.ModelBackend",  # Default backend
    "apps.users.auth.DjangoAdminAuthBackend",
    "allauth.account.auth_backends.AuthenticationBackend",
)

DJANGO_ADMIN_SSO_OAUTH_CLIENT_ID = os.getenv("DJANGO_ADMIN_SSO_OAUTH_CLIENT_ID")
DJANGO_ADMIN_SSO_OAUTH_CLIENT_SECRET = os.getenv("DJANGO_ADMIN_SSO_OAUTH_CLIENT_SECRET")

# We use our own template, don't add the admin_sso login button
DJANGO_ADMIN_SSO_ADD_LOGIN_BUTTON = False

AUTH_USER_MODEL = "users.User"


SECURE_SSL_REDIRECT = os.getenv("SECURE_SSL_REDIRECT", "FALSE").upper() == "TRUE"

LOGIN_URL = "account_login"
LOGOUT_REDIRECT_URL = "account_login"


# Templates

TEMPLATES = (
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": ["templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "debug": DEBUG,
            "context_processors": (
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.request",
                "django.template.context_processors.debug",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.tz",
                "apps.sitewide.context_processors.base_template",
                "django.contrib.messages.context_processors.messages",
            ),
            "builtins": ["apps.sitewide.templatetags.sitewide_tags"],
        },
    },
)
FORM_RENDERER = "django.forms.renderers.TemplatesSetting"

# Internationalization
# https://docs.djangoproject.com/en/1.6/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = False

USE_L10N = True

USE_TZ = True

# Media Files

DEFAULT_FILE_STORAGE = os.getenv(
    "DEFAULT_FILE_STORAGE", "django.core.files.storage.FileSystemStorage"
)
THUMBNAIL_DEFAULT_STORAGE = DEFAULT_FILE_STORAGE


MEDIA_ROOT = os.path.join(BASE_DIR, "media/")

MEDIA_URL = os.getenv("MEDIA_URL", "/media/")

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/1.6/howto/static-files/

STATICFILES_STORAGE = os.getenv(
    "STATICFILES_STORAGE", "django.contrib.staticfiles.storage.StaticFilesStorage"
)

STATIC_URL = "static/"
STATICFILES_DIRS = [
    "static",
]
STATIC_ROOT = BASE_DIR / "staticfiles"

STATICFILES_FINDERS = (
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
)

# Metrics


ENABLE_SENTRY = os.environ.get("ENABLE_SENTRY", "FALSE").upper() == "TRUE"
SENTRY_DSN = os.getenv("SENTRY_DSN", None)
SENTRY_ENV = os.getenv("SENTRY_ENVIRONMENT", "unknown")

# Logging

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s"
        },
        "simple": {
            "format": "%(asctime)s Django SM_Project_Tracker: %(message)s",
            "datefmt": "%Y-%m-%dT%H:%M:%S",
        },
    },
    "filters": {"require_debug_false": {"()": "django.utils.log.RequireDebugFalse"}},
    "handlers": {
        "console": {"level": "DEBUG", "class": "logging.StreamHandler"},
        "mail_admins": {
            "level": "ERROR",
            "filters": ["require_debug_false"],
            "class": "django.utils.log.AdminEmailHandler",
        },
    },
    "loggers": {
        "py.warnings": {"handlers": ["console"]},
        "django": {"handlers": ["console"]},
    },
}

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

SYSTEM_ADMINISTRATOR_EMAIL = "<EMAIL>"

# Authentication and allauth settings
SOCIALACCOUNT_PROVIDERS = {
    "google": {
        "APP": {
            # we use the same variables for the ADMIN SSO because the variables above cant change
            # because of admin sso library but they are the same values
            "client_id": DJANGO_ADMIN_SSO_OAUTH_CLIENT_ID,
            "secret": DJANGO_ADMIN_SSO_OAUTH_CLIENT_SECRET,
            "key": "",
        },
        "SCOPE": [
            "profile",
            "email",
        ],
        "AUTH_PARAMS": {
            "access_type": "online",
        },
        "OAUTH_PKCE_ENABLED": True,
    },
}
# Social Account Settings
SOCIALACCOUNT_LOGIN_ON_GET = True
SOCIALACCOUNT_AUTO_SIGNUP = True
ACCOUNT_LOGOUT_ON_GET = True
ACCOUNT_LOGIN_FIELDS = {"email*"}
ACCOUNT_SIGNUP_FIELDS = ["email*"]

ACCOUNT_ADAPTER = "apps.users.adapters.CustomAccountAdapter"
SOCIALACCOUNT_ADAPTER = "apps.users.adapters.CustomSocialAccountAdapter"

FORMS_URLFIELD_ASSUME_HTTPS = True
