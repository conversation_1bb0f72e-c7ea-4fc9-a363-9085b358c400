import { alertBox } from "./widgets/alert-box";
import * as autosize from "autosize";
// @ts-ignore
import "./get-to-green";
// @ts-ignore
import Tagify from "@yaireo/tagify";
import jQuery from "jquery";
import "jquery.scrollto";
import "kendo-ui-core/js/kendo.datepicker";
import "kendo-ui-core/js/kendo.slider";
import "select2";

import createHTMLEditor from "./utils/create-html-editor";

import "@yaireo/tagify/dist/tagify.css";
// import "kendo-ui-core/css/web/kendo.common.core.css";
import "kendo-ui-core/css/web/kendo.common.css";
import "kendo-ui-core/css/web/kendo.default.css";
import "select2/dist/css/select2.css";
import "quill/dist/quill.bubble.css";

import "./project-reorderable-form";
import MicroModal from "micromodal";

interface Tag {
    value: string;
}


declare const requestStorage: {
    tagsWhitelist: Tag[];
};

// Scroll to form sections
const pageLinkElems = document.getElementsByClassName(
    "Page-link",
) as HTMLCollectionOf<HTMLAnchorElement>;
for (const pageLink of pageLinkElems) {
    pageLink.addEventListener("click", event => {
        event.preventDefault();
        const targetId = pageLink.getAttribute("href");
        if (targetId == null) {
            console.log(`Could not scroll to "${targetId}"`);
            return;
        }
        jQuery.scrollTo(targetId, 500, { offset: -188 });
        for (const pLink of pageLinkElems) {
            pLink.classList.remove("Page-link--active");
        }
        pageLink.classList.add("Page-link--active");
    });
}

// Scroll to error fields
const errorLinkElems = document.getElementsByClassName(
    "ErrorLink",
) as HTMLCollectionOf<HTMLAnchorElement>;
for (const errorLink of errorLinkElems) {
    errorLink.addEventListener("click", event => {
        event.preventDefault();
        const targetId = errorLink.getAttribute("href");
        if (targetId == null) {
            console.log(`Could not scroll to "${targetId}"`);
            return;
        }
        jQuery.scrollTo(targetId, 500, { offset: -228 });
    });
}

// Date-time inputs
jQuery("input[type='date']").kendoDatePicker({ format: "yyyy-MM-dd" });

export const formatSelection = (selection: any) => {
    const displayName = selection.element.getAttribute("data-displayname");
    if (displayName) {
        return displayName;
    }
    return selection.text;
};

// @ts-ignore
jQuery("select[multiple]").select2({ templateSelection: formatSelection });

// Fancy select boxes
// jQuery("select[multiple]").select2();
const formatAction = (data: any) => {
    // Return immediately if there is no element attached
    if (data.element == null) {
        return null;
    }

    // Just return the empty label text when there is no value
    if (data.element.value == "") {
        return data.text;
    }

    const imageUrl = `/files/dist/images/icons/icon-action-${data.element.value.replace(
        " ",
        "_",
    )}.svg`;
    const template = `<span><image src="${imageUrl}" alt="${
        data.element.value
    }" />&nbsp;&nbsp;${data.text}</span>`;
    return jQuery<HTMLElement>(template);
};
jQuery("select.ProjectForm-executiveAction-action-select").select2({
    minimumResultsForSearch: 10,
    templateResult: formatAction,
    templateSelection: formatAction,
});

// Tags
const tagifyInputElems = document.querySelectorAll(
    "input[data-tagify]",
) as NodeListOf<HTMLInputElement>;
for (const tagifyInputElem of tagifyInputElems) {
    new Tagify(tagifyInputElem, {
        whitelist: requestStorage.tagsWhitelist || [],
        dropdown: {
            enabled: 1,
        },
    });
}

// Auto-sizing text areas
// @ts-ignore
autosize(document.querySelectorAll(".TextArea--small"));
// @ts-ignore
autosize(document.querySelectorAll(".TextArea--autosize"));

// WYSIWYG text areas
createHTMLEditor("#id_summary");
createHTMLEditor("#id_business_case");
createHTMLEditor("#id_technology_components");
createHTMLEditor("#id_corporate_communication_needs_description");

// Clear Impact Checkboxes
for (const elem of document.querySelectorAll(
    'input[type="checkbox"][name$="-impact_none"]',
) as NodeListOf<HTMLInputElement>) {
    // clear "No Impact" checkbox
    const siblingInputElems = elem.parentElement!.parentElement!.querySelectorAll(
        `input[type="checkbox"]:not(#${elem.id})`,
    ) as NodeListOf<HTMLInputElement>;
    for (const inputElem of siblingInputElems) {
        inputElem.addEventListener("change", event => {
            if (inputElem.checked) {
                elem.checked = false;
            }
        });
    }

    // clear all other impact checkboxes
    elem.addEventListener("change", event => {
        if (elem.checked) {
            for (const inputElem of siblingInputElems) {
                inputElem.checked = false;
            }
        }
    });
}

// Add a person
const addPersonForm = <HTMLFormElement | null>(
    document.querySelector("#add-person-form")
);
if (addPersonForm != null) {
    addPersonForm.addEventListener("ajax:success", event => {
        addPersonForm.reset();
        const customEvent = event as CustomEvent<any>;
        const data = customEvent.detail.contents.data;
        const personFieldIds = [
            "id_executive_owners",
            "id_business_leads",
            "id_business_analysts",
            "id_other_stakeholders",
            "id_project_managers",
        ];
        for (const personFieldId of personFieldIds) {
            const fieldElem = <HTMLSelectElement | null>(
                document.getElementById(personFieldId)
            );
            if (fieldElem == null) {
                break;
            }
            const optionElem = new Option(
                `${data.first_name} ${data.last_name}`,
                data.id,
                false,
                false,
            );
            fieldElem.appendChild(optionElem);
        }

        MicroModal.show("add-person-modal");
        MicroModal.close("add-person-modal");

        alertBox(`${data.first_name} ${data.last_name} added.`);
    });

    addPersonForm.addEventListener("ajax:failure", event => {
        const customEvent = event as CustomEvent<any>;
        for (const fieldName of Object.keys(
            customEvent.detail.contents.errors,
        )) {
            const fieldElem = addPersonForm.querySelector(`#id_${fieldName}`);
            if (fieldElem == null) {
                console.log(
                    `Could not display field error on #id_${fieldName}`,
                );
                return;
            }
            const formControlElem = fieldElem.parentElement;
            if (formControlElem == null) {
                return;
            }
            const formGroupElem = formControlElem.parentElement;
            if (formGroupElem == null) {
                return;
            }
            formGroupElem.classList.add("Form-group--errors");
            let errorListElem = formGroupElem.querySelector(".Form-errors");
            if (errorListElem == null) {
                errorListElem = document.createElement("ul");
                errorListElem.classList.add("Form-errors");
            }
            errorListElem.innerHTML = "";
            const errors = customEvent.detail.contents.errors[
                fieldName
            ] as Array<string>;
            for (const error of errors) {
                const errorItemElem = document.createElement("li");
                errorItemElem.innerText = error;
                errorListElem.appendChild(errorItemElem);
            }
            formGroupElem.appendChild(errorListElem);
        }
    });
}

