import {
    addMonths,
    subMonths,
    max as dateMax,
    min as dateMin,
    parse,
} from "date-fns";
import { max, min } from "d3-array";
import { axisTop } from "d3-axis";
import { scaleBand, scaleOrdinal, scaleTime } from "d3-scale";
import { event, select, Selection } from "d3-selection";
import { timeMonth } from "d3-time";
import { timeFormat } from "d3-time-format";
import "d3-transition";
import URI from "urijs";

function getDateRange(): [Date, Date] {
    const now = new Date();
    const nowMonth = new Date(now.getFullYear(), now.getMonth());
    let rangeStartDate = subMonths(nowMonth, 3);
    let rangeEndDate = addMonths(nowMonth, 15);
    const uri = new URI();
    const queryStartDate = uri.search(true)["date_after"] as string | null;
    const queryEndDate = uri.search(true)["date_before"] as string | null;
    let parsedStartDate: Date | null = null;
    if (queryStartDate) {
        parsedStartDate = parse(queryStartDate) as Date | null;
    }
    let parsedEndDate: Date | null = null;
    if (queryEndDate) {
        parsedEndDate = parse(queryEndDate) as Date | null;
    }
    if (parsedStartDate != null) {
        rangeStartDate = parsedStartDate;
    }
    if (parsedEndDate != null) {
        rangeEndDate = parsedEndDate;
    }
    if (parsedStartDate != null && parsedEndDate == null) {
        rangeEndDate = addMonths(parsedStartDate, 18);
    }
    if (parsedStartDate == null && parsedEndDate != null) {
        rangeStartDate = subMonths(parsedEndDate, 18);
    }
    return [rangeStartDate, rangeEndDate];
}
const [graphStartDate, graphEndDate] = getDateRange();

interface Project {
    id: string;
    name: string;
    project_state: string;
    health: string;
    complete: number;
    start_date: string;
    end_date: string;
    executive_owners: string;

    project_manager: string;
    url: string;
}

interface GraphData {
    id: string;
    name: string;
    projectState: string;
    health: string;
    complete: number;
    displayStartDate: Date;
    displayEndDate: Date;
    startDate: Date;
    endDate: Date;
    executiveOwners: string;
    brms: string;
    projectManager: string;
    url: string;
}

declare const requestStorage: {
    projects: Project[];
};

let data: GraphData[] = [];
if (requestStorage && requestStorage.projects) {
    data = requestStorage.projects.map(project => {
        return {
            id: project.id,
            name: project.name,
            projectState: project.project_state,
            health: project.health,
            complete: project.complete / 100,
            displayStartDate: new Date(project.start_date),
            displayEndDate: new Date(project.end_date),
            startDate: dateMax(new Date(project.start_date), graphStartDate),
            endDate: dateMin(new Date(project.end_date), graphEndDate),
            executiveOwners: project.executive_owners,

            projectManager: project.project_manager,
            url: project.url,
        };
    });
}
data = data.filter(
    d =>
        d.displayStartDate < subMonths(graphEndDate, 1) &&
        d.displayEndDate > addMonths(graphStartDate, 1),
);
data.sort(
    (x1: GraphData, x2: GraphData) =>
        x2.displayStartDate.getTime() - x1.displayStartDate.getTime(),
);

const margin = { top: 80, right: 40, bottom: 20, left: 0 };
const boxMargin = { top: 6, right: 6, bottom: 6, left: 6 };
const rectHeight = 64;
const innerPadding = 8;

let xMin = min<GraphData, Date>(data, d => d.startDate) || new Date();
let xMax = max<GraphData, Date>(data, d => d.endDate) || new Date();
console.log(xMin, xMax);

const monthCount =
    xMax.getFullYear() * 12 +
    xMax.getMonth() -
    (xMin.getFullYear() * 12 + xMin.getMonth());
const monthWidth = 100;

const height =
    data.length * rectHeight +
    (data.length - 1) * innerPadding +
    margin.top +
    margin.bottom;
const width = monthCount * monthWidth;

const healthColors = scaleOrdinal<string>()
    .domain(["red", "yellow", "green"])
    .range(["#D15C5C", "#D1C65C", "#8CD15C"]);

const xScale = scaleTime()
    .domain([xMin, xMax])
    .range([margin.left, width - margin.right])
    .nice(timeMonth);

const yScale = scaleBand()
    .domain(data.map(d => d.name))
    .rangeRound([height - margin.bottom, margin.top]);

const xAxis = (g: Selection<SVGGElement, {}, HTMLElement, any>) =>
    g
        .attr("transform", `translate(0,${margin.top - 8})`)
        .call(
            axisTop(xScale)
                .ticks(timeMonth.every(2))
                .tickFormat(x =>
                    x instanceof Date ? timeFormat("%b")(x).toUpperCase() : "",
                )
                .tickSizeInner(35)
                .tickSizeOuter(0),
        )
        .call(g =>
            g
                .selectAll("text")
                .attr("dx", 20)
                .attr("dy", 15),
        )
        .call(g => g.selectAll("line").style("shape-rendering", "crispedges"));

const xYearLabel = (g: Selection<SVGGElement, {}, HTMLElement, any>) =>
    g
        .attr("transform", `translate(0,${margin.top - 8})`)
        .call(
            axisTop(xScale)
                .ticks(timeMonth.every(12))
                // @ts-ignore
                .tickFormat(timeFormat("%Y"))
                .tickSize(0),
        )
        .call(g =>
            g
                .selectAll("text")
                .attr("dx", 21)
                .attr("dy", -8),
        );

const xMinorTicks = (g: Selection<SVGGElement, {}, HTMLElement, any>) =>
    g
        .attr("transform", `translate(0,${margin.top - 8})`)
        .call(
            axisTop(xScale)
                .ticks(timeMonth)
                // @ts-ignore
                .tickFormat("")
                .tickSizeInner(20)
                .tickSizeOuter(0),
        )
        .call(g =>
            g
                .selectAll("text")
                .attr("dx", 20)
                .attr("dy", 30),
        )
        .call(g => g.selectAll("line").style("shape-rendering", "crispedges"));

// create tool tip container
const toolTip = select(".SVGContainer")
    .append("div")
    .attr("class", "RoadMapChart-toolTip")
    .style("height", 70)
    .style("opacity", 0);

// creat info box container
const infoBox = select(".SVGContainer")
    .append("div")
    .attr("class", "RoadMapChart-infoBox")
    .style("opacity", 0);

select("body").on("click", () => {
    infoBox
        .style("opacity", 0)
        .style("top", 0)
        .style("left", 0);
});

// get element
const svg = select("#road-map");
svg.attr("width", width).attr("height", height);

// size the filter bar to the graph
const filterBar = document.querySelector(
    ".RoadMapFilterBar",
) as HTMLElement | null;
if (filterBar != null) {
    filterBar.style["width"] = `${width}px`;
}

// draw x-axis
const xAxisGroup = svg.append("g").attr("class", "RoadMapChart-axis");

xAxisGroup.append("g").call(xMinorTicks);

xAxisGroup.append("g").call(xAxis);

xAxisGroup.append("g").call(xYearLabel);

const projectGroup = svg.append("g").attr("class", "RoadMapChart-project");

// draw project name
projectGroup
    .append("g")
    .selectAll("text")
    .data(data)
    .enter()
    .append("text")
    .attr("class", "rmName")
    .text(d => {
        const textWidth =
            Math.abs(xScale(d.endDate) - xScale(d.startDate)) -
            boxMargin.left -
            boxMargin.right;
        const textLength = textWidth / 10;
        return textLength < d.name.length
            ? `${d.name.slice(0, textLength)}...`
            : d.name;
    })
    .attr("x", d => xScale(d.startDate))
    .attr("dx", boxMargin.left)
    .attr("y", d => yScale(d.name) || 0)
    .attr("dy", 30)
    .style("font-family", "sans-serif")
    .style("font-size", 16)
    .style("font-weight", 500)
    .style("fill", "#333538");

// draw project details
projectGroup
    .append("g")
    .selectAll("text")
    .data(data)
    .enter()
    .append("text")
    .text(d => {
        const textWidth =
            Math.abs(xScale(d.endDate) - xScale(d.startDate)) -
            boxMargin.left -
            boxMargin.right;
        const textLength = textWidth / 7;
        const text = `${d.projectState}  ${timeFormat("%m/%d/%Y")(
            d.displayStartDate,
        )} - ${timeFormat("%m/%d/%Y")(d.displayEndDate)}`;
        return textLength < text.length
            ? `${text.slice(0, textLength)}...`
            : text;
    })
    .attr("xml:space", "preserve")
    .attr("x", d => xScale(d.startDate))
    .attr("dx", boxMargin.left)
    .attr("y", d => yScale(d.name) || 0)
    .attr("dy", 50)
    .style("font-family", "sans-serif")
    .style("font-size", 14)
    .style("fill", "#1A1A1A");

// draw rectangles
projectGroup
    .append("g")
    .selectAll("rect")
    .data(data)
    .enter()
    .append("rect")
    .style("stroke", "#BFBFBF")
    .style("stroke-width", 0.5)
    .style("fill", "transparent")
    .attr("rx", 5)
    .attr("ry", 5)
    .attr("x", d => xScale(d.startDate))
    .attr("width", d => Math.abs(xScale(d.endDate) - xScale(d.startDate)))
    .attr("y", d => yScale(d.name) || 0)
    .attr("height", rectHeight)
    .on("click", (d, i, elems) => {
        event.stopPropagation();
        const elem = elems[i];
        const elemRect = elem.getBoundingClientRect();
        const left = Math.max(event.pageX + 16, 0);
        const top = Math.max(window.pageYOffset + elemRect.top - 25, 0);
        infoBox
            .html(
                `
                <h1 class="RoadMapChart-infoBox-title">${d.name}</h1>
                <div class="RoadMapChart-infoBox-section">
                    <h4 class="RoadMapChart-infoBox-label">Start Date</h4>
                    <p class="RoadMapChart-infoBox-value">${timeFormat(
                        "%m/%d/%Y",
                    )(d.displayStartDate)}</p>
                </div>
                <div class="RoadMapChart-infoBox-section">
                    <h4 class="RoadMapChart-infoBox-label">End Date</h4>
                    <p class="RoadMapChart-infoBox-value">${timeFormat(
                        "%m/%d/%Y",
                    )(d.displayEndDate)}</p>
                </div>
                ${
                    d.executiveOwners
                        ? `<div class="RoadMapChart-infoBox-section">
                        <h4 class="RoadMapChart-infoBox-label">Executive Owner</h4>
                        <p class="RoadMapChart-infoBox-value">${
                            d.executiveOwners
                        }</p>
                    </div>`
                        : ""
                }
                ${
                    d.brms
                        ? `<div class="RoadMapChart-infoBox-section">
                        <h4 class="RoadMapChart-infoBox-label"> IT&T Business Relationship Manager</h4>
                        <p class="RoadMapChart-infoBox-value">${d.brms}</p>
                    </div>`
                        : ""
                }
                ${
                    d.projectManager
                        ? `<div class="RoadMapChart-infoBox-section">
                        <h4 class="RoadMapChart-infoBox-label"> Project Manager </h4>
                        <p class="RoadMapChart-infoBox-value">${
                            d.projectManager
                        }</p>
                    </div>`
                        : ""
                }
                <a class="Button Button--secondary" href="${
                    d.url
                }">View Project Details</a>
            `,
            )
            .style("left", `${left}px`)
            .style("top", `${top}px`)
            .style("opacity", 1);
    });

// draw progress bar
const progressGroup = projectGroup
    .append("g")
    .selectAll("rect")
    .data(data)
    .enter();

progressGroup
    .append("rect")
    .style("fill", "#E9E9E9")
    .attr("rx", 5)
    .attr("ry", 5)
    .attr("x", d => xScale(d.startDate) + boxMargin.left)
    .attr(
        "width",
        d =>
            xScale(d.endDate) -
            xScale(d.startDate) -
            boxMargin.left -
            boxMargin.right,
    )
    .attr("y", d => (yScale(d.name) || 0) + boxMargin.top)
    .attr("height", 5);

progressGroup
    .append("rect")
    .style("fill", d => healthColors(d.health))
    .attr("rx", 5)
    .attr("ry", 5)
    .attr("x", d => xScale(d.startDate) + boxMargin.left)
    .attr(
        "width",
        d =>
            (xScale(d.endDate) -
                xScale(d.startDate) -
                boxMargin.left -
                boxMargin.right) *
            d.complete,
    )
    .attr("y", d => (yScale(d.name) || 0) + boxMargin.top)
    .attr("height", 5)
    .on("mouseover", d => {
        toolTip
            .html(`${Math.round(d.complete * 100)}%`)
            .style("left", `${event.pageX}px`)
            .style("top", `${event.pageY - 55}px`)
            .style("transform", "translate(-50%, 0)")
            .style("opacity", 1);
    })
    .on("mouseout", () => {
        toolTip
            .style("opacity", 0)
            .style("left", "0px")
            .style("top", "0px");
    });

// draw today line
const today = new Date();

if (today <= xMax) {
    const todayGroup = svg.append("g");

    todayGroup
        .append("text")
        .text("Today")
        .attr("x", xScale(today))
        .attr("y", 18)
        .attr("text-anchor", "middle")
        .style("font-family", "sans-serif")
        .style("font-size", 16)
        .style("font-weight", 600)
        .style("fill", "#317FAF");

    todayGroup
        .append("circle")
        .attr("r", 4)
        .attr("cx", xScale(today))
        .attr("cy", 35)
        .attr("fill", "#317FAF");

    todayGroup
        .append("line")
        .attr("x1", xScale(today))
        .attr("y1", 45)
        .attr("x2", xScale(today))
        .attr("y2", height - margin.bottom)
        .attr("stroke", "#317FAF")
        .attr("stroke-dasharray", 2);
}
