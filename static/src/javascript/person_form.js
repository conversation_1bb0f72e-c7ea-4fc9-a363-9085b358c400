import MicroModal from "micromodal";
import jQuery from "jquery";
import "select2";
import "select2/dist/css/select2.css";

MicroModal.init();



jQuery("select[multiple]").select2();
const formatAction = data => {
    // Return immediately if there is no element attached
    if (data.element == null) {
        return null;
    }

    // Just return the empty label text when there is no value
    if (data.element.value == "") {
        return data.text;
    }

    const imageUrl = `/files/dist/images/icons/icon-action-${data.element.value.replace(
        " ",
        "_",
    )}.svg`;
    const template = `<span><image src="${imageUrl}" alt="${
        data.element.value
    }" />&nbsp;&nbsp;${data.text}</span>`;
    return jQuery < HTMLElement > template;
};
