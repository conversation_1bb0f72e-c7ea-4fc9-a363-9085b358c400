# Generated by Django 5.2.4 on 2025-10-28 00:03

import django.db.models.manager
import django_lifecycle.mixins
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Action",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("CREATED_IDEA", "New Idea"),
                            ("CREATED_PROJECT", "Create/New Project"),
                            ("ASSIGNED_ROLE", "Adding People"),
                            ("ADDED_COMMENT", "Comment Added"),
                            (
                                "CHANGED_TECHNICAL_COMPONENT",
                                "Technical Component Changed",
                            ),
                            (
                                "NEEDS_CORPORATE_COMMUNICATION",
                                "Corporate Communication Needs",
                            ),
                            ("CHANGED_STATE", "State Change"),
                            ("CHANGED_PHASE", "Phase Change"),
                            ("CHANGED_HEALTH", "Health Change"),
                            ("ADDED_PROGRAM", "Added to a Program"),
                            ("ADDED_EXECUTIVE_ACTION", "Executive Action Added"),
                            (
                                "ADDED_RECENT_ACCOMPLISHMENT",
                                "Recent Accomplishment Added",
                            ),
                            ("PURCHASE_ADDED", "Added a Product"),
                            ("PURCHASE_RESUBMITTED", "Resubmitted a Product"),
                            (
                                "PURCHASE_EVALUATED",
                                "Product Evaluated by Segment Legal",
                            ),
                            (
                                "PURCHASE_REEVALUATE",
                                "Product Marked 'Resubmit' by Segment Legal",
                            ),
                            ("PURCHASE_SUBMITTED", "Product Submitted to Corp Legal"),
                            ("PURCHASE_REJECTED", "Product Rejected by Corp Legal"),
                            (
                                "PURCHASE_REVISE",
                                "Product Marked 'Revise' by Corp Legal",
                            ),
                            ("PURCHASE_ACCEPTED", "Product Accepted by Corp Legal"),
                            ("TOLLGATE_REVIEW", "Tollgate Review"),
                            ("PROJECT_DELAY", "Project Delay"),
                            ("KEY_MEETING", "Key Meeting"),
                            ("KEY_DECISION", "Key Decision"),
                            ("SCHEDULE_REVISION", "Schedule Revision"),
                            ("BUDGET_REVISION", "Budget Revision"),
                            ("MAJOR_ACCOMPLISHMENT", "Major Accomplishment"),
                            ("OTHER", "Other"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                ("description", models.TextField(blank=True, max_length=1000)),
                ("notes", models.TextField(blank=True, max_length=2000)),
                ("is_key", models.BooleanField(db_index=True, default=False)),
                ("occurrence_date", models.DateField(db_index=True)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
                ("manually_created", models.BooleanField(blank=True, default=False)),
                ("active", models.BooleanField(blank=True, default=True)),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
    ]
