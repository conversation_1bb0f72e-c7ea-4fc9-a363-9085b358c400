# Generated by Django 5.2.4 on 2025-10-28 00:03

import auto_prefetch
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("actions", "0001_initial"),
        ("projects", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="action",
            name="editor",
            field=auto_prefetch.ForeignKey(
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="editor",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="action",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddIndex(
            model_name="action",
            index=models.Index(
                fields=["project", "action_type"], name="actions_act_project_94d310_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="action",
            index=models.Index(
                fields=["project", "occurrence_date"],
                name="actions_act_project_3762c1_idx",
            ),
        ),
    ]
