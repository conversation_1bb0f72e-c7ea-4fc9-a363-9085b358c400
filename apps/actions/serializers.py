from rest_framework import serializers
from rest_framework_csv.renderers import CSVRenderer

from .models import Action


class ProjectHistoryExportCSVRenderer(CSVRenderer):
    header = [
        "id",
        "date",
        "activity_type",
        "user",
        "short_description",
        "is_key",
        "notes",
    ]
    labels = {
        "id": "Project ID",
        "date": "Date",
        "activity_type": "Activity Type",
        "user": "User",
        "short_description": "Activity Details",
        "is_key": "Key Activity?",
        "notes": "Notes",
    }


class ProjectHistoryExportSerializer(serializers.ModelSerializer):
    id = serializers.SerializerMethodField()
    date = serializers.DateTimeField(source="modified", format="%m/%d/%Y")
    is_key = serializers.SerializerMethodField()
    user = serializers.SerializerMethodField()
    activity_type = serializers.SerializerMethodField()

    class Meta:
        model = Action
        fields = [
            "id",
            "date",
            "activity_type",
            "user",
            "short_description",
            "is_key",
            "notes",
        ]

    def get_id(self, obj: Action) -> str:
        return obj.project.id

    def get_is_key(self, obj: Action) -> str:
        if obj.is_key:
            return "Yes"
        return "No"

    def get_user(self, obj: Action) -> str:
        return obj.editor.full_name()

    def get_activity_type(self, obj: Action) -> str:
        return obj.action_type_display
