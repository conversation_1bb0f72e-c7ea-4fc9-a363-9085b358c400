from __future__ import annotations
from datetime import date
from typing import Optional
import auto_prefetch


from django.contrib.auth import get_user_model
from django.db import models
from django_lifecycle import LifecycleModel, hook, BEFORE_SAVE
from apps.projects.models import Project

User = get_user_model()


def get_action_description(project: Project, action_type: ActionType) -> str:
    """
    Generate a description for a given action type on a project.

    Args:
        project (Project): The project associated with the action.
        action_type (ActionType): The type of action performed.

    Returns:
        str: A description of the action.

    Raises:
        LookupError: If the action_type is not recognized.
    """
    if action_type == ActionType.CREATED_PROJECT:
        return f"Project {project.name} was created by {project.created_by}"
    elif action_type == ActionType.ASSIGNED_ROLE:
        return "Person was assigned role"
    elif action_type == ActionType.ADDED_COMMENT:
        return "Comment was added"
    elif action_type == ActionType.CHANGED_TECHNICAL_COMPONENT:
        result = "Yes" if project.has_technology_components else "No"
        return f"Technical component was marked as {result}"
    elif action_type == ActionType.NEEDS_CORPORATE_COMMUNICATION:
        need = "needed" if project.needs_corporate_communication() else "not needed"
        return f"Corporate Communication Need was marked as {need}"
    elif action_type == ActionType.CHANGED_STATE:
        return f"The project state changed to {project.project_state_display}"
    elif action_type == ActionType.CHANGED_PHASE:
        return f"The project transitioned to {project.phase_display}"
    elif action_type == ActionType.CHANGED_HEALTH:
        return f"Project health changed to {project.current_health.health_display}"
    elif action_type == ActionType.ADDED_PROGRAM:
        return f"Project added to {project.program.name}"
    elif action_type == ActionType.ADDED_EXECUTIVE_ACTION:
        return "Executive action added"
    elif action_type == ActionType.ADDED_RECENT_ACCOMPLISHMENT:
        return "Recent accomplishment added."
    else:
        return f"Unspecified action: {action_type}"


def record(
    project: Project,
    action_type: ActionType,
    editor: User,
    description: Optional[str] = None,
) -> Action:
    """
    Create a historic action record for the given project.

    Args:
        project (Project): The project associated with the action.
        action_type (ActionType): The type of action performed.
        editor (User): The user who performed the action.
        description (Optional[str], optional): A custom description for the action.
            If None, a description will be generated. Defaults to None.

    Returns:
        Action: The created Action instance.
    """
    if description is None:
        description = get_action_description(project=project, action_type=action_type)
    action = Action.objects.create(
        project=project, action_type=action_type, editor=editor, description=description
    )
    return action


class ActionType(models.TextChoices):
    """Enumeration of possible action types for projects."""

    CREATED_IDEA = "CREATED_IDEA", "New Idea"
    CREATED_PROJECT = "CREATED_PROJECT", "Create/New Project"
    ASSIGNED_ROLE = "ASSIGNED_ROLE", "Adding People"
    ADDED_COMMENT = "ADDED_COMMENT", "Comment Added"
    CHANGED_TECHNICAL_COMPONENT = (
        "CHANGED_TECHNICAL_COMPONENT",
        "Technical Component Changed",
    )
    NEEDS_CORPORATE_COMMUNICATION = (
        "NEEDS_CORPORATE_COMMUNICATION",
        "Corporate Communication Needs",
    )
    CHANGED_STATE = "CHANGED_STATE", "State Change"
    CHANGED_PHASE = "CHANGED_PHASE", "Phase Change"
    CHANGED_HEALTH = "CHANGED_HEALTH", "Health Change"
    ADDED_PROGRAM = "ADDED_PROGRAM", "Added to a Program"
    ADDED_EXECUTIVE_ACTION = "ADDED_EXECUTIVE_ACTION", "Executive Action Added"
    ADDED_RECENT_ACCOMPLISHMENT = (
        "ADDED_RECENT_ACCOMPLISHMENT",
        "Recent Accomplishment Added",
    )
    PURCHASE_ADDED = "PURCHASE_ADDED", "Added a Product"
    PURCHASE_RESUBMITTED = "PURCHASE_RESUBMITTED", "Resubmitted a Product"
    PURCHASE_EVALUATED = "PURCHASE_EVALUATED", "Product Evaluated by Segment Legal"
    PURCHASE_REEVALUATE = (
        "PURCHASE_REEVALUATE",
        "Product Marked 'Resubmit' by Segment Legal",
    )
    PURCHASE_SUBMITTED = "PURCHASE_SUBMITTED", "Product Submitted to Corp Legal"
    PURCHASE_REJECTED = "PURCHASE_REJECTED", "Product Rejected by Corp Legal"
    PURCHASE_REVISE = "PURCHASE_REVISE", "Product Marked 'Revise' by Corp Legal"
    PURCHASE_ACCEPTED = "PURCHASE_ACCEPTED", "Product Accepted by Corp Legal"
    TOLLGATE_REVIEW = "TOLLGATE_REVIEW", "Tollgate Review"
    PROJECT_DELAY = "PROJECT_DELAY", "Project Delay"
    KEY_MEETING = "KEY_MEETING", "Key Meeting"
    KEY_DECISION = "KEY_DECISION", "Key Decision"
    SCHEDULE_REVISION = "SCHEDULE_REVISION", "Schedule Revision"
    BUDGET_REVISION = "BUDGET_REVISION", "Budget Revision"
    MAJOR_ACCOMPLISHMENT = "MAJOR_ACCOMPLISHMENT", "Major Accomplishment"
    OTHER = "OTHER", "Other"


class Action(auto_prefetch.Model, LifecycleModel):
    """
    Represents an action taken on a project.

    This model stores information about various actions that can occur within a project,
    such as comments, accomplishments, decisions, and other activities.
    """

    project: auto_prefetch.ForeignKey = auto_prefetch.ForeignKey(
        Project, on_delete=models.CASCADE
    )
    action_type: models.CharField = models.CharField(
        max_length=50, choices=ActionType.choices, db_index=True
    )
    editor: auto_prefetch.ForeignKey = auto_prefetch.ForeignKey(
        "users.User",
        null=True,
        editable=False,
        on_delete=models.SET_NULL,
        related_name="editor",
    )
    description: models.TextField = models.TextField(max_length=1000, blank=True)
    notes: models.TextField = models.TextField(max_length=2000, blank=True)
    is_key: models.BooleanField = models.BooleanField(default=False, db_index=True)
    occurrence_date: models.DateField = models.DateField(db_index=True)
    created: models.DateTimeField = models.DateTimeField(
        auto_now_add=True, editable=False
    )
    modified: models.DateTimeField = models.DateTimeField(auto_now=True, editable=False)
    manually_created: models.BooleanField = models.BooleanField(
        default=False, blank=True
    )
    active: models.BooleanField = models.BooleanField(default=True, blank=True)

    def __str__(self) -> str:
        """
        Returns a string representation of the Action.

        Returns:
            str: The description of the action.
        """
        return self.description

    @property
    def action_type_display(self) -> str:
        """
        Returns the human-readable label for the action type.

        Returns:
            str: The display value of the action type.
        """
        return self.get_action_type_display()

    @property
    def short_description(self) -> str:
        """
        Generates a short description of the action based on its type.

        Returns:
            str: A brief description of the action.
        """
        name = self.editor.full_name

        action_type_descriptions = {
            ActionType.ADDED_COMMENT: f"Comment was added/modified by {name}",
            ActionType.ADDED_RECENT_ACCOMPLISHMENT: f"Recent Accomplishment was added/modified by {name}",
            ActionType.ADDED_EXECUTIVE_ACTION: f"Executive Action was added/modified by {name}",
            ActionType.PROJECT_DELAY: f"Project Delay was added/modified by {name}",
            ActionType.KEY_MEETING: f"Key Meeting was added/modified by {name}",
            ActionType.KEY_DECISION: f"Key Decision was added/modified by {name}",
            ActionType.SCHEDULE_REVISION: f"Schedule Revision was added/modified by {name}",
            ActionType.BUDGET_REVISION: f"Budget Revision was added/modified by {name}",
            ActionType.MAJOR_ACCOMPLISHMENT: f"Major Accomplishment was added/modified by {name}",
            ActionType.OTHER: f"Other activity was added/modified by {name}",
        }

        return action_type_descriptions.get(self.action_type, self.description)

    @hook(BEFORE_SAVE)
    def set_occurrence_date(self) -> None:
        """
        Sets the occurrence date to today if it's not already set.
        """
        if self.occurrence_date is None:
            self.occurrence_date = date.today()

    class Meta(auto_prefetch.Model.Meta):
        indexes = [
            models.Index(fields=["project", "action_type"]),
            models.Index(fields=["project", "occurrence_date"]),
        ]
