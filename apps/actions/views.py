from typing import Any, Dict, Iterable

from django.http import HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.utils.functional import cached_property
from django.views.generic import CreateView, DetailView, UpdateView
from django.views.generic.edit import DeleteView
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from apps.projects.models import Project
from apps.utils.mixins import (
    LoginRequiredMixin,
    ProjectDeactivatedMixin,
)
from apps.organizations.mixins import RolePermissionMixin

from .filters import ProjectHistoryFilter
from .forms import ActionFilterForm, ActionForm, ActionUpdateForm
from .models import Action
from .serializers import ProjectHistoryExportCSVRenderer, ProjectHistoryExportSerializer
from .utils import group_actions_by_date


class ProjectActionListExportView(RetrieveAPIView):
    model = Project
    serializer_class = ProjectHistoryExportSerializer
    pagination_class = None
    renderer_classes = [ProjectHistoryExportCSVRenderer]
    permission_classes = [IsAuthenticated]
    queryset = Project.objects.all()

    def get_actions(self, instance):
        action_queryset = (
            Action.objects.select_related("editor")
            .filter(project=instance, active=True)
            .order_by("-occurrence_date", "action_type")
        )
        action_queryset = ProjectHistoryFilter(
            self.request.GET, queryset=action_queryset
        )
        action_queryset = action_queryset.qs.distinct()
        return action_queryset

    def finalize_response(self, *args, **kwargs) -> Response:
        response = super().finalize_response(*args, **kwargs)
        response["Content-Disposition"] = "attachment; filename=project_history.csv"
        return response

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        actions = self.get_actions(instance)
        serializer = ProjectHistoryExportSerializer(actions, many=True)
        return Response(serializer.data)


class ProjectActionList(
    LoginRequiredMixin, RolePermissionMixin, ProjectDeactivatedMixin, DetailView
):
    model = Project
    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    model = Project
    template_name = "actions/history.html"

    def get_queryset(self):
        user = self.request.user
        queryset = super().get_queryset()
        return queryset.with_starred_by_user(user)

    @cached_property
    def project(self) -> Project:
        return self.get_object()

    @cached_property
    def project_actions(self) -> Iterable[Action]:
        queryset = (
            Action.objects.filter(project=self.project)
            .filter(active=True)
            .order_by("-occurrence_date", "-created")
        )
        queryset = ProjectHistoryFilter(self.request.GET, queryset=queryset).qs
        return queryset.distinct()

    def get_context_data(self, *args, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["history"] = group_actions_by_date(self.project_actions)
        context["action_filter_form"] = ActionFilterForm(
            self.request.GET, project=self.object
        )
        context["querystring"] = self.request.GET.urlencode()
        return context


class ProjectActionDetailView(
    LoginRequiredMixin, RolePermissionMixin, ProjectDeactivatedMixin, DetailView
):
    # Note: This works with Action objects but checks Project permissions
    # We need to override get_model to return Project for permission checking
    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"

    def get_model(self):
        # Override to check Project permissions instead of Action
        from apps.projects.models import Project

        return Project

    model = Action
    template_name = "actions/history.html"

    @cached_property
    def project(self) -> Project:
        user = self.request.user
        return get_object_or_404(
            Project.objects.with_starred_by_user(user), pk=self.kwargs["project_pk"]
        )

    @cached_property
    def project_actions(self) -> Iterable[Action]:
        queryset = (
            Action.objects.filter(project=self.project)
            .filter(active=True)
            .order_by("-occurrence_date", "-created")
        )
        queryset = ProjectHistoryFilter(self.request.GET, queryset=queryset).qs
        return queryset.distinct()

    def get_context_data(self, *args, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        context["history"] = group_actions_by_date(self.project_actions)
        context["action_filter_form"] = ActionFilterForm(
            self.request.GET, project=self.project
        )
        context["action_instance"] = self.object
        context["querystring"] = self.request.GET.urlencode()
        context["has_edit_delete_access"] = self.project.user_has_modify_access(
            self.request.user
        )
        return context


class ProjectActionCreateView(
    LoginRequiredMixin, RolePermissionMixin, ProjectDeactivatedMixin, CreateView
):
    # Note: This creates Action objects but checks Project permissions
    # We need to override get_model to return Project for permission checking
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"

    def get_model(self):
        # Override to check Project permissions instead of Action
        from apps.projects.models import Project

        return Project

    model = Action
    form_class = ActionForm
    template_name = "actions/history.html"

    @cached_property
    def project(self) -> Project:
        user = self.request.user
        return get_object_or_404(
            Project.objects.with_starred_by_user(user), pk=self.kwargs["project_pk"]
        )

    @cached_property
    def project_actions(self) -> Iterable[Action]:
        queryset = (
            Action.objects.filter(project=self.project)
            .filter(active=True)
            .order_by("-occurrence_date", "-created")
        )
        queryset = ProjectHistoryFilter(self.request.GET, queryset=queryset).qs
        return queryset.distinct()

    def get_context_data(self, *args, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        context["history"] = group_actions_by_date(self.project_actions)
        context["action_filter_form"] = ActionFilterForm(
            self.request.GET, project=self.project
        )
        context["create_action_form"] = context.pop("form")
        context["querystring"] = self.request.GET.urlencode()
        return context

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["manually_created"] = True
        kwargs["editor"] = self.request.user
        if "data" in kwargs.keys():
            data = kwargs["data"].copy()
            data.update({"project": self.project.id})
            kwargs["data"] = data
        return kwargs

    def get_success_url(self):
        return reverse(
            "project_history_action_detail",
            kwargs={"project_pk": self.object.project.id, "pk": self.object.id},
        )


class ProjectActionUpdateView(
    LoginRequiredMixin, RolePermissionMixin, ProjectDeactivatedMixin, UpdateView
):
    # Note: This updates Action objects but checks Project permissions
    # We need to override get_model to return Project for permission checking
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"

    def get_model(self):
        # Override to check Project permissions instead of Action
        from apps.projects.models import Project

        return Project

    model = Action
    form_class = ActionUpdateForm
    template_name = "actions/history.html"

    @cached_property
    def project(self) -> Project:
        user = self.request.user
        return get_object_or_404(
            Project.objects.with_starred_by_user(user), pk=self.kwargs["project_pk"]
        )

    @cached_property
    def project_actions(self) -> Iterable[Action]:
        queryset = (
            Action.objects.filter(project=self.project)
            .filter(active=True)
            .order_by("-occurrence_date", "-created")
        )
        queryset = ProjectHistoryFilter(self.request.GET, queryset=queryset).qs
        return queryset.distinct()

    def get_context_data(self, *args, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        context["history"] = group_actions_by_date(self.project_actions)
        context["action_filter_form"] = ActionFilterForm(
            self.request.GET, project=self.project
        )
        context["update_action_form"] = context.pop("form")
        context["querystring"] = self.request.GET.urlencode()
        return context

    def get_success_url(self):
        return reverse(
            "project_history_action_detail",
            kwargs={"project_pk": self.object.project.id, "pk": self.object.id},
        )


class ProjectActionDeactivateView(
    LoginRequiredMixin, RolePermissionMixin, ProjectDeactivatedMixin, DeleteView
):
    # Note: This deletes Action objects but checks Project permissions
    # We need to override get_model to return Project for permission checking
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"

    def get_model(self):
        # Override to check Project permissions instead of Action
        from apps.projects.models import Project

        return Project

    model = Action

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(Project, pk=self.kwargs["project_pk"])

    def get_success_url(self) -> str:
        return reverse("project_history", kwargs={"pk": self.project.pk})

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        success_url = self.get_success_url()
        self.object.active = False
        self.object.save()
        return HttpResponseRedirect(success_url)
