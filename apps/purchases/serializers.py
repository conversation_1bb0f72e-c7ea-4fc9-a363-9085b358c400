from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from rest_framework import serializers

from apps.attachments.serializers import AttachmentSerializer
from apps.comments.serializers import CommentSerializer
from apps.links.serializers import LinkSerializer

from .models import Purchase


class PurchaseSerializer(serializers.ModelSerializer):
    content_type_id = serializers.SerializerMethodField()
    created_by = serializers.SerializerMethodField()
    state = serializers.SerializerMethodField()
    validated = serializers.SerializerMethodField()
    approved = serializers.SerializerMethodField()
    drafted = serializers.SerializerMethodField()
    editable = serializers.SerializerMethodField()
    rejected = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()
    urls = serializers.SerializerMethodField()
    attachments = AttachmentSerializer(many=True)
    links = LinkSerializer(many=True)
    comments = serializers.SerializerMethodField()

    class Meta:
        model = Purchase
        fields = [
            "id",
            "name",
            "description",
            "vendor_info",
            "created_by",
            "state",
            "validated",
            "approved",
            "drafted",
            "editable",
            "rejected",
            "validation_note",
            "display_approval",
            "display_validation",
            "requires_revision",
            "requires_reevaluation",
            "review_note",
            "required_documents",
            "other_documents",
            "content_type_id",
            "permissions",
            "urls",
            "attachments",
            "links",
            "comments",
        ]

    def get_content_type_id(self, instance):
        return ContentType.objects.get_for_model(Purchase).pk

    def get_created_by(self, instance):
        return instance.creator.full_name

    def get_state(self, instance):
        return instance.state.value

    def get_validated(self, instance):
        return instance.is_validated

    def get_editable(self, instance):
        return instance.is_editable

    def get_rejected(self, instance):
        return instance.is_rejected

    def get_drafted(self, instance):
        return instance.is_drafted

    def get_approved(self, instance):
        return instance.is_approved

    def get_comments(self, instance):
        return CommentSerializer(
            instance.comments.all().order_by("-created"), many=True
        ).data

    def get_permissions(self, instance):
        user = self.context["request"].user
        return {
            "edit": instance.user_has_edit_delete_access(user),
            "validate": instance.user_can_validate(user),
            "approve": instance.user_can_approve(user),
            "submit": instance.user_can_submit(user),
        }

    def get_urls(self, instance):
        return {
            "update": reverse(
                "project_legal_purchase_update", args=[instance.project.pk, instance.pk]
            ),
            "delete": reverse(
                "project_legal_purchase_delete", args=[instance.project.pk, instance.pk]
            ),
            "submitToLegal": reverse(
                "project_legal_purchase_submit", args=[instance.project.pk, instance.pk]
            ),
            "repropose": reverse(
                "project_legal_purchase_repropose",
                args=[instance.project.pk, instance.pk],
            ),
            "validate": reverse(
                "project_legal_purchase_validate",
                args=[instance.project.pk, instance.pk],
            ),
            "review": reverse(
                "project_legal_purchase_approve",
                args=[instance.project.pk, instance.pk],
            ),
        }
