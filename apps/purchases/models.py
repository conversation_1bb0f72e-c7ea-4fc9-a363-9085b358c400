import operator
import re
from functools import reduce
from typing import cast

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import GenericRelation
from django.db import models
from django.db.models import Case, Exists, OuterRef, Q, Value, When
from django.db.models.query import QuerySet
from django.urls import reverse

from apps.attachments.models import Attachment
from apps.comments.models import Comment
from apps.links.models import Link
from apps.projects.models import Project, StarredProject
from apps.utils.models import EnumField
import auto_prefetch

User = get_user_model()


class DocumentTypes(models.TextChoices):
    MSA = "MSA", "MSA (Master Services Agreement)"
    NDA = "NDA", "NDA (Non-Disclosure Agreement)"
    SOW = "SOW", "SOW (Statement of Work)"


class LegalDecisionChoices(models.TextChoices):
    APPROVED = "APPROVED", "Approved"
    REJECTED = "REJECTED", "Rejected"
    NEEDS_REVISION = "NEEDS_REVISION", "Needs Revision"


class PurchaseState(models.TextChoices):
    PENDING = "PENDING", "Pending"
    VALIDATED = "VALIDATED", "Validated"
    DRAFTED = "DRAFTED", "Drafted"
    APPROVED = "APPROVED", "Approved"
    REJECTED = "REJECTED", "Rejected"


class ActionRequired(models.TextChoices):
    NONE = "NONE", "None"
    REEVALUATE = "REEVALUATE", "Reevaluate"
    RESUBMIT = "RESUBMIT", "Resubmit"


class PurchaseQuerySet(QuerySet):
    def search(self, query: str):
        quoted_re = re.compile('"(.*?)"')
        keywords = re.findall(quoted_re, query)
        keywords += re.sub(quoted_re, "", query).split()
        params = []
        fields = ("name", "description", "vendor_info", "project__name")
        for keyword in keywords:
            for field in fields:
                params.append(Q(**{field + "__icontains": keyword}))
        queryset = self.filter(reduce(operator.or_, params))
        return cast(PurchaseQuerySet, queryset)

    def with_starred_by_user(self, user: User):
        queryset = self.annotate(
            starred=Exists(
                StarredProject.objects.filter(
                    project=OuterRef("project__pk"), starred_by=user
                )
            )
        )
        return cast(PurchaseQuerySet, queryset)

    def with_legal_review_status(self):
        whens = [
            When(state=PurchaseState.APPROVED, then=Value("Approved")),
            When(state=PurchaseState.DRAFTED, then=Value("Submitted to Legal")),
            When(state=PurchaseState.VALIDATED, then=Value("Initial Review")),
        ]
        legal_review_status = Case(
            *whens, default=Value("Submitted"), output_field=models.CharField()
        )
        return self.annotate(legal_review=legal_review_status)


class PurchaseManager(auto_prefetch.Manager):
    def get_queryset(self) -> PurchaseQuerySet:
        return PurchaseQuerySet(self.model, using=self._db)

    def search(self, query: str) -> PurchaseQuerySet:
        return self.get_queryset().search(query)

    def with_starred_by_user(self, user: User) -> PurchaseQuerySet:
        return self.get_queryset().with_starred_by_user(user)

    def with_legal_review_status(self):
        return self.get_queryset().with_legal_review_status()


class Purchase(auto_prefetch.Model):
    project = auto_prefetch.ForeignKey(
        Project, on_delete=models.CASCADE, related_name="products"
    )
    name = models.CharField(max_length=250)
    description = models.TextField(max_length=1000)
    vendor_info = models.TextField(max_length=1000)

    state = EnumField(max_length=50, enum=PurchaseState, default=PurchaseState.PENDING)
    action_required = EnumField(
        max_length=50, enum=ActionRequired, default=ActionRequired.NONE
    )

    validation_note = models.TextField(max_length=1000, blank=True)
    required_documents = models.CharField(max_length=250, null=True, blank=True)
    other_documents = models.TextField(max_length=1000, blank=True)
    legal_decision = EnumField(max_length=50, enum=LegalDecisionChoices, null=True)
    review_note = models.TextField(max_length=1000, blank=True)

    attachments = GenericRelation(Attachment)
    comments = GenericRelation(Comment)
    links = GenericRelation(Link)

    # Metadata
    creator = models.ForeignKey("users.User", on_delete=models.PROTECT)
    created = models.DateTimeField(auto_now_add=True, editable=False)
    modified = models.DateTimeField(auto_now=True, editable=False)

    objects = PurchaseManager()

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse("project_legal", kwargs={"project_pk": self.project.pk})

    def required_documents_array(self):
        return self.required_documents.split(",") if self.required_documents else []

    @property
    def display_status(self) -> str:
        if self.is_approved:
            return "Approved"
        elif self.state is PurchaseState.DRAFTED:
            return "Submitted to Legal"
        elif self.state is PurchaseState.VALIDATED:
            return "Initial Review"
        return "Submitted"

    @property
    def display_approval(self) -> bool:
        return self.is_approved or self.review_note != ""

    @property
    def display_validation(self) -> bool:
        return self.is_validated or self.validation_note != ""

    @property
    def is_editable(self) -> bool:
        return self.state in [PurchaseState.PENDING, PurchaseState.VALIDATED]

    @property
    def is_approved(self) -> bool:
        return self.state is PurchaseState.APPROVED

    @property
    def is_rejected(self) -> bool:
        return self.state is PurchaseState.REJECTED

    @property
    def is_drafted(self) -> bool:
        return self.state in [PurchaseState.DRAFTED, PurchaseState.APPROVED]

    @property
    def is_validated(self) -> bool:
        return self.state in [
            PurchaseState.VALIDATED,
            PurchaseState.DRAFTED,
            PurchaseState.APPROVED,
        ]

    @property
    def requires_revision(self) -> bool:
        return self.action_required is ActionRequired.RESUBMIT

    @property
    def requires_reevaluation(self) -> bool:
        return self.action_required is ActionRequired.REEVALUATE

    def user_can_validate(self, user: User) -> bool:
        return (
            user.is_company_admin
            or User.objects.filter(
                Q(is_segment_legal_reviewer=True) | Q(is_corporate_legal_reviewer=True),
                email=user.email,
            ).exists()
        )

    def user_can_submit(self, user: User) -> bool:
        return (
            user.is_company_admin
            or User.objects.filter(
                Q(is_segment_legal_reviewer=True) | Q(is_corporate_legal_reviewer=True),
                email=user.email,
            ).exists()
        )

    def user_can_approve(self, user: User) -> bool:
        return (
            user.is_company_admin
            or User.objects.filter(
                email=user.email, is_corporate_legal_reviewer=True
            ).exists()
        )


class PendingPurchaseManager(auto_prefetch.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(state=PurchaseState.PENDING)


class PendingPurchase(Purchase):
    class Meta(auto_prefetch.Model.Meta):
        proxy = True

    objects = PendingPurchaseManager()

    def validate(self):
        self.state = PurchaseState.VALIDATED
        self.save()


class ValidatedPurchaseManager(auto_prefetch.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(state=PurchaseState.VALIDATED)


class ValidatedPurchase(Purchase):
    class Meta(auto_prefetch.Model.Meta):
        proxy = True

    objects = ValidatedPurchaseManager()

    def draft(self):
        self.state = PurchaseState.DRAFTED
        self.save()


class DraftedPurchaseManager(auto_prefetch.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(state=PurchaseState.DRAFTED)


class DraftedPurchase(Purchase):
    class Meta(auto_prefetch.Model.Meta):
        proxy = True

    objects = DraftedPurchaseManager()

    def approve(self):
        self.state = PurchaseState.APPROVED
        self.save()


class ApprovedPurchaseManager(auto_prefetch.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(state=PurchaseState.APPROVED)


class ApprovedPurchase(Purchase):
    class Meta(auto_prefetch.Model.Meta):
        proxy = True

    objects = ApprovedPurchaseManager()
