# Generated by Django 5.2.4 on 2025-10-28 00:03

import apps.purchases.models
import apps.utils.models
import django.db.models.manager
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Purchase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField(max_length=1000)),
                ("vendor_info", models.TextField(max_length=1000)),
                (
                    "state",
                    apps.utils.models.EnumField(
                        choices=[
                            ("PENDING", "PENDING"),
                            ("VALIDATED", "VALIDATED"),
                            ("DRAFTED", "DRAFTED"),
                            ("APPROVED", "APPROVED"),
                            ("REJECTED", "REJECTED"),
                        ],
                        default="PENDING",
                        enum=apps.purchases.models.PurchaseState,
                        max_length=50,
                    ),
                ),
                (
                    "action_required",
                    apps.utils.models.EnumField(
                        choices=[
                            ("NONE", "NONE"),
                            ("REEVALUATE", "REEVALUATE"),
                            ("RESUBMIT", "RESUBMIT"),
                        ],
                        default="NONE",
                        enum=apps.purchases.models.ActionRequired,
                        max_length=50,
                    ),
                ),
                ("validation_note", models.TextField(blank=True, max_length=1000)),
                (
                    "required_documents",
                    models.CharField(blank=True, max_length=250, null=True),
                ),
                ("other_documents", models.TextField(blank=True, max_length=1000)),
                (
                    "legal_decision",
                    apps.utils.models.EnumField(
                        choices=[
                            ("APPROVED", "APPROVED"),
                            ("REJECTED", "REJECTED"),
                            ("NEEDS_REVISION", "NEEDS_REVISION"),
                        ],
                        enum=apps.purchases.models.LegalDecisionChoices,
                        max_length=50,
                        null=True,
                    ),
                ),
                ("review_note", models.TextField(blank=True, max_length=1000)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
    ]
