# Generated by Django 5.2.4 on 2025-10-28 00:03

import auto_prefetch
import django.db.models.deletion
import django.db.models.manager
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("projects", "0001_initial"),
        ("purchases", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="purchase",
            name="creator",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="purchase",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="products",
                to="projects.project",
            ),
        ),
        migrations.CreateModel(
            name="ApprovedPurchase",
            fields=[],
            options={
                "abstract": False,
                "proxy": True,
                "base_manager_name": "prefetch_manager",
                "indexes": [],
                "constraints": [],
            },
            bases=("purchases.purchase",),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="DraftedPurchase",
            fields=[],
            options={
                "abstract": False,
                "proxy": True,
                "base_manager_name": "prefetch_manager",
                "indexes": [],
                "constraints": [],
            },
            bases=("purchases.purchase",),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="PendingPurchase",
            fields=[],
            options={
                "abstract": False,
                "proxy": True,
                "base_manager_name": "prefetch_manager",
                "indexes": [],
                "constraints": [],
            },
            bases=("purchases.purchase",),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ValidatedPurchase",
            fields=[],
            options={
                "abstract": False,
                "proxy": True,
                "base_manager_name": "prefetch_manager",
                "indexes": [],
                "constraints": [],
            },
            bases=("purchases.purchase",),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
    ]
