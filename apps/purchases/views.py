import json
from typing import Any, Dict, Union

from django.contrib import messages
from django.contrib.auth.mixins import UserPassesTestMixin
from django.db.models import QuerySet
from django.forms import modelformset_factory
from django.forms.models import ModelForm
from django.http.response import HttpResponse, HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.utils.functional import cached_property
from django.views.generic import CreateView, DeleteView, DetailView, UpdateView, View

from apps.actions.models import ActionType, record
from apps.notifications.models import MessageTypes, Priority, notify
from apps.projects.models import Project
from apps.utils.mixins import (
    LoginRequiredMixin,
    ProjectDeactivatedMixin,
)
from apps.organizations.mixins import RolePermissionMixin

from .forms import PurchaseApprovalForm, PurchaseForm, PurchaseValidationForm
from .models import ActionRequired, Purchase, PurchaseState
from .serializers import PurchaseSerializer


class ProjectLegalOverview(
    LoginRequiredMixin, RolePermissionMixin, ProjectDeactivatedMixin, DetailView
):
    model = Project
    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    queryset = Project.objects.filter(active=True)
    template_name = "projects/legal/overview.html"
    pk_url_kwarg = "project_pk"

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        queryset = queryset.with_starred_by_user(self.request.user)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # @@ Order purchases by status (proposed to approved)
        context["purchase_list"] = json.dumps(
            PurchaseSerializer(
                self.object.products.all(), many=True, context={"request": self.request}
            ).data
        )
        return context


class PurchaseEditAccessMixin(LoginRequiredMixin, UserPassesTestMixin):
    def test_func(self):
        purchase = self.get_object()
        return purchase.user_has_edit_delete_access(self.request.user)


class PurchaseMultiFormView(LoginRequiredMixin, RolePermissionMixin, DetailView):
    model = Project
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    queryset = Project.objects.filter(active=True)
    template_name = "projects/legal/purchase_multi_form.html"
    pk_url_kwarg = "project_pk"
    PurchaseFormSet = modelformset_factory(
        Purchase, form=PurchaseForm, can_delete=True, extra=0
    )
    formset = None

    @cached_property
    def project(self) -> Project:
        return self.get_object()

    def get_context_data(self, **kwargs):
        data = super().get_context_data(**kwargs)
        data["formset"] = self.formset or self.PurchaseFormSet(
            queryset=self.object.products.all(), form_kwargs={"user": self.request.user}
        )
        data["page_title"] = "Edit Products for Legal Review"
        return data

    def get_success_url(self):
        self.object = self.get_object()
        return reverse("project_legal", args=[self.object.pk])

    def post(self, request, *args, **kwargs):
        formset = self.PurchaseFormSet(
            request.POST,
            request.FILES,
            form_kwargs={"user": self.request.user, "project": self.project},
        )
        if formset.is_valid():
            formset.save()
            return redirect(self.get_success_url())
        else:
            self.formset = formset
        return super().get(request, *args, **kwargs)


class BasePurchaseView(LoginRequiredMixin, View):
    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project.objects.filter(active=True), pk=self.kwargs["project_pk"]
        )

    def get_context_data(self, *args, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        context["request"] = self.request
        return context

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        kwargs["project"] = self.project
        return kwargs

    def get_success_url(self):
        return reverse("project_legal", args=[self.project.pk])


class BasePurchaseDetailView(BasePurchaseView, DetailView):
    model = Purchase

    def get_context_data(self, *args, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        if not self.object:
            self.object: Purchase = self.get_object()
        self.add_purchase_forms(self.object)
        return context


class PurchaseCreateView(
    BasePurchaseView, LoginRequiredMixin, RolePermissionMixin, CreateView
):
    # Note: This creates Purchase objects but checks Project permissions
    # We need to override get_model to return Project for permission checking
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"

    def get_model(self):
        # Override to check Project permissions instead of Purchase
        from apps.projects.models import Project

        return Project

    form_class = PurchaseForm
    model = Purchase
    template_name = "projects/legal/purchase_form.html"

    def create_action_record(self):
        self.object: Purchase
        text = '{user} added a new product, "{product}"'.format(
            user=self.object.creator.full_name, product=self.object.name
        )
        record(
            project=self.object.project,
            action_type=ActionType.PURCHASE_ADDED,
            editor=self.request.user,
            description=text,
        )

    def create_notifications(self):
        self.object: Purchase
        message = "{user} added <a href='{url}'>{name}</a>.".format(
            user=self.request.user.full_name,
            url=reverse("project_legal", args=[self.object.project.pk]),
            name=self.object.name,
        )
        notify(
            message_type=MessageTypes.PURCHASE_NEW,
            message=message,
            priority=Priority.HIGH,
            creator=self.request.user,
            content_object=self.object,
        )

    def form_valid(self, form: ModelForm) -> HttpResponse:
        self.object: Purchase
        response = super().form_valid(form)
        self.create_notifications()
        self.create_action_record()

        if self.request.is_ajax() and self.object:
            serializer = PurchaseSerializer(
                self.object, context={"request": self.request}
            )
            return JsonResponse(serializer.data)

        messages.success(self.request, "Product has been created.")
        return response

    def form_invalid(self, form: ModelForm) -> HttpResponse:
        if self.request.is_ajax():
            message = {"errors": form.errors}
            return JsonResponse(message)
        return super().form_invalid(form)


class PurchaseUpdateView(BasePurchaseDetailView, PurchaseEditAccessMixin, UpdateView):
    form_class = PurchaseForm

    def form_valid(self, form: ModelForm) -> HttpResponse:
        self.object: Purchase
        response = super().form_valid(form)
        if self.request.is_ajax():
            serializer = PurchaseSerializer(
                self.object, context={"request": self.request}
            )
            return JsonResponse(serializer.data)
        return response

    def form_invalid(self, form: ModelForm) -> HttpResponse:
        if self.request.is_ajax():
            message = {"errors": form.errors}
            return JsonResponse(message, status=302)
        return super().form_invalid(form)


class PurchaseDeleteView(BasePurchaseDetailView, PurchaseEditAccessMixin, DeleteView):
    template_name = "projects/legal/partials/purchase_delete.html"

    def delete(self, request, *args, **kwargs):
        """
        Call the delete() method on the fetched object and then redirect to the
        success URL or return JSON to async request.
        """
        self.object: Purchase = self.get_object()
        object_pk = self.object.pk
        success_url = self.get_success_url()
        self.object.delete()
        if self.request.is_ajax():
            message = {"deleted": object_pk}
            return JsonResponse(message)
        return HttpResponseRedirect(success_url)

    def get_success_url(self):
        return super().get_success_url()


class PurchaseReproposeView(BasePurchaseDetailView, UserPassesTestMixin):
    def test_func(self):
        purchase: Purchase = self.get_object()
        return purchase.user_has_edit_delete_access(self.request.user)

    def create_action_record(self):
        self.object: Purchase
        text = 'The product, "{product}", was resubmitted by {user}'.format(
            user=self.object.creator.full_name, product=self.object.name
        )
        record(
            project=self.object.project,
            action_type=ActionType.PURCHASE_RESUBMITTED,
            editor=self.request.user,
            description=text,
        )

    def create_notifications(self):
        self.object: Purchase
        message = (
            "{user} resubmitted <a href='{url}'>{name}</a> for evaluation.".format(
                user=self.request.user.full_name,
                url=reverse("project_legal", args=[self.object.project.pk]),
                name=self.object.name,
            )
        )
        notify(
            message_type=MessageTypes.PURCHASE_REPROPOSED,
            message=message,
            priority=Priority.HIGH,
            creator=self.request.user,
            content_object=self.object,
        )

    def get(self, *args, **kwargs):
        self.object: Purchase = self.get_object()
        return redirect("project_legal", self.object.project.pk)

    def post(self, request, **kwargs):
        self.object: Purchase = self.get_object()
        if (
            self.object.action_required != ActionRequired.NONE
            and self.object.user_has_edit_delete_access(request.user)
        ):
            self.object.action_required = ActionRequired.NONE
            self.object.save()
            self.create_notifications()
            self.create_action_record()
        if self.request.is_ajax():
            serializer = PurchaseSerializer(self.object, context={"request": request})
            return JsonResponse(serializer.data)
        return redirect("project_legal", self.object.project.pk)


class PurchaseValidationView(BasePurchaseDetailView, UserPassesTestMixin, UpdateView):
    form_class = PurchaseValidationForm

    def test_func(self):
        purchase: Purchase = self.get_object()
        return purchase.user_can_validate(self.request.user)

    def create_action_record(self):
        self.object: Purchase
        if self.object.action_required == ActionRequired.NONE:
            action_type = ActionType.PURCHASE_EVALUATED
            text_string = 'The product, "{product}", was evaluated by Segment Legal'
        elif self.object.action_required == ActionRequired.REEVALUATE:
            action_type = ActionType.PURCHASE_REEVALUATE
            text_string = (
                'The product, "{product}", was marked "Resubmit" by Segment Legal'
            )
        else:
            return

        text = text_string.format(product=self.object.name)
        record(
            project=self.object.project,
            action_type=action_type,
            editor=self.request.user,
            description=text,
        )

    def create_notifications(self):
        self.object: Purchase
        if self.object.action_required == ActionRequired.NONE:
            message_type = "purchase_segment_accepted"
            action = "accepted"
        elif self.object.action_required == ActionRequired.REEVALUATE:
            message_type = "purchase_segment_rejected"
            action = "rejected"
        else:
            return
        message = "{user} {action} the Segment Legal Evaluation <a href='{url}'>{name}</a>.".format(
            user=self.request.user.full_name,
            action=action,
            url=reverse("project_legal", args=[self.object.project.pk]),
            name=self.object.name,
        )
        notify(
            message_type=message_type,
            message=message,
            priority=Priority.HIGH,
            creator=self.request.user,
            content_object=self.object,
        )

    def form_valid(self, form: ModelForm) -> Union[HttpResponse, JsonResponse]:
        self.object: Purchase
        super().form_valid(form)
        # reset prior legal decision
        if self.object.review_note:
            self.object.review_note = ""
            self.object.legal_decision = None
            self.object.save()
        self.create_action_record()
        self.create_notifications()
        if self.request.is_ajax():
            serializer = PurchaseSerializer(
                self.object, context={"request": self.request}
            )
            return JsonResponse(serializer.data)
        return redirect("project_legal", self.object.project.pk)

    def form_invalid(self, form: ModelForm) -> Union[HttpResponse, JsonResponse]:
        if self.request.is_ajax():
            message = {"errors": form.errors}
            return JsonResponse(message, status=302)
        return super().form_invalid(form)


class PurchaseSubmitToLegalView(BasePurchaseDetailView, UserPassesTestMixin):
    def test_func(self):
        purchase: Purchase = self.get_object()
        return purchase.user_can_submit(self.request.user)

    def create_action_record(self):
        self.object: Purchase
        text = 'The product, "{product}", was submitted to Corporate Legal by {user}'.format(
            user=self.object.creator.full_name, product=self.object.name
        )
        record(
            project=self.object.project,
            action_type=ActionType.PURCHASE_SUBMITTED,
            editor=self.request.user,
            description=text,
        )

    def create_notifications(self):
        self.object: Purchase
        message = "{user} submitted <a href='{url}'>{name}</a> to legal.".format(
            user=self.request.user.full_name,
            url=reverse("project_legal", args=[self.object.project.pk]),
            name=self.object.name,
        )
        notify(
            message_type=MessageTypes.PURCHASE_DRAFTED,
            message=message,
            priority=Priority.HIGH,
            creator=self.request.user,
            content_object=self.object,
        )

    def get(self, *args, **kwargs):
        self.object: Purchase = self.get_object()
        return redirect("project_legal", self.project.pk)

    def post(self, request, **kwargs):
        self.object: Purchase = self.get_object()
        if self.object.state is PurchaseState.VALIDATED:
            self.object.state = PurchaseState.DRAFTED
            self.object.action_required = ActionRequired.NONE
            self.object.save()
            self.create_action_record()
            self.create_notifications()
        if self.request.is_ajax():
            serializer = PurchaseSerializer(self.object, context={"request": request})
            return JsonResponse(serializer.data)
        return redirect("project_legal", self.object.project.pk)


class PurchaseApprovalView(BasePurchaseDetailView, UserPassesTestMixin, UpdateView):
    form_class = PurchaseApprovalForm

    def test_func(self):
        purchase: Purchase = self.get_object()
        return purchase.user_can_approve(self.request.user)

    def create_action_record(self):
        self.object: Purchase
        if self.object.state == PurchaseState.APPROVED:
            action = "accepted"
            action_type = ActionType.PURCHASE_ACCEPTED
        elif self.object.state == PurchaseState.PENDING:
            action = "marked 'Revise'"
            action_type = ActionType.PURCHASE_REVISE
        elif self.object.state == PurchaseState.REJECTED:
            action = "rejected"
            action_type = ActionType.PURCHASE_REJECTED
        text = 'The product, "{product}", was {action} by Corporate Legal'.format(
            action=action, product=self.object.name
        )
        record(
            project=self.object.project,
            action_type=action_type,
            editor=self.request.user,
            description=text,
        )

    def create_notifications(self):
        self.object: Purchase
        if self.object.state == PurchaseState.APPROVED:
            message_string = "{user} approved <a href='{url}'>{name}</a>."
            message_type = "purchase_approved"
        elif self.object.state == PurchaseState.PENDING:
            message_string = (
                "{user} requested resubmission for <a href='{url}'>{name}</a>."
            )
            message_type = "purchase_needs_revision"
        elif self.object.state == PurchaseState.REJECTED:
            message_string = "{user} rejected <a href='{url}'>{name}</a>."
            message_type = "purchase_rejected"

        message = message_string.format(
            user=self.request.user.full_name,
            url=reverse("project_legal", args=[self.object.project.pk]),
            name=self.object.name,
        )
        notify(
            message_type=message_type,
            message=message,
            priority=Priority.HIGH,
            creator=self.request.user,
            content_object=self.object,
        )

    def form_valid(self, form: ModelForm) -> HttpResponse:
        self.object: Purchase = self.get_object()
        if self.object.state is PurchaseState.DRAFTED:
            super().form_valid(form)
            self.create_action_record()
            self.create_notifications()
        if self.request.is_ajax():
            serializer = PurchaseSerializer(
                self.object, context={"request": self.request}
            )
            return JsonResponse(serializer.data)
        return redirect("project_legal", self.object.project.pk)

    def form_invalid(self, form: ModelForm) -> Union[HttpResponse, JsonResponse]:
        if self.request.is_ajax():
            message = {"errors": form.errors}
            return JsonResponse(message, status=302)
        return super().form_invalid(form)
