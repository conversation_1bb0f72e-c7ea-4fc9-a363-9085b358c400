# Generated by Django 5.2.4 on 2025-10-28 00:03

import auto_prefetch
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("attachments", "0001_initial"),
        ("programs", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="program",
            name="created_by",
            field=models.ForeignKey(
                editable=False,
                on_delete=django.db.models.deletion.PROTECT,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="program",
            name="executive_sponsors",
            field=models.ManyToManyField(
                blank=True,
                related_name="executive_sponsors",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="program",
            name="program_managers",
            field=models.ManyToManyField(
                blank=True, related_name="program_managers", to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="program",
            name="shared_with",
            field=models.ManyToManyField(
                blank=True,
                help_text="These individuals can view, but not edit the Program.",
                related_name="shared_with",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="programattachment",
            name="attachment",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="attachments.attachment"
            ),
        ),
        migrations.AddField(
            model_name="programattachment",
            name="program",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="programs.program"
            ),
        ),
    ]
