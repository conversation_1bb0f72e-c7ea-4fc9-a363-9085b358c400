# Generated by Django 5.2.4 on 2025-10-28 00:03

import auto_prefetch
import django.db.models.deletion
import django.db.models.manager
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("organizations", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProgramAttachment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="Program",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("active", models.<PERSON>oleanField(default=True)),
                ("name", models.CharField(max_length=255)),
                ("summary", models.TextField(blank=True, null=True)),
                (
                    "state",
                    models.CharField(
                        choices=[("inactive", "Inactive"), ("active", "Active")],
                        default="active",
                        max_length=100,
                    ),
                ),
                ("private", models.BooleanField(blank=True, default=False)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
                (
                    "company",
                    auto_prefetch.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="organizations.company",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
