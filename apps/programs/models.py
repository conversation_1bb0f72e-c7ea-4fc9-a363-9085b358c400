import operator
import re
import auto_prefetch
from functools import reduce
from typing import cast

from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation
from django.db import models
from django.db.models import Q
from django.db.models.query import QuerySet
from django.urls import reverse

from apps.attachments.models import Attachment
from apps.comments.models import Comment, Commentable
from apps.links.models import Link

from apps.users.models import User


class ProgramAttachment(auto_prefetch.Model):
    program = auto_prefetch.ForeignKey("Program", on_delete=models.CASCADE)
    attachment = auto_prefetch.ForeignKey(Attachment, on_delete=models.CASCADE)


class ProgramQuerySet(QuerySet):
    def search(self, query: str):
        quoted_re = re.compile('"(.*?)"')
        keywords = re.findall(quoted_re, query)
        keywords += re.sub(quoted_re, "", query).split()
        params = []
        fields = ("name", "summary")
        for keyword in keywords:
            for field in fields:
                params.append(Q(**{field + "__icontains": keyword}))
        queryset = self.filter(reduce(operator.or_, params))
        return cast(ProgramQuerySet, queryset)


class ProgramManager(auto_prefetch.Manager):
    def get_queryset(self) -> ProgramQuerySet:
        return ProgramQuerySet(self.model, using=self._db)

    def search(self, query: str) -> ProgramQuerySet:
        return self.get_queryset().search(query)


class Program(Commentable):
    company = auto_prefetch.ForeignKey(
        "organizations.Company", on_delete=models.PROTECT
    )
    STATE_ACTIVE = "active"
    STATE_INACTIVE = "inactive"
    STATE_CHOICES = ((STATE_INACTIVE, "Inactive"), (STATE_ACTIVE, "Active"))
    STATE_DICT = dict(STATE_CHOICES)

    active = models.BooleanField(default=True)
    name = models.CharField(max_length=255)
    summary = models.TextField(blank=True, null=True)
    state = models.CharField(
        max_length=100, choices=STATE_CHOICES, default=STATE_ACTIVE
    )
    executive_sponsors = models.ManyToManyField(
        "users.User", related_name="executive_sponsors", blank=True
    )
    program_managers = models.ManyToManyField(
        "users.User", related_name="program_managers", blank=True
    )
    shared_with = models.ManyToManyField(
        "users.User",
        related_name="shared_with",
        blank=True,
        help_text="These individuals can view, but not edit the Program.",
    )
    private = models.BooleanField(default=False, blank=True)
    created_by = models.ForeignKey(
        "users.User", editable=False, on_delete=models.PROTECT
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    attachments = GenericRelation(Attachment)
    comments = GenericRelation(Comment)
    links = GenericRelation(Link)

    objects = ProgramManager()

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse("program_detail", kwargs={"pk": self.pk})

    def deactivate(self):
        self.project_set.clear()
        self.active = False
        self.save()
