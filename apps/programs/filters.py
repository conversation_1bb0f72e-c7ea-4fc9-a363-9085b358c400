import django_filters as filters
from django.db.models.query import QuerySet

from apps.users.models import User

from .models import Program


class ProgramOrderingFilter(filters.OrderingFilter):
    def filter(self, qs, value):
        if value:
            if "program_manager" in value:
                return qs.order_by("program_managers__first_name")
            elif "-program_manager" in value:
                return qs.order_by("-program_managers__first_name")
            elif "executive_sponsor" in value:
                return qs.order_by("executive_sponsors__first_name")
            elif "-executive_sponsor" in value:
                return qs.order_by("-executive_sponsors__first_name")
        return super().filter(qs, value)


class ProgramFilter(filters.FilterSet):
    id = filters.AllValuesMultipleFilter()
    state = filters.ChoiceFilter(field_name="state", choices=Program.STATE_CHOICES)
    search = filters.CharFilter(method="filter_search")
    person = filters.ModelChoiceFilter(
        queryset=User.objects.all(), method="filter_person"
    )
    role = filters.MultipleChoiceFilter()

    order_by = ProgramOrderingFilter(
        fields=[
            "id",
            "name",
            "program_manager",
            "executive_sponsor",
            "state",
            "modified",
        ]
    )

    def filter_search(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        return queryset.search(value)

    def filter_person(self, queryset: QuerySet, name: str, value: User) -> QuerySet:
        """
        Filter projects by person.
        If roles are also selected, only show projects where the person has those roles.
        Otherwise, show all projects where the person is a team member.
        """
        from apps.users.models import Role

        if not value:
            return queryset

        # Get selected roles if any
        selected_roles = None
        if "role" in self.data.keys():
            role_ids = self.data.getlist("role", None)
            if role_ids:
                selected_roles = Role.objects.filter(id__in=role_ids)

        # Filter by team membership
        queryset = queryset.filter(team_members=value)

        # If specific roles are selected, further filter by those roles
        if selected_roles:
            from apps.users.models import RoleAssignment

            # Check if the person actually has any of the selected roles
            person_has_roles = RoleAssignment.objects.filter(
                user=value, role__in=selected_roles
            ).exists()

            if not person_has_roles:
                # Person doesn't have any of the selected roles, return empty queryset
                return queryset.none()

            # Person has at least one of the selected roles
            # Filter projects where they have role assignments matching the selected roles
            queryset = queryset.filter(
                projectroleassignment__user=value,
                projectroleassignment__role__in=selected_roles,
            ).distinct()

        return queryset
