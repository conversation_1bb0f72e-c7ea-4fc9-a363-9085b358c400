import json

import reversion
from django import forms
from django.db.models import Q

from apps.attachments.models import Attachment
from apps.links.models import Link
from apps.projects.models import Project
from apps.users.models import User

from .models import Program

from apps.users.models import Role


class ProgramProjectsForm(forms.ModelForm):
    projects = forms.ModelMultipleChoiceField(
        queryset=Project.objects.all(), required=False
    )

    class Meta:
        model = Program
        fields = ["projects"]

    def __init__(self, *args, user=None, **kwargs):
        super().__init__(*args, **kwargs)
        project_queryset = self.fields["projects"].queryset
        self.fields["projects"].queryset = project_queryset

    def save(self, *args, **kwargs):
        with reversion.create_revision():
            instance = super().save(*args, **kwargs)
            projects = self.cleaned_data.get("projects")

            instance.project_set.clear()
            if projects is not None:
                for project in projects:
                    instance.project_set.add(project)

        return instance


class ProgramForm(forms.ModelForm):
    state = forms.ChoiceField(
        choices=Program.STATE_CHOICES,
        required=False,
        widget=forms.RadioSelect(attrs={"class": "SelectButtons"}),
    )

    attachments = forms.ModelMultipleChoiceField(
        queryset=Attachment.objects.all(), required=False
    )
    links = forms.ModelMultipleChoiceField(queryset=Link.objects.all(), required=False)

    private = forms.ChoiceField(
        choices=[(False, "All Members"), (True, "Program Team")],
        required=False,
        widget=forms.RadioSelect(attrs={"class": "SelectButtons"}),
        label="Visibility",
    )

    executive_sponsors = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name").filter(
            is_active=True
        ),
        label="Executive Sponsor",
        required=False,
    )

    program_managers = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name").filter(
            is_active=True
        ),
        label="Program Manager",
        required=False,
    )

    shared_with = forms.ModelMultipleChoiceField(
        queryset=User.objects.order_by("first_name", "last_name").filter(
            is_active=True
        ),
        label="Shared With",
        required=False,
        help_text=Program._meta.get_field("shared_with").help_text,
    )

    projects = forms.ModelMultipleChoiceField(
        queryset=Project.objects.all(), required=False
    )

    class Meta:
        model = Program
        fields = ["name", "summary", "state", "private", "shared_with"]
        labels = {"name": "Program Name", "summary": "Program Summary"}

    def __init__(self, *args, user=None, **kwargs):
        super().__init__(*args, **kwargs)
        project_queryset = self.fields["projects"].queryset

        if self.instance.pk:
            project_queryset = project_queryset.filter(
                Q(program__isnull=True) | Q(program__pk=self.instance.pk)
            )
            self.fields[
                "executive_sponsors"
            ].initial = self.instance.executive_sponsors.first()
            self.fields[
                "program_managers"
            ].initial = self.instance.program_managers.first()
            self.fields["projects"].initial = Project.objects.filter(
                program=self.instance.pk
            )
            self.fields["attachments"].initial = [
                attachment.id for attachment in self.instance.attachments.all()
            ]
            self.fields["links"].initial = [
                link.id for link in self.instance.links.all()
            ]
        else:
            self.instance.created_by = user
            project_queryset = project_queryset.filter(program__isnull=True)
            self.fields["projects"].initial = None
            self.fields["state"].initial = "active"
            self.fields["private"].initial = False

        self.fields["projects"].queryset = project_queryset
        self.fields["shared_with"].label = "Share with..."
        self.fields["summary"].widget.attrs["class"] = "ProgramSummary"

        privacy_choice_dict = {
            "True": "Visible only to people assigned to roles",
            "False": "Visible to everyone in the organization",
        }

        default_subtitle = (
            privacy_choice_dict["False"]
            if not self.instance
            else privacy_choice_dict[str(self.instance.private)]
        )

        self.fields["private"].widget.attrs.update(
            {
                "default_subtitle": default_subtitle,
                "has_switchable_subtitles": True,
                "data-switchable_subtitles": json.dumps(privacy_choice_dict),
            }
        )

    def save(self, *args, **kwargs):
        with reversion.create_revision():
            attachments = self.cleaned_data.pop("attachments")
            links = self.cleaned_data.pop("links")

            instance = super().save(*args, **kwargs)

            for attachment in attachments:
                attachment.content_object = self.instance
                attachment.save()

            for link in links:
                link.content_object = self.instance
                link.save()

            projects = self.cleaned_data.get("projects")

            instance.project_set.clear()
            if projects is not None:
                for project in projects:
                    instance.project_set.add(project)

            executive_sponsors = self.cleaned_data.get("executive_sponsors")
            instance.executive_sponsors.clear()
            if executive_sponsors is not None:
                instance.executive_sponsors.add(executive_sponsors)

            program_managers = self.cleaned_data.get("program_managers")
            instance.program_managers.clear()
            if program_managers is not None:
                instance.program_managers.add(program_managers)

        return instance


class ProgramFilterForm(forms.Form):
    STATE_CHOICES = (("", "Show All"),) + Program.STATE_CHOICES
    person = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name"),
        empty_label="Show All",
        required=False,
    )

    role = forms.ModelMultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        queryset=Role.objects.none(),
        required=False,
    )

    state = forms.ChoiceField(
        choices=STATE_CHOICES, required=False, widget=forms.RadioSelect()
    )
    search = forms.CharField(max_length=200, widget=forms.HiddenInput())
    order_by = forms.CharField(widget=forms.HiddenInput())

    def __init__(self, *args, user=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["role"].queryset = Role.objects.filter(company=user.company)
