from typing import cast

from django.contrib import messages
from django.contrib.contenttypes.models import ContentType
from django.forms.models import ModelForm
from django.http.request import HttpRequest
from django.http.response import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.views.generic import (
    <PERSON>reate<PERSON>iew,
    DeleteView,
    DetailView,
    ListView,
    UpdateView,
)
from rest_framework import status
from rest_framework.authentication import SessionAuthentication
from rest_framework.generics import CreateAPIView, UpdateAPIView
from rest_framework.parsers import MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from viewclass_mixins.views import CorsMixin

from apps.actions.models import ActionType, record
from apps.attachments.mixins import AttachmentJSONMixin
from apps.attachments.models import Attachment
from apps.attachments.serializers import AttachmentSerializer
from apps.comments.serializers import CommentSerializer
from apps.links.mixins import LinkJSONMixin
from apps.links.models import Link
from apps.links.serializers import LinkSerializer
from apps.notifications.models import MessageTypes, notify
from apps.programs.permissions import ProgramEditDeletePermission
from apps.users.models import User
from apps.utils.mixins import (
    CompanyAdminRequiredMixin,
    LoginRequiredMixin,
)
from apps.organizations.mixins import CompanyQuerysetMixin, RolePermissionMixin

from .filters import ProgramFilter
from .forms import ProgramForm, ProgramProjectsForm
from .models import Program, ProgramAttachment
from .serializers import (
    ProgramAttachmentSerializer,
    ProgramChangesSerializer,
    ProgramSerializer,
)


class ProgramListView(LoginRequiredMixin, ListView):
    queryset = Program.objects.filter(active=True)
    template_name = "programs/program_list.html"
    paginate_by = 10

    def get_context_data(self, *, object_list=None, **kwargs):
        context = super().get_context_data()
        context["program_pks"] = [program.pk for program in self.object_list]
        return context

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = ProgramFilter(self.request.GET, queryset=queryset).qs.distinct()
        return queryset

    def get(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        query: str = request.GET.get("search", "")
        if query.isdigit() and Program.objects.filter(pk=query).exists():
            return redirect("program_detail", pk=query)
        return super().get(request, *args, **kwargs)


class ProgramDetailView(
    LoginRequiredMixin,
    RolePermissionMixin,
    AttachmentJSONMixin,
    LinkJSONMixin,
    DetailView,
):
    model = Program
    required_permission = "read"
    permission_denied_redirect_url = "/programs/"
    queryset = Program.objects.all()
    template_name = "programs/program_detail.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["projects"] = self.object.project_set
        context["project_pks"] = [project.pk for project in context["projects"]]
        context["content_type_id"] = ContentType.objects.get_for_model(Program).pk

        user = self.request.user

        if user.is_anonymous:
            can_edit = False
        else:
            person = User.objects.get(email=user.email)
            program = context["program"]
            can_edit = (
                user.is_superuser
                or program.created_by == user
                or person in program.program_managers.all()
                or person in program.executive_sponsors.all()
            )

        context["can_edit"] = can_edit
        context["program_comments"] = CommentSerializer(
            self.object.comments.all(), many=True, context={"request": self.request}
        ).data

        return context

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        if not self.object.active:
            return self.response_class(
                request=self.request,
                template="programs/program_detail_deactivated.html",
                context={"program": self.object},
                using=self.template_engine,
            )
        return super().get(request, *args, **kwargs)


class ProgramCreateView(CompanyAdminRequiredMixin, CompanyQuerysetMixin, CreateView):
    Program.objects.all()
    form_class = ProgramForm
    template_name = "programs/program_form_create.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["has_perms"] = True
        context["content_type_id"] = ContentType.objects.get_for_model(Program).pk
        if self.request.POST.get("attachments"):
            attachment_ids = self.request.POST.getlist("attachments")
            context["attachments"] = AttachmentSerializer(
                Attachment.objects.filter(id__in=attachment_ids), many=True
            ).data
        if self.request.POST.get("links"):
            link_ids = self.request.POST.getlist("links")
            context["links"] = LinkSerializer(
                Link.objects.filter(id__in=link_ids), many=True
            ).data
        return context

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def get_success_url(self):
        return reverse("program_detail", args=[self.object.pk])

    def create_notifications(self):
        self.object: Program
        message = "{user} added <a href='{url}'>{name}</a>.".format(
            user=self.request.user.full_name,
            url=reverse("program_detail", args=[self.object.pk]),
            name=self.object.name,
        )
        notify(
            message_type=MessageTypes.PROJECT_NEW,
            message=message,
            creator=self.request.user,
            content_object=self.object,
        )

    def form_valid(self, form: ModelForm) -> HttpResponse:
        response = super().form_valid(form)
        self.create_notifications()
        messages.success(self.request, "Program created.")
        return response


def create_action_records_for_program(previous_data, program, user):
    current_data = ProgramChangesSerializer(program).data
    changed_fields = [
        k
        for k in current_data
        if k in previous_data and current_data[k] != previous_data[k]
    ]

    for field in changed_fields:
        if field == "projects":
            added_projects = set(current_data["projects"]) - set(
                previous_data["projects"]
            )
            for project in added_projects:
                record(
                    project=project, action_type=ActionType.ADDED_PROGRAM, editor=user
                )


def create_notifications_for_program(previous_data, program, user):
    current_data = ProgramChangesSerializer(program).data
    changed_fields = [
        k
        for k in current_data
        if k in previous_data and current_data[k] != previous_data[k]
    ]

    for field in changed_fields:
        if field == "program_managers":
            added_people = set(current_data["program_managers"]) - set(
                previous_data["program_managers"]
            )
            message = "{user} added You as an Program Manager to <a href='{url}'>{name}</a>.".format(
                user=user.full_name,
                url=reverse("program_detail", args=[program.pk]),
                name=program.name,
            )
            notify(
                message_type=MessageTypes.ROLE_ASSIGNED,
                message=message,
                creator=user,
                content_object=program,
                recipients=added_people,
            )
        elif field == "executive_sponsors":
            added_people = set(current_data["executive_sponsors"]) - set(
                previous_data["executive_sponsors"]
            )
            message = "{user} added You as a Executive Sponsor to <a href='{url}'>{name}</a>.".format(
                user=user.full_name,
                url=reverse("program_detail", args=[program.pk]),
                name=program.name,
            )
            notify(
                message_type=MessageTypes.ROLE_ASSIGNED,
                message=message,
                creator=user,
                content_object=program,
                recipients=added_people,
            )
        elif field == "shared_with":
            added_people = set(current_data["shared_with"]) - set(
                previous_data["shared_with"]
            )
            message = "{user} added You as a Share Recipient to <a href='{url}'>{name}</a>.".format(
                user=user.full_name,
                url=reverse("program_detail", args=[program.pk]),
                name=program.name,
            )
            notify(
                message_type=MessageTypes.ROLE_ASSIGNED,
                message=message,
                creator=user,
                content_object=program,
                recipients=added_people,
            )

        # TODO - put this back in, when we're ready to add project notificaions
        # elif field == "projects":
        #     added_projects = set(current_data["projects"]) - set(previous_data["projects"])
        #     removed_projects = set(previous_data["projects"]) - set(current_data["projects"])
        #     recipients = (
        #         program.program_managers.all()
        #         | program.executive_sponsors.all()
        #         | Person.objects.filter(email=program.created_by.email)
        #     ).distinct()
        #
        #     if added_projects:
        #         project_string = ", ".join(
        #             [
        #                 f"<a href='{reverse('project_detail', args=[project.id])}'>{project.name}</a>"
        #                 for project in added_projects
        #             ]
        #         )
        #         program_string = (
        #             f"<a href='{reverse('program_detail', args=[program.pk])}'>{program.name}</a>"
        #         )
        #         user_name = user.full_name
        #
        #         project_word = "Project"
        #         if len(added_projects) > 1:
        #             project_word += "s"
        #
        #         message = (
        #             f"{user_name} added {project_word} {project_string} to Program {program_string}"
        #         )
        #         notify(
        #             message_type="project_added_to_program",
        #             message=message,
        #             creator=user,
        #             conent_object=program,
        #             recipients=recipients,
        #         )
        #     if removed_projects:
        #         project_string = ", ".join(
        #             [
        #                 f"<a href='{reverse('project_detail', args=[project.id])}'>{project.name}</a>"
        #                 for project in removed_projects
        #             ]
        #         )
        #         program_string = (
        #             f"<a href='{reverse('program_detail', args=[program.pk])}'>{program.name}</a>"
        #         )
        #         user_name = user.full_name
        #
        #         project_word = "Project"
        #         if len(added_projects) > 1:
        #             project_word += "s"
        #
        #         message = f"{user_name} removed {project_word} {project_string} from Program {program_string}"
        #         notify(
        #             message_type="project_removed_from_program",
        #             message=message,
        #             creator=user,
        #             conent_object=program,
        #             recipients=recipients,
        #         )


class ProgramUpdateView(
    LoginRequiredMixin,
    RolePermissionMixin,
    AttachmentJSONMixin,
    LinkJSONMixin,
    UpdateView,
):
    model = Program
    required_permission = "update"
    permission_denied_redirect_url = "/programs/"
    queryset = Program.objects.all()
    form_class = ProgramForm
    template_name = "programs/program_form_update.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["has_perms"] = True
        context["content_type_id"] = ContentType.objects.get_for_model(Program).pk
        return context

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def post(self, request, *args, **kwargs):
        self.previous_data = ProgramChangesSerializer(self.get_object()).data
        return super().post(request, *args, **kwargs)

    def get_success_url(self):
        return reverse("program_detail", args=[self.object.pk])

    def form_valid(self, form: ModelForm) -> HttpResponse:
        response = super().form_valid(form)
        create_action_records_for_program(
            previous_data=self.previous_data,
            program=self.object,
            user=self.request.user,
        )
        create_notifications_for_program(
            previous_data=self.previous_data,
            program=self.object,
            user=self.request.user,
        )
        self.object.save()
        messages.success(self.request, "Program updated.")
        return response


class ProgramUpdateProjectsView(LoginRequiredMixin, RolePermissionMixin, UpdateView):
    model = Program
    required_permission = "update"
    permission_denied_redirect_url = "/programs/"
    queryset = Program.objects.all()
    form_class = ProgramProjectsForm
    template_name = "programs/program_form_update.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["has_perms"] = True
        return context

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def post(self, request, *args, **kwargs):
        self.previous_data = ProgramChangesSerializer(self.get_object()).data
        return super().post(request, *args, **kwargs)

    def get_success_url(self):
        return reverse("program_detail", args=[self.object.pk])

    def form_valid(self, form: ModelForm) -> HttpResponse:
        response = super().form_valid(form)
        create_action_records_for_program(
            previous_data=self.previous_data,
            program=self.object,
            user=self.request.user,
        )
        create_notifications_for_program(
            previous_data=self.previous_data,
            program=self.object,
            user=self.request.user,
        )
        self.object.save()
        messages.success(self.request, "Program updated.")
        return response


class ProgramUpdateAPIView(CorsMixin, UpdateAPIView):
    queryset = Program.objects.all()
    serializer_class = ProgramSerializer
    authentication_classes = [SessionAuthentication]
    permission_classes = [IsAuthenticated, ProgramEditDeletePermission]

    def put(self, request, *args, **kwargs):
        self.previous_data = ProgramChangesSerializer(self.get_object()).data
        return super().put(request, *args, **kwargs)

    def perform_update(self, serializer):
        program: Program = self.get_object()
        super().perform_update(serializer)
        create_action_records_for_program(
            previous_data=self.previous_data, program=program, user=self.request.user
        )
        create_notifications_for_program(
            previous_data=self.previous_data, program=program, user=self.request.user
        )


class ProgramDeleteView(RolePermissionMixin, DeleteView):
    model = Program
    required_permission = "delete"
    permission_denied_redirect_url = "/programs/"
    queryset = Program.objects.all()
    template_name = "programs/program_confirm_delete.html"

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object = cast(Program, self.get_object())
        self.object.deactivate()
        success_url = self.get_success_url()
        response = HttpResponseRedirect(success_url)
        messages.success(request, "Program has been deleted.")
        return response

    def get_success_url(self) -> str:
        url = reverse("program_list")
        return f"{url}?state=active"


class ProgramAttachmentCreateView(CreateAPIView):
    queryset = ProgramAttachment.objects.all()
    serializer_class = AttachmentSerializer
    permission_classes = [IsAuthenticated, ProgramEditDeletePermission]
    parser_classes = [MultiPartParser]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["created_by"] = self.request.user
        return context

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        program = get_object_or_404(Program, pk=self.kwargs["pk"])
        program_attachment_serializer = ProgramAttachmentSerializer(
            data={"program": program.id, "attachment": serializer.data["id"]}
        )
        program_attachment_serializer.is_valid(raise_exception=True)
        program_attachment_serializer.save()
        headers = self.get_success_headers(program_attachment_serializer.data)
        return Response(
            program_attachment_serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers,
        )
