from typing import List

from django.contrib.humanize.templatetags.humanize import naturalday
from django.urls import reverse
from rest_framework import serializers

from apps.projects.models import Project
from apps.users.models import User

from .models import Program, ProgramAttachment


class ProgramSerializer(serializers.ModelSerializer):
    class Meta:
        model = Program
        fields = ["project_set"]

    def to_representation(self, instance):
        return {
            "project_set": [
                {
                    "name": project.name,
                    "id": project.id,
                    "health": project.current_health.health,
                    "url": reverse("project_detail", args=[project.pk]),
                }
                for project in instance.project_set.all()
            ]
        }


class ProgramAttachmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProgramAttachment
        fields = ["attachment", "program"]

    def to_representation(self, instance):
        return {
            "id": instance.attachment.id,
            "application_type": instance.attachment.application_type,
            "created_by": instance.attachment.created_by.full_name,
            "created": naturalday(instance.attachment.created).title(),
            "file_name": instance.attachment.file_name,
            "file_url": instance.attachment.download_url,
            "programattachment_id": instance.id,
            "program_id": instance.program.id,
            "delete_url": reverse("attachment_delete", args=[instance.attachment.id]),
        }


class ProgramChangesSerializer(serializers.ModelSerializer):
    program_managers = serializers.SerializerMethodField()
    executive_sponsors = serializers.SerializerMethodField()
    shared_with = serializers.SerializerMethodField()
    projects = serializers.SerializerMethodField()

    class Meta:
        model = Program
        fields = ["program_managers", "executive_sponsors", "shared_with", "projects"]

    def get_program_managers(self, obj: Program) -> List[User]:
        return list(obj.program_managers.all())

    def get_executive_sponsors(self, obj: Program) -> List[User]:
        return list(obj.executive_sponsors.all())

    def get_shared_with(self, obj: Program) -> List[User]:
        return list(obj.shared_with.all())

    def get_projects(self, obj: Program) -> List[Project]:
        return list(obj.project_set.all())
