import datetime

from django.test import RequestFactory
from django.urls import reverse

from apps.users.models import User
from apps.projects.tests.factories import ProjectFactory
from apps.projects.tests.test_project_list_export_view import setup_view
from apps.reports.views import ExpenditureReportExportView
from tests.base import BaseTestCase


class ComplianceListViewTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.private_project = ProjectFactory(private=True, project_state="active")
        self.regular_person = User.objects.get(email=self.regular_user.email)
        for i in range(10):
            ProjectFactory()

    def test_admin_view(self):
        self.client.force_login(self.admin_user)
        url = reverse("compliance_report")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_authenticated_view(self):
        self.client.force_login(self.regular_user)
        url = reverse("compliance_report")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_private_project_should_not_appear_in_list_to_unnamed_user(self):
        self.client.force_login(self.regular_user)
        url = reverse("compliance_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertFalse(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_business_lead(self):
        self.private_project.business_leads.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("compliance_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_business_analyst(
        self,
    ):
        self.private_project.business_analysts.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("compliance_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_executive_owner(
        self,
    ):
        self.private_project.executive_owners.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("compliance_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_project_manager(
        self,
    ):
        self.private_project.project_managers.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("compliance_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_finance_lead(self):
        self.private_project.finance_leads.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("compliance_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_other_stakeholder(
        self,
    ):
        self.private_project.other_stakeholders.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("compliance_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_created_by(self):
        self.private_project.created_by = self.regular_user
        self.private_project.save()

        self.client.force_login(self.regular_user)
        url = reverse("compliance_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_anonymous_view(self):
        url = reverse("compliance_report")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)


class RoadMapListViewTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.regular_person = User.objects.get(email=self.regular_user.email)
        self.private_project = ProjectFactory(
            private=True,
            project_state="active",
            start_date=datetime.datetime.today(),
            end_date=datetime.datetime.today() + datetime.timedelta(days=1),
        )
        yesterday = (datetime.datetime.today() - datetime.timedelta(days=1)).strftime(
            "%Y-%m-%d"
        )
        tomorrow = (datetime.datetime.today() + datetime.timedelta(days=1)).strftime(
            "%Y-%m-%d"
        )
        self.url_date_args = f"?date_after={yesterday}&date_before={tomorrow}"
        for i in range(10):
            ProjectFactory()

    def test_admin_view(self):
        self.client.force_login(self.admin_user)
        url = reverse("road_map")
        response = self.client.get(url + self.url_date_args)
        self.assertEqual(response.status_code, 200)

    def test_authenticated_view(self):
        self.client.force_login(self.regular_user)
        url = reverse("road_map")
        response = self.client.get(url + self.url_date_args)
        self.assertEqual(response.status_code, 200)

    def test_anonymous_view(self):
        url = reverse("road_map")
        response = self.client.get(url + self.url_date_args)
        self.assertEqual(response.status_code, 302)

    def test_private_project_should_not_appear_in_list_to_unnamed_user(self):
        self.client.force_login(self.regular_user)
        url = reverse("road_map")
        response = self.client.get(url + self.url_date_args)
        projects = response.context_data["object_list"]
        self.assertFalse(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_business_lead(self):
        self.private_project.business_leads.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("road_map")
        response = self.client.get(url + self.url_date_args)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_business_analyst(
        self,
    ):
        self.private_project.business_analysts.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("road_map")
        response = self.client.get(url + self.url_date_args)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_executive_owner(
        self,
    ):
        self.private_project.executive_owners.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("road_map")
        response = self.client.get(url + self.url_date_args)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_project_manager(
        self,
    ):
        self.private_project.project_managers.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("road_map")
        response = self.client.get(url + self.url_date_args)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_finance_lead(self):
        self.private_project.finance_leads.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("road_map")
        response = self.client.get(url + self.url_date_args)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_other_stakeholder(
        self,
    ):
        self.private_project.other_stakeholders.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("road_map")
        response = self.client.get(url + self.url_date_args)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_created_by(self):
        self.private_project.created_by = self.regular_user
        self.private_project.save()

        self.client.force_login(self.regular_user)
        url = reverse("road_map")
        response = self.client.get(url + self.url_date_args)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_query_count(self):
        self.client.force_login(self.admin_user)
        url = reverse("road_map")
        with self.assertNumQueries(4):
            self.client.get(url)


class ExpendituresListViewTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.private_project = ProjectFactory(private=True)
        self.regular_person = User.objects.get(email=self.regular_user.email)
        for i in range(10):
            ProjectFactory()

    def test_admin_view(self):
        self.client.force_login(self.admin_user)
        url = reverse("expenditures_report")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_authenticated_view(self):
        self.client.force_login(self.regular_user)
        url = reverse("expenditures_report")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_anonymous_view(self):
        url = reverse("expenditures_report")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_private_project_should_not_appear_in_list_to_unnamed_user(self):
        self.client.force_login(self.regular_user)
        url = reverse("expenditures_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertFalse(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_business_lead(self):
        self.private_project.business_leads.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("expenditures_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_business_analyst(
        self,
    ):
        self.private_project.business_analysts.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("expenditures_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_executive_owner(
        self,
    ):
        self.private_project.executive_owners.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("expenditures_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_project_manager(
        self,
    ):
        self.private_project.project_managers.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("expenditures_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_finance_lead(self):
        self.private_project.finance_leads.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("expenditures_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_other_stakeholder(
        self,
    ):
        self.private_project.other_stakeholders.add(self.regular_person)

        self.client.force_login(self.regular_user)
        url = reverse("expenditures_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_private_project_should_appear_in_list_to_user_named_as_created_by(self):
        self.private_project.created_by = self.regular_user
        self.private_project.save()

        self.client.force_login(self.regular_user)
        url = reverse("expenditures_report")
        response = self.client.get(url)
        projects = response.context_data["object_list"]
        self.assertTrue(self.private_project in projects)

    def test_query_count(self):
        self.client.force_login(self.admin_user)
        url = reverse("expenditures_report")
        with self.assertNumQueries(9):
            self.client.get(url)


class ExpendituresExportViewTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.private_project = ProjectFactory(private=True)
        for i in range(10):
            ProjectFactory()

    def test_admin_view(self):
        self.client.force_login(self.admin_user)
        url = reverse("expenditures_report_export")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_authenticated_view(self):
        self.client.force_login(self.regular_user)
        url = reverse("expenditures_report_export")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_anonymous_view(self):
        url = reverse("expenditures_report_export")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_private_projects_should_not_be_exported_to_unnamed_user(self):
        url = reverse("expenditures_report_export")
        request_factory = RequestFactory()
        request = request_factory.get(url)
        request.user = self.regular_user
        view = setup_view(ExpenditureReportExportView(), request)
        qs = view.filter_queryset(queryset=view.get_queryset())
        self.assertFalse(self.private_project in qs)
