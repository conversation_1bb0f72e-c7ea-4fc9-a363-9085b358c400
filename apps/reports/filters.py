import operator
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from functools import reduce
from typing import Iterable, List, Optional, Any

import django_filters as filters
from django.db.models import Q, QuerySet
from django.utils import timezone

from apps.projects.filters import (
    FILTER_PROJECT_STATE_CHOICES,
    ProjectOrderingFilter,
)
from apps.projects.models import Division, Project, ProjectHealth
from apps.users.models import User
from apps.projects.models.raid import (
    AllStatusOptions,
    GenericOptions,
    RaidReportOption,
    SectionOption,
)
from apps.purchases.models import Purchase, PurchaseState
from apps.reports.forms import TEAM_FILTER_CHOICES


class ComplianceReportFilter(filters.FilterSet):
    LAST_UPDATE_CHOICES = (
        ("", "Any Time"),
        ("lt_1_week", "Within 1 Week"),
        ("lt_2_weeks", "Within 2 Weeks"),
        ("gt_2_weeks", "Outside 2 Weeks"),
    )

    starred = filters.BooleanFilter(method="filter_starred")
    state = filters.MultipleChoiceFilter(
        field_name="project_state", choices=Project.PROJECT_STATE_CHOICES
    )
    last_update = filters.ChoiceFilter(
        method="filter_last_update", choices=LAST_UPDATE_CHOICES
    )
    overdue = filters.BooleanFilter(method="filter_overdue")
    grade = filters.RangeFilter()
    program = filters.NumberFilter(method="filter_program")
    person = filters.ModelChoiceFilter(
        queryset=User.objects.all(), method="filter_person"
    )
    role = filters.MultipleChoiceFilter()

    order_by = filters.OrderingFilter(
        fields=[
            "id",
            "name",
            "phase",
            "modified",
            "percent_complete_grade",
            "expenditure_grade",
            "health_grade",
            "recent_accomplishments_grade",
            "planned_activities_grade",
            "technology_components_grade",
            "executive_owners_grade",
            "project_managers_grade",
            "end_date",
            "grade",
        ]
    )

    def filter_program(self, queryset: QuerySet, name: str, value: Decimal) -> QuerySet:
        if value:
            queryset = queryset.filter(program__pk=value)
        return queryset

    def filter_starred(self, queryset: QuerySet, name: str, value: bool) -> QuerySet:
        if value:
            queryset = queryset.filter(starred=True)
        return queryset

    def filter_overdue(self, queryset: QuerySet, name: str, value: bool) -> QuerySet:
        if value:
            queryset = queryset.filter(end_date__lte=timezone.now())
        return queryset

    def filter_last_update(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        if value == "lt_1_week":
            queryset = queryset.filter(
                modified__gte=timezone.now() - timedelta(weeks=1)
            )
        elif value == "lt_2_weeks":
            queryset = queryset.filter(
                modified__gte=timezone.now() - timedelta(weeks=2)
            )
        elif value == "gt_2_weeks":
            queryset = queryset.filter(modified__lt=timezone.now() - timedelta(weeks=2))
        return queryset

    def filter_person(self, queryset: QuerySet, name: str, value: User) -> QuerySet:
        """
        Filter projects by person.
        If roles are also selected, only show projects where the person has those roles.
        Otherwise, show all projects where the person is a team member.
        """
        if not value:
            return queryset

        # Get selected roles if any
        selected_roles = None
        if "role" in self.data.keys():
            role_ids = self.data.getlist("role", None)
            if role_ids:
                selected_roles = Role.objects.filter(id__in=role_ids)

        # Filter by team membership
        queryset = queryset.filter(team_members=value)

        # If specific roles are selected, further filter by those roles
        if selected_roles:
            from apps.users.models import RoleAssignment

            # Check if the person actually has any of the selected roles
            person_has_roles = RoleAssignment.objects.filter(
                user=value, role__in=selected_roles
            ).exists()

            if not person_has_roles:
                # Person doesn't have any of the selected roles, return empty queryset
                return queryset.none()

            # Person has at least one of the selected roles
            # Filter projects where they have role assignments matching the selected roles
            queryset = queryset.filter(
                projectroleassignment__user=value,
                projectroleassignment__role__in=selected_roles,
            ).distinct()

        return queryset


class BenefitsReportFilter(filters.FilterSet):
    STATE_CHOICES = (
        ("", "Show All"),
        (Project.PROJECT_STATE_ACTIVE, "Active"),
        (Project.PROJECT_STATE_ON_HOLD, "On Hold"),
        (Project.PROJECT_STATE_COMPLETE, "Complete"),
    )

    starred = filters.BooleanFilter(method="filter_starred")
    date = filters.DateFromToRangeFilter(method="filter_date")
    state = filters.MultipleChoiceFilter(
        field_name="project_state", choices=STATE_CHOICES
    )

    search = filters.CharFilter(method="filter_search")
    program = filters.NumberFilter(method="filter_program")
    person = filters.ModelChoiceFilter(
        queryset=User.objects.all(), method="filter_person"
    )
    role = filters.MultipleChoiceFilter()

    order_by = filters.OrderingFilter(
        fields=[
            "id",
            "name",
            "date_range_savings",
            "total_savings",
            "annual_savings_target",
            "savings_difference",
        ]
    )

    def filter_person(self, queryset: QuerySet, name: str, value: User) -> QuerySet:
        """
        Filter projects by person.
        If roles are also selected, only show projects where the person has those roles.
        Otherwise, show all projects where the person is a team member.
        """
        if not value:
            return queryset

        # Get selected roles if any
        selected_roles = None
        if "role" in self.data.keys():
            role_ids = self.data.getlist("role", None)
            if role_ids:
                selected_roles = Role.objects.filter(id__in=role_ids)

        # Filter by team membership
        queryset = queryset.filter(team_members=value)

        # If specific roles are selected, further filter by those roles
        if selected_roles:
            from apps.users.models import RoleAssignment

            # Check if the person actually has any of the selected roles
            person_has_roles = RoleAssignment.objects.filter(
                user=value, role__in=selected_roles
            ).exists()

            if not person_has_roles:
                # Person doesn't have any of the selected roles, return empty queryset
                return queryset.none()

            # Person has at least one of the selected roles
            # Filter projects where they have role assignments matching the selected roles
            queryset = queryset.filter(
                projectroleassignment__user=value,
                projectroleassignment__role__in=selected_roles,
            ).distinct()

        return queryset

    def filter_program(self, queryset: QuerySet, name: str, value: Decimal) -> QuerySet:
        if value:
            queryset = queryset.filter(program__pk=value)
        return queryset

    def filter_starred(self, queryset: QuerySet, name: str, value: bool) -> QuerySet:
        if value:
            queryset = queryset.filter(starred=True)
        return queryset

    def filter_date(self, queryset: QuerySet, name: str, value: slice) -> QuerySet:
        return queryset.with_savings_calculations(
            start_date=value.start, end_date=value.stop
        )

    def filter_search(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        return queryset.search(value)


class RoadMapReportFilter(filters.FilterSet):
    starred = filters.BooleanFilter(method="filter_starred")
    date = filters.DateFromToRangeFilter(method="filter_date")
    state = filters.MultipleChoiceFilter(
        field_name="project_state", choices=Project.PROJECT_STATE_CHOICES
    )
    search = filters.CharFilter(method="filter_search")
    program = filters.NumberFilter(method="filter_program")
    person = filters.ModelChoiceFilter(
        queryset=User.objects.all(), method="filter_person"
    )
    role = filters.MultipleChoiceFilter()

    def filter_program(self, queryset: QuerySet, name: str, value: Decimal) -> QuerySet:
        if value:
            queryset = queryset.filter(program__pk=value)
        return queryset

    def filter_person(self, queryset: QuerySet, name: str, value: User) -> QuerySet:
        """
        Filter projects by person.
        If roles are also selected, only show projects where the person has those roles.
        Otherwise, show all projects where the person is a team member.
        """
        if not value:
            return queryset

        # Get selected roles if any
        selected_roles = None
        if "role" in self.data.keys():
            role_ids = self.data.getlist("role", None)
            if role_ids:
                selected_roles = Role.objects.filter(id__in=role_ids)

        # Filter by team membership
        queryset = queryset.filter(team_members=value)

        # If specific roles are selected, further filter by those roles
        if selected_roles:
            from apps.users.models import RoleAssignment

            # Check if the person actually has any of the selected roles
            person_has_roles = RoleAssignment.objects.filter(
                user=value, role__in=selected_roles
            ).exists()

            if not person_has_roles:
                # Person doesn't have any of the selected roles, return empty queryset
                return queryset.none()

            # Person has at least one of the selected roles
            # Filter projects where they have role assignments matching the selected roles
            queryset = queryset.filter(
                projectroleassignment__user=value,
                projectroleassignment__role__in=selected_roles,
            ).distinct()

        return queryset

    def filter_starred(self, queryset: QuerySet, name: str, value: bool) -> QuerySet:
        if value:
            queryset = queryset.filter(starred=True)
        return queryset

    def filter_date(self, queryset: QuerySet, name: str, value: slice) -> QuerySet:
        if value.start:
            queryset = queryset.filter(end_date__gt=value.start)
        if value.stop:
            queryset = queryset.filter(start_date__lt=value.stop)
        return queryset

    def filter_search(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        return queryset.search(value)


class ExpendituresReportFilter(filters.FilterSet):
    EXPENDITURE_CHOICES = (("capital", "Capital Expenditures"), ("opex", "OpEx"))

    starred = filters.BooleanFilter(method="filter_starred")
    state = filters.MultipleChoiceFilter(
        field_name="project_state", choices=Project.PROJECT_STATE_CHOICES
    )
    expenditure = filters.MultipleChoiceFilter(
        choices=EXPENDITURE_CHOICES, method="filter_expenditure"
    )
    search = filters.CharFilter(method="filter_search")
    program = filters.NumberFilter(method="filter_program")
    person = filters.ModelChoiceFilter(
        queryset=User.objects.all(), method="filter_person"
    )
    role = filters.MultipleChoiceFilter()

    order_by = filters.OrderingFilter(
        fields=[
            "id",
            "name",
            "capital_forecast_total",
            "capital_actuals_total",
            "capital_forecast_remaining",
            "operational_forecast_total",
            "operational_actuals_total",
            "operational_forecast_remaining",
        ]
    )

    def filter_person(self, queryset: QuerySet, name: str, value: User) -> QuerySet:
        """
        Filter projects by person.
        If roles are also selected, only show projects where the person has those roles.
        Otherwise, show all projects where the person is a team member.
        """
        if not value:
            return queryset

        # Get selected roles if any
        selected_roles = None
        if "role" in self.data.keys():
            role_ids = self.data.getlist("role", None)
            if role_ids:
                selected_roles = Role.objects.filter(id__in=role_ids)

        # Filter by team membership
        queryset = queryset.filter(team_members=value)

        # If specific roles are selected, further filter by those roles
        if selected_roles:
            from apps.users.models import RoleAssignment

            # Check if the person actually has any of the selected roles
            person_has_roles = RoleAssignment.objects.filter(
                user=value, role__in=selected_roles
            ).exists()

            if not person_has_roles:
                # Person doesn't have any of the selected roles, return empty queryset
                return queryset.none()

            # Person has at least one of the selected roles
            # Filter projects where they have role assignments matching the selected roles
            queryset = queryset.filter(
                projectroleassignment__user=value,
                projectroleassignment__role__in=selected_roles,
            ).distinct()

        return queryset

    def filter_program(self, queryset: QuerySet, name: str, value: Decimal) -> QuerySet:
        if value:
            queryset = queryset.filter(program__pk=value)
        return queryset

    def filter_starred(self, queryset: QuerySet, name: str, value: bool) -> QuerySet:
        if value:
            queryset = queryset.filter(starred=True)
        return queryset

    def filter_expenditure(
        self, queryset: QuerySet, name: str, value: Iterable[str]
    ) -> QuerySet:
        params = []
        if "capital" in value:
            params.append(Q(capital_expenditure=True))
        if "opex" in value:
            params.append(Q(opex_expenditure=True))
        queryset = queryset.filter(reduce(operator.or_, params))

        return queryset

    def filter_search(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        return queryset.search(value)


class LegalReportOrderingFilter(filters.OrderingFilter):
    def filter(self, qs, value):
        if value:
            if "primary_segment" in value:
                return qs.order_by("project__primary_division__name")
            elif "-primary_segment" in value:
                return qs.order_by("-project__primary_division__name")
            elif "executive_owner" in value:
                return qs.order_by("project__executive_owners__first_name")
            elif "-executive_owner" in value:
                return qs.order_by("-project__executive_owners__first_name")

        return super().filter(qs, value)


class LegalReportFilter(filters.FilterSet):
    """
    A Django FilterSet for filtering and ordering legal report (Purchase) data.

    This filter allows users to:
      - Filter reports by starred status.
      - Filter by one or more legal review statuses.
      - Perform text-based search queries.
      - Filter by associated project segment (division).
      - Filter by assigned person and roles.
      - Apply ordering based on a set of allowed fields.

    Attributes:
        starred (BooleanFilter): Filters reports that are starred by the user.
        legal_review_status (MultipleChoiceFilter): Filters by review status.
        search (CharFilter): Enables text search across reports.
        segment (MultipleChoiceFilter): Filters by project division.
        person (ModelChoiceFilter): Filters by responsible or related person.
        role (MultipleChoiceFilter): Filters by specific role.
        order_by (LegalReportOrderingFilter): Custom ordering filter.
    """

    starred: filters.BooleanFilter = filters.BooleanFilter(method="filter_starred")
    legal_review_status: filters.MultipleChoiceFilter = filters.MultipleChoiceFilter(
        field_name="legal_review_status",
        method="filter_legal_review_status",
        choices=[(choice.value, choice.value) for choice in PurchaseState],
    )
    search: filters.CharFilter = filters.CharFilter(method="filter_search")
    segment: filters.MultipleChoiceFilter = filters.MultipleChoiceFilter(
        field_name="project_division",
        choices=[],
        method="filter_segment",
    )
    person: filters.ModelChoiceFilter = filters.ModelChoiceFilter(
        queryset=User.objects.all(),
        method="filter_person",
    )
    role: filters.MultipleChoiceFilter = filters.MultipleChoiceFilter()

    order_by: LegalReportOrderingFilter = LegalReportOrderingFilter(
        fields=[
            "id",
            "project",
            "name",
            "legal_review",
            "modified",
            "primary_segment",
            "executive_owner",
        ]
    )

    class Meta:
        """Metadata class linking this filter to the Purchase model."""

        model = Purchase
        fields = ["legal_review_status"]

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        """
        Initialize the LegalReportFilter and dynamically populate
        the 'segment' filter choices with available Divisions.

        Args:
            *args: Positional arguments passed to the parent FilterSet.
            **kwargs: Keyword arguments passed to the parent FilterSet.
        """
        super().__init__(*args, **kwargs)
        self.filters["segment"].extra["choices"] = [(0, "Unassigned Segment")] + [
            (division.pk, division.name)
            for division in Division.objects.all().order_by("name")
        ]

    def filter_person(self, queryset: QuerySet, name: str, value: User) -> QuerySet:
        """
        Filter projects by person.
        If roles are also selected, only show projects where the person has those roles.
        Otherwise, show all projects where the person is a team member.
        """
        from apps.users.models import Role

        if not value:
            return queryset

        # Get selected roles if any
        selected_roles = None
        if "role" in self.data.keys():
            role_ids = self.data.getlist("role", None)
            if role_ids:
                selected_roles = Role.objects.filter(id__in=role_ids)

        # Filter by team membership
        queryset = queryset.filter(team_members=value)

        # If specific roles are selected, further filter by those roles
        if selected_roles:
            from apps.users.models import RoleAssignment

            # Check if the person actually has any of the selected roles
            person_has_roles = RoleAssignment.objects.filter(
                user=value, role__in=selected_roles
            ).exists()

            if not person_has_roles:
                # Person doesn't have any of the selected roles, return empty queryset
                return queryset.none()

            # Person has at least one of the selected roles
            # Filter projects where they have role assignments matching the selected roles
            queryset = queryset.filter(
                projectroleassignment__user=value,
                projectroleassignment__role__in=selected_roles,
            ).distinct()

        return queryset

    def filter_starred(self, queryset: QuerySet, name: str, value: bool) -> QuerySet:
        """
        Filter reports by starred status.

        Args:
            queryset (QuerySet): The initial queryset.
            name (str): The name of the filter field.
            value (bool): Whether to include only starred reports.

        Returns:
            QuerySet: Filtered queryset including only starred reports if True.
        """
        return queryset.filter(starred=True) if value else queryset

    def filter_legal_review_status(
        self, queryset: QuerySet, name: str, value: List[str]
    ) -> QuerySet:
        """
        Filter reports based on one or more legal review statuses.

        Args:
            queryset (QuerySet): The initial queryset.
            name (str): The name of the filter field.
            value (List[str]): A list of selected status values.

        Returns:
            QuerySet: Filtered queryset containing only reports matching statuses.
        """
        value = [f"PurchaseState.{val.upper()}" for val in value]
        return queryset.filter(state__in=value) if value else queryset

    def filter_search(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        """
        Perform a text search across legal report fields.

        Args:
            queryset (QuerySet): The initial queryset.
            name (str): The name of the filter field.
            value (str): The search query.

        Returns:
            QuerySet: Filtered queryset matching the search text.
        """
        return queryset.search(value)

    def filter_segment(self, queryset: QuerySet, name: str, value: Any) -> QuerySet:
        """
        Filter reports by the primary division (segment) of the associated project.

        Args:
            queryset (QuerySet): The initial queryset.
            name (str): The name of the filter field.
            value (Any): The selected division(s).

        Returns:
            QuerySet: Filtered queryset matching the selected segments.
        """
        return (
            queryset.filter(project__primary_division__in=value) if value else queryset
        )


class RaidReportOrderingFilter(filters.OrderingFilter):
    def filter(self, qs, value):
        if value:
            if "project_name" in value:
                return qs.order_by("project_name__project__name")
            elif "-project_name" in value:
                return qs.order_by("-project_name__project__name")
            elif "project_number" in value:
                return qs.order_by("project_name__project__id")
            elif "-project_number" in value:
                return qs.order_by("-project_name__project__id")
            elif "type" in value:
                return qs.order_by("type")
            elif "-type" in value:
                return qs.order_by("-type")
            elif "section" in value:
                return qs.order_by("section")
            elif "-section" in value:
                return qs.order_by("-section")
            elif "rating_priority" in value:
                return qs.order_by("rating_priority")
            elif "-rating_priority" in value:
                return qs.order_by("-rating_priority")
            elif "owner" in value:
                return qs.order_by("owner__person__full_name")
            elif "-owner" in value:
                return qs.order_by("-owner__person__full_name")
            elif "impact_date" in value:
                return qs.order_by("impact_date")
            elif "-impact_date" in value:
                return qs.order_by("-impact_date")

        return super().filter(qs, value)


class RaidReportFilter(filters.FilterSet):
    search = filters.CharFilter(method="filter_search")
    type = filters.MultipleChoiceFilter(
        choices=RaidReportOption.choices, method="filter_type"
    )
    section = filters.MultipleChoiceFilter(
        choices=SectionOption.choices, method="filter_section"
    )
    status = filters.MultipleChoiceFilter(
        choices=AllStatusOptions.choices, method="filter_status"
    )
    rating_priority = filters.MultipleChoiceFilter(
        choices=GenericOptions.choices, method="filter_rating"
    )
    owner = filters.ModelChoiceFilter(
        queryset=User.objects.all(), method="filter_person"
    )
    order_by = RaidReportOrderingFilter(
        fields=[
            "project_name",
            "project_number",
            "type",
            "section",
            "rating_priority",
            "owner",
            "impact_date",
        ]
    )

    def filter_type(self, queryset: QuerySet, name: str, value: list) -> QuerySet:
        return queryset.filter(type__in=value)

    def filter_section(self, queryset: QuerySet, name: str, value: list) -> QuerySet:
        return queryset.filter(section__in=value)

    def filter_status(self, queryset: QuerySet, name: str, value: list) -> QuerySet:
        return queryset.filter(status__in=value)

    def filter_rating(self, queryset: QuerySet, name: str, value: list) -> QuerySet:
        return queryset.filter(rating_priority__in=value)

    def filter_person(self, queryset: QuerySet, name: str, value: list) -> QuerySet:
        return queryset.filter(owner=value.id)

    def filter_search(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        return queryset.filter(
            Q(project_name__name__icontains=value)
            | Q(project_name__tags__name__icontains=value)
            | Q(project_name__summary__icontains=value)
            | Q(description__icontains=value)
        )


class ITTFilter(filters.FilterSet):
    CREATED_DAY_CHOICES = (
        ("month", "Last 30 Days"),
        ("quarter", "Last 3 Months"),
        ("half-year", "Last 6 Months"),
        ("year", "Last Year"),
    )
    END_DATE_DAY_CHOICES = (
        ("thirty", "30 Days"),
        ("sixty", "60 Days"),
        ("ninety", "90 Days"),
        ("longer", "Longer"),
    )
    starred = filters.BooleanFilter(method="filter_starred")
    health = filters.MultipleChoiceFilter(
        choices=ProjectHealth.HEALTH.choices, method="filter_health"
    )
    state = filters.MultipleChoiceFilter(
        field_name="project_state", choices=FILTER_PROJECT_STATE_CHOICES
    )
    search = filters.CharFilter(method="filter_search")
    segment = filters.MultipleChoiceFilter(
        method="filter_department",
        choices=[],  # Initialized empty, set in __init__
    )
    department_is_primary = filters.CharFilter(method="filter_department_is_primary")

    created = filters.ChoiceFilter(
        method="filter_by_created", choices=CREATED_DAY_CHOICES
    )
    end_date = filters.ChoiceFilter(
        method="filter_end_date", choices=END_DATE_DAY_CHOICES
    )

    order_by = ProjectOrderingFilter(
        fields=[
            "starred",
            "id",
            "latest_health_value",
            "name",
            "program",
            "finance_lead",
            "business_lead",
            "project_manager",
            "executive_sponsor",
            "end_date",
            "latest_percentage",
            "phase",
            "strategic_value",
            "modified",
            "primary_division",
            "created",
        ]
    )

    def __init__(self):
        super().__init__()
        self.fields["segment"].extra["choices"] = [("0", "Unassigned Segment")] + [
            (division.pk, division.name)
            for division in Division.objects.all().order_by("name")
        ]

    def filter_starred(self, queryset: QuerySet, name: str, value: bool) -> QuerySet:
        if value:
            queryset = queryset.filter(starred=True)
        return queryset

    def filter_health(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        queryset = queryset.filter(latest_health__in=value)
        return queryset

    def filter_search(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        return queryset.search(value)

    def filter_department(
        self, queryset: QuerySet, name: str, value: Iterable[str]
    ) -> QuerySet:
        filters = []
        is_unassigned = "0" in value
        if is_unassigned:
            value.remove("0")
        if len(value) > 0:
            filters.append(Q(primary_division__id__in=value))
        if "department_is_primary" in self.data.keys():
            filters.append(Q(other_involved_divisions__in=value))
        if is_unassigned:
            filters.append(Q(primary_division__isnull=True))
        return queryset.filter(reduce(operator.or_, filters))

    def filter_by_created(self, queryset, name, value):
        if value == "half-year":
            return queryset.filter(created__gte=datetime.today() - timedelta(days=180))
        elif value == "month":
            return queryset.filter(created__gte=datetime.today() - timedelta(days=30))
        elif value == "quarter":
            return queryset.filter(created__gte=datetime.today() - timedelta(days=90))
        elif value == "year":
            return queryset.filter(created__gte=datetime.today() - timedelta(days=365))
        return queryset

    def filter_end_date(self, queryset, name, value):
        if value == "sixty":
            return queryset.filter(
                end_date__lte=datetime.today() + timedelta(days=60),
                end_date__gte=datetime.today(),
            )
        elif value == "thirty":
            return queryset.filter(
                end_date__lte=datetime.today() + timedelta(days=30),
                end_date__gte=datetime.today(),
            )
        elif value == "ninety":
            return queryset.filter(
                end_date__lte=datetime.today() + timedelta(days=90),
                end_date__gte=datetime.today(),
            )
        elif value == "longer":
            return queryset.filter(end_date__gte=datetime.today())
        return queryset

    def filter_department_is_primary(
        self, queryset: QuerySet, name: str, value: str
    ) -> QuerySet:
        return queryset


class AdminReportFilter(filters.FilterSet):
    starred = filters.BooleanFilter(method="filter_starred")
    state = filters.MultipleChoiceFilter(
        field_name="project_state", choices=FILTER_PROJECT_STATE_CHOICES
    )
    search = filters.CharFilter(method="filter_search")

    person = filters.ModelChoiceFilter(
        queryset=User.objects.all(), method="filter_person"
    )
    role = filters.MultipleChoiceFilter()
    project_visibility = filters.ChoiceFilter(
        choices=TEAM_FILTER_CHOICES, method="filter_visibility"
    )

    order_by = ProjectOrderingFilter(
        fields=[
            "starred",
            "id",
            "latest_health_value",
            "name",
            "program",
            "finance_lead",
            "business_lead",
            "project_manager",
            "executive_sponsor",
            "end_date",
            "latest_percentage",
            "phase",
            "strategic_value",
            "modified",
            "primary_division",
        ]
    )

    def filter_person(self, queryset: QuerySet, name: str, value: User) -> QuerySet:
        """
        Filter projects by person.
        If roles are also selected, only show projects where the person has those roles.
        Otherwise, show all projects where the person is a team member.
        """
        if not value:
            return queryset

        # Get selected roles if any
        selected_roles = None
        if "role" in self.data.keys():
            role_ids = self.data.getlist("role", None)
            if role_ids:
                selected_roles = Role.objects.filter(id__in=role_ids)

        # Filter by team membership
        queryset = queryset.filter(team_members=value)

        # If specific roles are selected, further filter by those roles
        if selected_roles:
            from apps.users.models import RoleAssignment

            # Check if the person actually has any of the selected roles
            person_has_roles = RoleAssignment.objects.filter(
                user=value, role__in=selected_roles
            ).exists()

            if not person_has_roles:
                # Person doesn't have any of the selected roles, return empty queryset
                return queryset.none()

            # Person has at least one of the selected roles
            # Filter projects where they have role assignments matching the selected roles
            queryset = queryset.filter(
                projectroleassignment__user=value,
                projectroleassignment__role__in=selected_roles,
            ).distinct()

        return queryset

    def filter_starred(self, queryset: QuerySet, name: str, value: bool) -> QuerySet:
        if value:
            queryset = queryset.filter(starred=True)
        return queryset

    def filter_search(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        return queryset.search(value)

    def filter_visibility(self, queryset, name, value):
        if not value:
            return queryset.filter(private=True)
        if value == "all":
            return queryset.exclude(private=True)
        elif value == "project_team":
            return queryset.filter(private=True)
        elif value == "shared":
            return queryset
        return queryset
