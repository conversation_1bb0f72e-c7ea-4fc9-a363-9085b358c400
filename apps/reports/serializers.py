from django.urls import reverse
from rest_framework import serializers
from rest_framework_csv.renderers import CSVRenderer

from apps.projects.models import Project
from apps.users.models import User
from apps.projects.models.raid import RaidReport
from apps.purchases.models import Purchase


class ProjectRoadMapSerializer(serializers.ModelSerializer):
    project_state = serializers.SerializerMethodField()
    health = serializers.CharField(source="latest_health")
    complete = serializers.IntegerField(source="latest_percentage")
    executive_owners = serializers.SerializerMethodField()
    project_manager = serializers.SerializerMethodField()
    url = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = [
            "id",
            "name",
            "project_state",
            "health",
            "complete",
            "start_date",
            "end_date",
            "executive_owners",
            "project_manager",
            "url",
        ]

    def get_project_state(self, obj: Project) -> str:
        return obj.project_state_display

    def get_executive_owners(self, obj: Project) -> str:
        return ", ".join([person.full_name for person in obj.executive_owners.all()])

    def get_project_manager(self, obj: Project) -> str:
        return ", ".join([person.full_name for person in obj.project_managers.all()])

    def get_url(self, obj: Project) -> str:
        return reverse("project_detail", args=[obj.pk])


class BenefitsReportSerializer(serializers.ModelSerializer):
    total_savings = serializers.SerializerMethodField()
    savings_difference = serializers.SerializerMethodField()
    monthly_savings = serializers.SerializerMethodField()
    annual_savings_target = serializers.SerializerMethodField()
    date_range_savings = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = [
            "id",
            "name",
            "date_range_savings",
            "total_savings",
            "annual_savings_target",
            "savings_difference",
            "monthly_savings",
        ]

    def get_date_range_savings(self, obj):
        return obj.date_range_savings or 0

    def get_total_savings(self, obj):
        return obj.total_savings or 0

    def get_savings_difference(self, obj):
        return obj.savings_difference or 0

    def get_annual_savings_target(self, obj):
        return obj.annual_savings_target or 0

    def get_monthly_savings(self, obj):
        months = {}
        for projectsavings in obj.projectsavings_set.all():
            months[f"{projectsavings.month}-{projectsavings.year}"] = (
                projectsavings.savings
            )
        return months


class ExpenditureReportExportCSVRenderer(CSVRenderer):
    header = [
        "id",
        "name",
        "capital_forecast_total",
        "capital_actuals_total",
        "capital_forecast_remaining",
        "operational_forecast_total",
        "operational_actuals_total",
        "operational_forecast_remaining",
    ]
    labels = {
        "id": "Project #",
        "name": "Project Name",
        "capital_forecast_total": "Capital Total Forecast",
        "capital_actuals_total": "Captial Total Spend",
        "capital_forecast_remaining": "Capital Remaining",
        "operational_forecast_total": "OpEx Total Forecast",
        "operational_actuals_total": "OpEx Total Spend",
        "operational_forecast_remaining": "OpEx Remaining",
    }


class ExpenditureReportSerializer(serializers.ModelSerializer):
    capital_forecast_total = serializers.IntegerField()
    capital_actuals_total = serializers.IntegerField()
    capital_forecast_remaining = serializers.IntegerField()
    operational_forecast_total = serializers.IntegerField()
    operational_actuals_total = serializers.IntegerField()
    operational_forecast_remaining = serializers.IntegerField()

    class Meta:
        model = Project
        fields = [
            "id",
            "name",
            "capital_forecast_total",
            "capital_actuals_total",
            "capital_forecast_remaining",
            "operational_forecast_total",
            "operational_actuals_total",
            "operational_forecast_remaining",
        ]


class LegalReportSerializer(serializers.ModelSerializer):
    project = serializers.SerializerMethodField()
    executive_owner = serializers.SerializerMethodField()
    primary_segment = serializers.SerializerMethodField()
    legal_review_status = serializers.SerializerMethodField()
    last_updated = serializers.SerializerMethodField()

    class Meta:
        model = Purchase
        fields = [
            "id",
            "project",
            "name",
            "executive_owner",
            "primary_segment",
            "legal_review_status",
            "last_updated",
        ]

    def get_executive_owner(self, obj: Purchase) -> str:
        owner: User = obj.project.executive_owners.first()
        return owner.full_name if owner else ""

    def get_last_updated(self, obj: Purchase) -> str:
        return obj.modified.strftime("%Y-%m-%d")

    def get_legal_review_status(self, obj: Purchase) -> str:
        return obj.display_status

    def get_primary_segment(self, obj: Purchase) -> str:
        return obj.project.primary_division.name if obj.project.primary_division else ""

    def get_project(self, obj: Purchase) -> str:
        return obj.project.name


class RaidReportSerializer(serializers.ModelSerializer):
    project_number = serializers.PrimaryKeyRelatedField(queryset=Project.objects.all())
    project_name = serializers.SerializerMethodField()
    type = serializers.StringRelatedField()
    section = serializers.StringRelatedField()
    description = serializers.StringRelatedField()
    rating_priority = serializers.StringRelatedField()
    owner = serializers.StringRelatedField()
    status = serializers.StringRelatedField()

    class Meta:
        model = RaidReport
        fields = [
            "project_number",
            "project_name",
            "type",
            "section",
            "description",
            "rating_priority",
            "owner",
            "status",
        ]

    def get_project_name(self, obj) -> str:
        return str(obj)
