from dateutil.relativedelta import relativedelta
from django import forms

from apps.programs.models import Program
from apps.projects.filters import FILTER_PROJECT_STATE_CHOICES
from apps.projects.models import Project, ProjectHealth
from apps.users.models import User, Role
from apps.projects.models.raid import (
    AllStatusOptions,
    GenericOptions,
    RaidReportOption,
    SectionOption,
)
from apps.purchases.models import PurchaseState

TEAM_FILTER_CHOICES = (
    ("all", "All Members"),
    ("project_team", "Project Team Only"),
    ("shared", "Shared and Private Projects"),
)


class ComplianceReportFilterForm(forms.Form):
    STATE_CHOICES = (
        (Project.PROJECT_STATE_ACTIVE, "Active"),
        (Project.PROJECT_STATE_ON_HOLD, "On Hold"),
    )
    LAST_UPDATE_CHOICES = (
        ("", "Any Time"),
        ("lt_1_weeks", "Within 1 Week"),
        ("lt_2_weeks", "Within 2 Weeks"),
        ("gt_2_weeks", "Outside 2 Weeks"),
    )
    OVERDUE_CHOICES = ((False, "Show All"), (True, "Show Overdue"))

    starred = forms.BooleanField(required=False, widget=forms.HiddenInput())
    state = forms.MultipleChoiceField(
        choices=STATE_CHOICES, required=False, widget=forms.CheckboxSelectMultiple()
    )
    last_update = forms.ChoiceField(
        choices=LAST_UPDATE_CHOICES, required=False, widget=forms.RadioSelect()
    )
    overdue = forms.ChoiceField(
        choices=OVERDUE_CHOICES, required=False, widget=forms.RadioSelect()
    )

    program = forms.ModelChoiceField(
        queryset=Program.objects.filter(active=True).order_by("name"),
        empty_label="Show All",
        required=False,
        label="Program",
    )

    person = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name"),
        empty_label="Show All",
        required=False,
    )

    role = forms.ModelMultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        queryset=Role.objects.none(),
        required=False,
    )

    grade_min = forms.IntegerField(
        min_value=0, max_value=100, initial=0, required=False
    )
    grade_max = forms.IntegerField(
        min_value=0, max_value=100, initial=100, required=False
    )
    order_by = forms.CharField(widget=forms.HiddenInput())

    def __init__(self, *args, user=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["state"].widget.attrs.update({"clearable": True})
        if user:
            self.fields["program"].queryset = self.fields["program"].queryset.order_by(
                "name"
            )
            self.fields["role"].queryset = Role.objects.filter(company=user.company)


class BenefitsReportFilterForm(forms.Form):
    STATE_CHOICES = (
        (Project.PROJECT_STATE_ACTIVE, "Active"),
        (Project.PROJECT_STATE_ON_HOLD, "On Hold"),
        (Project.PROJECT_STATE_COMPLETE, "Complete"),
    )
    OVERDUE_CHOICES = ((False, "Show All"), (True, "Show Overdue"))

    search = forms.CharField(
        max_length=1000,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "FilterSearch-search", "placeholder": "Search Projects..."}
        ),
    )
    starred = forms.BooleanField(required=False, widget=forms.HiddenInput())
    date_after = forms.DateField(
        label="Start Date",
        input_formats=["%Y-%m-%d"],
        widget=forms.DateInput(attrs={"type": "date"}),
    )
    date_before = forms.DateField(
        label="End Date",
        input_formats=["%Y-%m-%d"],
        widget=forms.DateInput(attrs={"type": "date"}),
    )

    state = forms.MultipleChoiceField(
        choices=STATE_CHOICES, required=False, widget=forms.CheckboxSelectMultiple()
    )

    person = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name"),
        empty_label="Show All",
        required=False,
    )

    role = forms.ModelMultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        queryset=Role.objects.none(),
        required=False,
    )

    program = forms.ModelChoiceField(
        queryset=Program.objects.filter(active=True).order_by("name"),
        empty_label="Show All",
        required=False,
        label="Program",
    )

    order_by = forms.CharField(widget=forms.HiddenInput(), required=False)

    def __init__(self, *args, user=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["state"].widget.attrs.update({"clearable": True})
        if user:
            self.fields["program"].queryset = Program.objects.order_by("name")
            self.fields["role"].queryset = Role.objects.filter(company=user.company)

    def clean(self):
        cleaned_data = super().clean()

        if "date_after" in cleaned_data.keys() and "date_before" in cleaned_data.keys():
            if cleaned_data["date_after"] > cleaned_data["date_before"]:
                cleaned_data["date_before"] = cleaned_data[
                    "date_after"
                ] + relativedelta(years=1, days=1)
                data = self.data.copy()
                data["date_before"] = cleaned_data["date_before"].strftime("%Y-%m-%d")
                self.data = data

        return cleaned_data


class RoadMapReportFilterForm(forms.Form):
    STATE_CHOICES = FILTER_PROJECT_STATE_CHOICES

    search = forms.CharField(
        max_length=1000,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "FilterSearch-search", "placeholder": "Search Projects..."}
        ),
    )
    starred = forms.BooleanField(
        initial=False, required=False, widget=forms.HiddenInput()
    )
    date_after = forms.DateField(
        label="Start Date",
        input_formats=["%Y-%m-%d"],
        widget=forms.DateInput(attrs={"type": "date"}),
    )
    date_before = forms.DateField(
        label="End Date",
        input_formats=["%Y-%m-%d"],
        widget=forms.DateInput(attrs={"type": "date"}),
    )
    state = forms.MultipleChoiceField(
        choices=STATE_CHOICES, required=False, widget=forms.CheckboxSelectMultiple()
    )

    person = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name"),
        empty_label="Show All",
        required=False,
    )

    role = forms.ModelMultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        queryset=Role.objects.none(),
        required=False,
    )

    program = forms.ModelChoiceField(
        queryset=Program.objects.filter(active=True).order_by("name"),
        empty_label="Show All",
        required=False,
        label="Program",
    )

    def __init__(self, *args, user=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["state"].widget.attrs.update({"clearable": True})
        if user:
            self.fields["program"].queryset = Program.objects.order_by("name")
            self.fields["role"].queryset = Role.objects.filter(company=user.company)

    def clean(self):
        cleaned_data = super().clean()

        if "date_after" in cleaned_data.keys() and "date_before" in cleaned_data.keys():
            if cleaned_data["date_after"] > cleaned_data["date_before"]:
                cleaned_data["date_before"] = cleaned_data[
                    "date_after"
                ] + relativedelta(years=1, days=1)
                data = self.data.copy()
                data["date_before"] = cleaned_data["date_before"].strftime("%Y-%m-%d")
                self.data = data

        return cleaned_data


class ExpendituresReportFilterForm(forms.Form):
    STATE_CHOICES = FILTER_PROJECT_STATE_CHOICES
    EXPENDITURE_CHOICES = (("capital", "Capital Expenditures"), ("opex", "OpEx"))

    search = forms.CharField(
        max_length=1000,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "FilterSearch-search", "placeholder": "Search Projects..."}
        ),
    )
    starred = forms.BooleanField(required=False, widget=forms.HiddenInput())
    state = forms.MultipleChoiceField(
        choices=STATE_CHOICES, required=False, widget=forms.CheckboxSelectMultiple()
    )
    expenditure = forms.MultipleChoiceField(
        choices=EXPENDITURE_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple(),
    )
    order_by = forms.CharField(widget=forms.HiddenInput())

    program = forms.ModelChoiceField(
        queryset=Program.objects.filter(active=True).order_by("name"),
        empty_label="Show All",
        required=False,
        label="Program",
    )

    person = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name"),
        empty_label="Show All",
        required=False,
    )

    role = forms.ModelMultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        queryset=Role.objects.none(),
        required=False,
    )

    def __init__(self, *args, user=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["state"].widget.attrs.update({"clearable": True})
        self.fields["expenditure"].widget.attrs.update({"clearable": True})
        if user:
            self.fields["program"].queryset = Program.objects.order_by("name")
            self.fields["role"].queryset = Role.objects.filter(company=user.company)


LEGAL_STATUS_LABELS = {
    "PENDING": "Submitted",
    "VALIDATED": "Initial Review",
    "DRAFTED": "Submitted to Legal",
    "APPROVED": "Approved",
    "REJECTED": "Rejected",
}


class LegalReportFilterForm(forms.Form):
    STATE_CHOICES = Project.PROJECT_STATE_CHOICES
    search = forms.CharField(
        max_length=1000,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "FilterSearch-search", "placeholder": "Search Projects..."}
        ),
    )
    legal_review_status = forms.MultipleChoiceField(
        choices=[(v.value, LEGAL_STATUS_LABELS[v.name]) for v in PurchaseState],
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )
    starred = forms.BooleanField(required=False, widget=forms.HiddenInput())
    # segment = forms.MultipleChoiceField(
    #     choices=[(d.id, d.abbreviation) for d in Division.objects.all()],
    #     required=False,
    #     widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    # )
    person = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name"),
        empty_label="Show All",
        required=False,
    )
    role = forms.ModelMultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        queryset=Role.objects.none(),
        required=False,
    )
    order_by = forms.CharField(widget=forms.HiddenInput())

    def __init__(self, *args, user=None, **kwargs):
        super().__init__(*args, **kwargs)
        if user:
            self.fields["role"].queryset = Role.objects.filter(company=user.company)


class ServiceNowReportFilterForm(forms.Form):
    TYPE_CHOICES = [(1, "One"), (2, "Two")]
    SEGMENT_CHOICES = [("a", "Segment A"), ("b", "Segment B")]
    CATEGORY_CHOICES = [("x", "Category X"), ("y", "Category Y")]
    SUBCATEGORY_CHOICES = [("alpha", "Subcat-Alpha"), ("beta", "Subcat-Beta")]

    type = forms.MultipleChoiceField(
        choices=TYPE_CHOICES, required=False, widget=forms.CheckboxSelectMultiple()
    )
    segment = forms.MultipleChoiceField(
        choices=SEGMENT_CHOICES, required=False, widget=forms.CheckboxSelectMultiple()
    )
    category = forms.MultipleChoiceField(
        choices=CATEGORY_CHOICES, required=False, widget=forms.CheckboxSelectMultiple()
    )
    subcategory = forms.MultipleChoiceField(
        choices=SUBCATEGORY_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple(),
    )

    def __init__(self, *args, user=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["type"].widget.attrs.update({"clearable": True})
        self.fields["segment"].widget.attrs.update({"clearable": True})
        self.fields["category"].widget.attrs.update({"clearable": True})
        self.fields["subcategory"].widget.attrs.update({"clearable": True})


class RaidReportFilterForm(forms.Form):
    search = forms.CharField(
        max_length=1000,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "FilterSearch-search", "placeholder": "Search Projects..."}
        ),
    )

    status = forms.MultipleChoiceField(
        choices=AllStatusOptions.choices,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )
    owner = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name"),
        empty_label="Show All",
        required=False,
    )
    section = forms.MultipleChoiceField(
        choices=SectionOption.choices,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )
    type = forms.MultipleChoiceField(
        choices=RaidReportOption.choices,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )
    rating_priority = forms.MultipleChoiceField(
        choices=GenericOptions.choices,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        label="Rating/Impact",
    )
    order_by = forms.CharField(widget=forms.HiddenInput())

    def __init__(self, *args, user=None, **kwargs):
        super().__init__(*args, **kwargs)


class ITTReportFilterForm(forms.Form):
    HEALTH_CHOICES = (
        (ProjectHealth.HEALTH.GREEN, "Green"),
        (ProjectHealth.HEALTH.YELLOW, "Yellow"),
        (ProjectHealth.HEALTH.RED, "Red"),
    )
    CREATED_DAY_CHOICES = (
        ("any", "Any Time"),
        ("month", "Last 30 Days"),
        ("quarter", "Last 3 Months"),
        ("half-year", "Last 6 Months"),
        ("year", "Last Year"),
    )
    END_DATE_DAY_CHOICES = (
        ("any", "Any Time"),
        ("thirty", "30 Days"),
        ("sixty", "60 Days"),
        ("ninety", "90 Days"),
    )
    STATE_CHOICES = FILTER_PROJECT_STATE_CHOICES
    starred = forms.BooleanField(required=False, widget=forms.HiddenInput())
    health = forms.MultipleChoiceField(
        choices=HEALTH_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )
    state = forms.MultipleChoiceField(
        choices=STATE_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )
    # department = forms.MultipleChoiceField(
    #     widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    #     choices=[
    #         (division.pk, division.abbreviation)
    #         for division in Division.objects.all().order_by("name")
    #     ],
    #     label="Segment",
    #     required=False,
    # )

    department_is_primary = forms.BooleanField(
        label='Include "Other Segments" in results', required=False
    )

    search = forms.CharField(
        max_length=1000,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "FilterSearch-search", "placeholder": "Search by COO tag"}
        ),
    )

    created = forms.ChoiceField(
        choices=CREATED_DAY_CHOICES, required=False, label="Project Created within..."
    )

    end_date = forms.ChoiceField(
        choices=END_DATE_DAY_CHOICES, required=False, label="Project End Date within..."
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.fields["department_is_primary"].widget.attrs.update({"switch": True})


class AdminReportFilterForm(forms.Form):
    starred = forms.BooleanField(required=False, widget=forms.HiddenInput())

    state = forms.MultipleChoiceField(
        choices=FILTER_PROJECT_STATE_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )

    person = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name"),
        empty_label="Show All",
        required=False,
    )

    search = forms.CharField(
        max_length=1000,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "FilterSearch-search", "placeholder": "Search Projects..."}
        ),
    )

    project_visibility = forms.ChoiceField(
        choices=TEAM_FILTER_CHOICES, required=False, widget=forms.RadioSelect()
    )

    role = forms.ModelMultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        queryset=Role.objects.none(),
        required=False,
    )

    def __init__(self, *args, user=None, **kwargs):
        super().__init__(*args, **kwargs)
        if user:
            self.fields["role"].queryset = Role.objects.filter(company=user.company)
