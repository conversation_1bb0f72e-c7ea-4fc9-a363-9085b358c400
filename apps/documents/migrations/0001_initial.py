# Generated by Django 5.2.4 on 2025-10-28 00:03

import django.db.models.manager
import django_lifecycle.mixins
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Area",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("slug", models.SlugField(max_length=100)),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
            ],
            options={
                "ordering": ["weight"],
            },
        ),
        migrations.CreateModel(
            name="DocumentFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("file", models.FileField(max_length=500, upload_to="documents")),
                ("name", models.Char<PERSON>ield(max_length=255)),
                (
                    "extension",
                    models.CharField(
                        choices=[
                            ("aac", "AAC"),
                            ("ai", "AI"),
                            ("avi", "AVI"),
                            ("bmp", "BMP"),
                            ("bpg", "BPG"),
                            ("csv", "CSV"),
                            ("doc", "DOC"),
                            ("docx", "DOCX"),
                            ("exif", "EXIF"),
                            ("flac", "FLAC"),
                            ("flv", "FLV"),
                            ("gif", "GIF"),
                            ("jpeg", "JPEG"),
                            ("jpg", "JPG"),
                            ("m4a", "M4A"),
                            ("m4v", "M4V"),
                            ("mkv", "MKV"),
                            ("mov", "MOV"),
                            ("mp3", "MP3"),
                            ("mp4", "MP4"),
                            ("mpeg4", "MPEG4"),
                            ("pdf", "PDF"),
                            ("png", "PNG"),
                            ("potx", "POTX"),
                            ("ppt", "PPT"),
                            ("pptm", "PPTM"),
                            ("pptx", "PPTX"),
                            ("psd", "PSD"),
                            ("svg", "SVG"),
                            ("tar", "TAR"),
                            ("tif", "TIF"),
                            ("tiff", "TIFF"),
                            ("txt", "TXT"),
                            ("vsd", "VSD"),
                            ("vsdx", "VSDX"),
                            ("wav", "WAV"),
                            ("webm", "WEBM"),
                            ("webp", "WEBP"),
                            ("wma", "WMA"),
                            ("wmv", "WMV"),
                            ("xls", "XLS"),
                            ("xlsb", "XLSB"),
                            ("xlsm", "XLSM"),
                            ("xlsx", "XLSX"),
                            ("zip", "ZIP"),
                        ],
                        db_index=True,
                        editable=False,
                        max_length=10,
                    ),
                ),
                (
                    "media_type",
                    models.CharField(
                        choices=[
                            ("archive", "Archive"),
                            ("audio", "Audio"),
                            ("document", "Document"),
                            ("image", "Image"),
                            ("miscellaneous", "Miscellaneous"),
                            ("presentation", "Presentation"),
                            ("spreadsheet", "Spreadsheet"),
                            ("video", "Video"),
                        ],
                        db_index=True,
                        editable=False,
                        max_length=100,
                    ),
                ),
                ("size", models.PositiveIntegerField(editable=False)),
                (
                    "height",
                    models.PositiveIntegerField(blank=True, editable=False, null=True),
                ),
                (
                    "width",
                    models.PositiveIntegerField(blank=True, editable=False, null=True),
                ),
                (
                    "dpi",
                    models.PositiveIntegerField(blank=True, editable=False, null=True),
                ),
                ("checksum", models.CharField(max_length=32)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["created"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="DocumentLink",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("url", models.URLField(max_length=1000)),
                ("domain", models.CharField(max_length=1000)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="Phase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("slug", models.SlugField(max_length=100)),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
            ],
            options={
                "ordering": ["weight"],
            },
        ),
        migrations.CreateModel(
            name="Purpose",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("slug", models.SlugField(max_length=100)),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
            ],
            options={
                "ordering": ["weight"],
            },
        ),
        migrations.CreateModel(
            name="Rigor",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("slug", models.SlugField(max_length=100)),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
            ],
            options={
                "ordering": ["weight"],
            },
        ),
        migrations.CreateModel(
            name="Document",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "document_type",
                    models.CharField(
                        choices=[("FILE", "File"), ("LINK", "Link")], max_length=50
                    ),
                ),
                (
                    "state",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("PUBLISHED", "Published"),
                            ("ARCHIVED", "Archived"),
                        ],
                        max_length=50,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(max_length=250)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
                (
                    "is_file",
                    models.GeneratedField(
                        db_persist=True,
                        expression=models.Q(("document_type", "FILE")),
                        output_field=models.BooleanField(),
                    ),
                ),
                (
                    "is_link",
                    models.GeneratedField(
                        db_persist=True,
                        expression=models.Q(("document_type", "LINK")),
                        output_field=models.BooleanField(),
                    ),
                ),
                (
                    "is_published",
                    models.GeneratedField(
                        db_persist=True,
                        expression=models.Q(("state", "PUBLISHED")),
                        output_field=models.BooleanField(),
                    ),
                ),
                (
                    "is_pending",
                    models.GeneratedField(
                        db_persist=True,
                        expression=models.Q(("state", "PENDING")),
                        output_field=models.BooleanField(),
                    ),
                ),
                (
                    "is_archived",
                    models.GeneratedField(
                        db_persist=True,
                        expression=models.Q(("state", "ARCHIVED")),
                        output_field=models.BooleanField(),
                    ),
                ),
                ("area", models.ManyToManyField(to="documents.area")),
            ],
            options={
                "ordering": ["-created"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
    ]
