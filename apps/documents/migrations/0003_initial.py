# Generated by Django 5.2.4 on 2025-10-28 00:03

import auto_prefetch
import django.db.models.deletion
import django.db.models.manager
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("documents", "0002_initial"),
        ("projects", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="document",
            name="owner",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="document",
            name="segment",
            field=models.ManyToManyField(to="projects.division"),
        ),
        migrations.CreateModel(
            name="ArchivedDocument",
            fields=[],
            options={
                "abstract": False,
                "proxy": True,
                "base_manager_name": "prefetch_manager",
                "indexes": [],
                "constraints": [],
            },
            bases=("documents.document",),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="PendingDocument",
            fields=[],
            options={
                "abstract": False,
                "proxy": True,
                "base_manager_name": "prefetch_manager",
                "indexes": [],
                "constraints": [],
            },
            bases=("documents.document",),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="PublishedDocument",
            fields=[],
            options={
                "abstract": False,
                "proxy": True,
                "base_manager_name": "prefetch_manager",
                "indexes": [],
                "constraints": [],
            },
            bases=("documents.document",),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.AddField(
            model_name="documentfile",
            name="document",
            field=auto_prefetch.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to="documents.document"
            ),
        ),
        migrations.AddField(
            model_name="documentlink",
            name="document",
            field=auto_prefetch.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to="documents.document"
            ),
        ),
        migrations.AddField(
            model_name="document",
            name="phase",
            field=models.ManyToManyField(to="documents.phase"),
        ),
        migrations.AddField(
            model_name="document",
            name="purpose",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="documents.purpose"
            ),
        ),
        migrations.AddField(
            model_name="document",
            name="rigor",
            field=models.ManyToManyField(to="documents.rigor"),
        ),
        migrations.AddIndex(
            model_name="documentlink",
            index=models.Index(fields=["domain"], name="documents_d_domain_deda46_idx"),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(fields=["state"], name="documents_d_state_df10a5_idx"),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(
                fields=["document_type"], name="documents_d_documen_40c475_idx"
            ),
        ),
    ]
