from io import BytesIO
from zipfile import ZipFile

from django.contrib import messages
from django.forms import BaseModelForm, ModelForm
from django.http import HttpRequest, HttpResponse, HttpResponseRedirect
from django.urls import reverse, reverse_lazy
from django.views.generic import C<PERSON><PERSON>iew, DeleteView, ListView, UpdateView
from django.views.generic.list import BaseListView

from apps.documents.utils import group_documents_by_phase
from apps.notifications.models import Priority
from apps.utils.mixins import CompanyAdminRequiredMixin, LoginRequiredMixin
from apps.organizations.mixins import CompanyQuerysetMixin

from ..notifications.models import MessageTypes, notify
from .filters import DocumentFilter
from .forms import DocumentForm
from .models import (
    ArchivedDocument,
    Document,
    PendingDocument,
    PublishedDocument,
    StateChoices,
    DocumentTypeChoices,
)


class PublishedDocumentListView(LoginRequiredMixin, CompanyQuerysetMixin, ListView):
    queryset = (
        PublishedDocument.objects.select_related(
            "purpose", "documentlink", "documentfile"
        )
        .with_application_types()
        .order_by("name")
    )
    template_name = "documents/published_document_list.html"

    def get_context_data(self, *args, **kwargs) -> dict:
        context = super().get_context_data(*args, **kwargs)
        context["phases"] = PublishedDocument.objects.values_list(
            "phase", flat=True
        ).distinct()
        context["grouped_documents"] = group_documents_by_phase(
            documents=self.object_list
        )
        return context

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = DocumentFilter(data=self.request.GET, queryset=queryset, request=self.request).qs
        return queryset


class PendingDocumentListView(
    CompanyAdminRequiredMixin, CompanyQuerysetMixin, ListView
):
    queryset = (
        PendingDocument.objects.select_related(
            "purpose", "documentlink", "documentfile"
        )
        .with_application_types()
        .order_by("name")
    )
    template_name = "documents/pending_document_list.html"

    def get_context_data(self, *args, **kwargs) -> dict:
        context = super().get_context_data(*args, **kwargs)
        context["phases"] = PendingDocument.objects.values_list(
            "phase", flat=True
        ).distinct()
        context["grouped_documents"] = group_documents_by_phase(
            documents=self.object_list
        )
        return context

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = DocumentFilter(data=self.request.GET, queryset=queryset, request=self.request).qs
        return queryset


class ArchivedDocumentListView(
    CompanyAdminRequiredMixin, CompanyQuerysetMixin, ListView
):
    queryset = (
        ArchivedDocument.objects.select_related(
            "purpose", "documentlink", "documentfile"
        )
        .with_application_types()
        .order_by("name")
    )
    template_name = "documents/archived_document_list.html"

    def get_context_data(self, *args, **kwargs) -> dict:
        context = super().get_context_data(*args, **kwargs)
        context["phases"] = ArchivedDocument.objects.values_list(
            "phase", flat=True
        ).distinct()
        context["grouped_documents"] = group_documents_by_phase(
            documents=self.object_list
        )
        return context

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = DocumentFilter(data=self.request.GET, queryset=queryset, request=self.request).qs
        return queryset


class DocumentCreateView(LoginRequiredMixin, CreateView):
    model = Document
    form_class = DocumentForm
    template_name = "documents/document_form.html"
    success_url = reverse_lazy("published_document_list")

    def get_form_kwargs(self) -> dict:
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def form_valid(self, form: ModelForm) -> HttpResponse:
        response = super().form_valid(form)
        self.create_notifications()
        if self.request.user.is_company_admin:
            messages.success(self.request, "Document has been created.")
        else:
            messages.success(
                self.request,
                "Your document has been saved and submitted for approval.",
                extra_tags="modal",
            )
        return response

    def create_notifications(self):
        self.object: Document

        if self.object.state == StateChoices.PENDING:
            message = "{user} added <a href='{url}'>{name}</a> for review.".format(
                user=self.request.user.full_name,
                url=reverse("document_update", args=[self.object.pk]),
                name=self.object.name,
            )
            notify(
                message_type=MessageTypes.DOCUMENT_REVIEW_NEEDED,
                message=message,
                priority=Priority.HIGH,
                creator=self.request.user,
                content_object=self.object,
            )


class DocumentUpdateView(LoginRequiredMixin, CompanyQuerysetMixin, UpdateView):
    queryset = Document.objects.select_related(
        "purpose", "documentlink", "documentfile"
    )
    form_class = DocumentForm
    template_name = "documents/document_form.html"
    success_url = reverse_lazy("published_document_list")

    def get_form_kwargs(self) -> dict:
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def form_valid(self, form: ModelForm) -> HttpResponse:
        response = super().form_valid(form)
        messages.success(self.request, "Document has been updated.")
        return response


class DocumentArchiveView(LoginRequiredMixin, CompanyQuerysetMixin, DeleteView):
    queryset = Document.objects.all()
    template_name = "documents/document_confirm_archive.html"
    success_url = reverse_lazy("published_document_list")

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object: Document = self.get_object()
        success_url = self.get_success_url()
        self.object.state = StateChoices.ARCHIVED
        self.object.save()
        messages.success(request, "Document has been archived.")
        return HttpResponseRedirect(success_url)


class DocumentRestoreView(LoginRequiredMixin, CompanyQuerysetMixin, DeleteView):
    queryset = ArchivedDocument.objects.all()
    template_name = "documents/document_confirm_restore.html"
    success_url = reverse_lazy("published_document_list")

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object: ArchivedDocument = self.get_object()
        success_url = self.get_success_url()
        self.object.restore()
        messages.success(request, "Document has been restored.")
        return HttpResponseRedirect(success_url)


class DocumentDeleteView(CompanyAdminRequiredMixin, CompanyQuerysetMixin, DeleteView):
    queryset = Document.objects.all()
    template_name = "documents/document_confirm_delete.html"
    success_url = reverse_lazy("published_document_list")

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        response = super().delete(request, *args, **kwargs)
        messages.success(request, "Document has been deleted.")
        return response


class DocumentExportView(LoginRequiredMixin, CompanyQuerysetMixin, BaseListView):
    queryset = (
        PublishedDocument.objects.select_related(
            "purpose", "documentlink", "documentfile"
        )
        .with_application_types()
        .filter(document_type=DocumentTypeChoices.FILE)
        .order_by("name")
    )

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = DocumentFilter(data=self.request.GET, queryset=queryset, request=self.request).qs
        return queryset

    def get(self, request, *args, **kwargs):
        self.object_list = self.get_queryset()
        memory_file = BytesIO()
        with ZipFile(memory_file, "w") as zip_file:
            for doc in self.object_list:
                zip_file.writestr(
                    doc.documentfile.file_name, doc.documentfile.file.file.read()
                )
        memory_file.seek(0)
        response = HttpResponse(memory_file, content_type="application/zip")
        response["Content-Disposition"] = 'attachment; filename="documents.zip"'
        return response
