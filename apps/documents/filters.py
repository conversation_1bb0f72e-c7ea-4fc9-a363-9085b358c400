import operator
from functools import reduce
from typing import Iterable

import django_filters as filters
from django import forms
from django.db.models import Q
from django.db.models.query import QuerySet

from apps.projects.models import Division

from .consts import ApplicationType
from .models import Document, DocumentQuerySet, Purpose


class DocumentFilter(filters.FilterSet):
    document_search = filters.CharFilter(
        label="Search",
        method="filter_document_search",
        widget=forms.TextInput(
            attrs={"class": "PageSearch-search", "placeholder": "Search Documents..."}
        ),
    )
    application_type = filters.MultipleChoiceFilter(
        field_name="application_type",
        choices=[(member.value, member.value) for member in ApplicationType],
    )
    segment = filters.MultipleChoiceFilter(
        method="filter_segment",
        choices=[],  # Initialized empty, set in __init__
    )
    segment_is_primary = filters.CharFilter(method="filter_segment_is_primary")
    purpose = filters.ModelMultipleChoiceFilter(
        field_name="purpose",
        queryset=Purpose.objects.none(),  # Initialized empty, set in __init__
    )

    order_by = filters.OrderingFilter(fields=["name", "modified"])

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop("request", None)
        super().__init__(*args, **kwargs)
        self.form.fields["segment"].choices = self.get_segment_choices()
        self.form.fields["purpose"].queryset = self.get_purpose_queryset()

    def get_segment_choices(self):
        """
        Dynamically set segment choices based on the Division model.
        """
        try:
            # Filter divisions by user's company if request is available
            if self.request and hasattr(self.request.user, 'company'):
                divisions = Division.objects.for_company(self.request.user.company).order_by("name")
            else:
                divisions = Division.objects.all().order_by("name")

            return [("0", "Unassigned Segment")] + [
                (division.pk, division.name)
                for division in divisions
            ]
        except Exception:
            return [("0", "Unassigned Segment")]

    def get_purpose_queryset(self):
        """
        Dynamically set purpose queryset based on the Purpose model.
        """
        try:
            return Purpose.objects.all()
        except Exception:
            return Purpose.objects.none()

    class Meta:
        model = Document
        fields = ["application_type", "segment", "rigor", "area", "purpose"]

    def filter_document_search(
        self, queryset: DocumentQuerySet, name: str, value: str
    ) -> DocumentQuerySet:
        return queryset.search(value)

    def filter_segment_is_primary(
        self, queryset: QuerySet, name: str, value: str
    ) -> QuerySet:
        return queryset

    def filter_segment(
        self, queryset: QuerySet, name: str, value: Iterable[str]
    ) -> QuerySet:
        filters = []
        is_unassigned = "0" in value
        if is_unassigned:
            value.remove("0")
        if len(value) > 0:
            filters.append(Q(segment__id__in=value))
        if "segment_is_primary" in self.data.keys():
            filters.append(Q(segment__id__in=value))
        if is_unassigned:
            filters.append(Q(segment__isnull=True))
        return queryset.filter(reduce(operator.or_, filters))
