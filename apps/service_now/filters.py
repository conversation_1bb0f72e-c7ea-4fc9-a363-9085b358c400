import django_filters as filters
from django.db.models import F
from django.db.models.query import QuerySet

from apps.projects.models import Division

from .models import ServiceNowCategory, ServiceNowChangeRequest, ServiceNowSubCategory


class ServiceNowChangeRequestOrderingFilter(filters.OrderingFilter):
    def filter(self, qs, value):
        if value:
            if "-estimated_hours" in value:
                return qs.order_by(F("estimated_hours").desc(nulls_last=True))
            elif "estimated_hours" in value:
                return qs.order_by(F("estimated_hours").asc(nulls_last=True))
            elif "request_type" in value:
                return qs.order_by(F("request_type").asc(nulls_last=True))
            elif "-request_type" in value:
                return qs.order_by(F("request_type").desc(nulls_last=True))
            elif "subcategory" in value:
                return qs.order_by(F("subcategories").asc(nulls_last=True))
            elif "-subcategory" in value:
                return qs.order_by(F("subcategories").desc(nulls_last=True))
            elif "category" in value:
                return qs.order_by(F("categories").asc(nulls_last=True))
            elif "-category" in value:
                return qs.order_by(F("categories").desc(nulls_last=True))
            elif "segment" in value:
                return qs.order_by(F("segment__name").asc(nulls_last=True))
            elif "-segment" in value:
                return qs.order_by(F("segment__name").desc(nulls_last=True))
        return super().filter(qs, value)


class ServiceNowChangeRequestFilter(filters.FilterSet):
    request_type = filters.MultipleChoiceFilter(
        choices=ServiceNowChangeRequest.CHANGE_REQUEST_TYPE_CHOICES
    )

    categories = filters.ModelMultipleChoiceFilter(
        queryset=ServiceNowCategory.objects.all()
    )
    subcategories = filters.ModelMultipleChoiceFilter(
        queryset=ServiceNowSubCategory.objects.all()
    )
    segment = filters.ModelMultipleChoiceFilter(queryset=Division.objects.none())
    date_closed = filters.DateFilter(method="filter_date_closed")

    order_by = ServiceNowChangeRequestOrderingFilter(
        fields=[
            "service_now_id",
            "request_type",
            "category",
            "subcategory",
            "segment",
            "estimated_hours",
            "date_opened",
            "date_closed",
        ]
    )

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop("request", None)
        super().__init__(*args, **kwargs)

        # Filter divisions by user's company if request is available
        if self.request and hasattr(self.request.user, 'company'):
            self.filters["segment"].queryset = Division.objects.for_company(self.request.user.company)
        else:
            self.filters["segment"].queryset = Division.objects.all()

    def filter_date_closed(self, queryset: QuerySet, name: str, value) -> QuerySet:
        if value:
            month = value.month
            year = value.year
            queryset = queryset.filter(
                date_closed__year__gte=year,
                date_closed__month__gte=month,
                date_closed__year__lte=year,
                date_closed__month__lte=month,
            )
        return queryset


class ServiceNowDateFilter(filters.FilterSet):
    date_closed = filters.DateFilter(method="filter_date_closed")

    def filter_date_closed(self, queryset: QuerySet, name: str, value) -> QuerySet:
        if value:
            month = value.month
            year = value.year
            queryset = queryset.filter(
                date_closed__year__gte=year,
                date_closed__month__gte=month,
                date_closed__year__lte=year,
                date_closed__month__lte=month,
            )
        return queryset
