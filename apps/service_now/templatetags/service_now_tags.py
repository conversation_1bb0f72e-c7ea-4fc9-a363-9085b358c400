from django import template

from ..forms import ServiceNowChangeRequestFilterForm

register = template.Library()


@register.inclusion_tag(
    "service_now/partials/service_now_filter_bar.html", takes_context=True
)
def service_now_filter_bar(context):
    request = context["request"]
    form = ServiceNowChangeRequestFilterForm(request.GET, user=request.user)
    return {"form": form, "request": request}


@register.inclusion_tag(
    "service_now/partials/segment_category_selector.html", takes_context=True
)
def segment_category_selector(context):
    context["segment_button_selected"] = True
    context["category_button_selected"] = False
    if context["filtered"] and context["filters_applied"]:
        if (
            context["filters_applied"]["category_selected"]
            and not context["filters_applied"]["segment_selected"]
        ):
            context["category_button_selected"] = True
            context["segment_button_selected"] = False

    return context


@register.inclusion_tag(
    "service_now/partials/service_now_tile.html", takes_context=True
)
def service_now_tile(context, tile):
    context["is_top_ranked"] = (
        tile in context["overview_tiles"]["top_three_segments"]
        or tile in context["overview_tiles"]["top_five_categories"]
    )
    context["tile"] = tile
    return context
