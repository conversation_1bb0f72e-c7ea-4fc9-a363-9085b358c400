# Generated by Django 5.2.4 on 2025-10-28 00:03

import django.db.models.deletion
import django.db.models.manager
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("projects", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ServiceNowCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name="ServiceNowSubCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name="ServiceNowChangeRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("service_now_id", models.IntegerField()),
                (
                    "request_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("emergency", "Emergency"),
                            ("normal", "Normal"),
                            ("standard", "Standard"),
                        ],
                        max_length=255,
                        null=True,
                    ),
                ),
                ("short_description", models.CharField(max_length=1000)),
                (
                    "estimated_hours",
                    models.DecimalField(
                        blank=True, decimal_places=1, max_digits=5, null=True
                    ),
                ),
                ("date_opened", models.DateField(blank=True, null=True)),
                ("date_closed", models.DateField(blank=True, null=True)),
                (
                    "categories",
                    models.ManyToManyField(
                        blank=True, to="service_now.servicenowcategory"
                    ),
                ),
                (
                    "segment",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="projects.division",
                    ),
                ),
                (
                    "subcategories",
                    models.ManyToManyField(
                        blank=True, to="service_now.servicenowsubcategory"
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
    ]
