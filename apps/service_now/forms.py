import logging
from datetime import datetime

from dateutil.relativedelta import relativedelta
from django import forms
from django.db.models.functions import ExtractMonth, ExtractYear

from apps.projects.models import Division

from .models import ServiceNowCategory, ServiceNowChangeRequest, ServiceNowSubCategory

logger = logging.getLogger(__name__)


class ServiceNowChangeRequestDateFilterForm(forms.Form):
    date_closed = forms.ChoiceField(
        required=False,
        widget=forms.Select(attrs={"class": "Select2-filteredDatePicker"}),
    )

    def get_date_choices(self):
        this_month = datetime.today().date()
        six_months_ago = this_month + relativedelta(months=-6)
        dates = (
            ServiceNowChangeRequest.objects.order_by("date_closed")
            .filter(date_closed__gte=six_months_ago, date_closed__lt=this_month)
            .annotate(
                year=ExtractYear("date_closed"), month=ExtractMonth("date_closed")
            )
            .distinct()
            .values("year", "month")
        )
        formatted_dates = [("", "Select a date")]
        for date_element in dates:
            date_object = datetime.strptime(
                f"{date_element['month']}-{date_element['year']}", "%m-%Y"
            ).date()
            date_string = date_object.strftime("%B %Y")
            if [date_object, date_string] not in formatted_dates:
                formatted_dates.append([date_object, date_string])

        return formatted_dates

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["date_closed"].choices = self.get_date_choices()


class ServiceNowChangeRequestFilterForm(forms.Form):
    request_type = forms.MultipleChoiceField(
        choices=ServiceNowChangeRequest.CHANGE_REQUEST_TYPE_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple,
    )

    segment = forms.ModelMultipleChoiceField(
        queryset=Division.objects.none(),
        required=False,
        widget=forms.CheckboxSelectMultiple,
    )

    categories = forms.ModelMultipleChoiceField(
        queryset=ServiceNowCategory.objects.all(), required=False
    )

    subcategories = forms.ModelMultipleChoiceField(
        queryset=ServiceNowSubCategory.objects.all(), required=False
    )

    date_closed = forms.DateField(widget=forms.HiddenInput())

    order_by = forms.CharField(widget=forms.HiddenInput())

    def __init__(self, *args, **kwargs):
        user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)
        self.fields["request_type"].widget.attrs.update({"clearable": True})
        self.fields["segment"].widget.attrs.update({"clearable": True})

        # Filter divisions by user's company if user is available
        if user and hasattr(user, 'company'):
            self.fields["segment"].queryset = Division.objects.for_company(user.company)
        else:
            self.fields["segment"].queryset = Division.objects.all()


class SegmentIDListValidator(forms.Form):
    segments = forms.ModelMultipleChoiceField(queryset=Division.objects.none())

    def __init__(self, *args, **kwargs):
        user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)

        # Filter divisions by user's company if user is available
        if user and hasattr(user, 'company'):
            self.fields["segments"].queryset = Division.objects.for_company(user.company)
        else:
            self.fields["segments"].queryset = Division.objects.all()


class CategoryIDListValidator(forms.Form):
    categories = forms.ModelMultipleChoiceField(
        queryset=ServiceNowCategory.objects.all()
    )
