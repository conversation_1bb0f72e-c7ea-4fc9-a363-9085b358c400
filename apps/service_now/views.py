from django.db.models import QuerySet
from django.urls import reverse
from django.views.generic import ListView

from apps.projects.models import Division
from apps.reports.views import GenericExportListView
from apps.utils.mixins import LoginRequiredMixin

from .filters import ServiceNowChangeRequestFilter, ServiceNowDateFilter
from .forms import (
    CategoryIDListValidator,
    SegmentIDListValidator,
    ServiceNowChangeRequestDateFilterForm,
)
from .models import ServiceNowCategory, ServiceNowChangeRequest
from .serializers import ServiceNowChangeRequestSerializer


class ServiceNowView(LoginRequiredMixin, ListView):
    queryset = ServiceNowChangeRequest.objects.all()
    template_name = "service_now/service_now_dashboard.html"
    paginate_by = 10

    def get_summary_tiles(self):
        return [
            {
                "name": "Normal",
                "request_count": self.date_filtered_queryset.filter(
                    request_type=ServiceNowChangeRequest.CHANGE_REQUEST_NORMAL
                ).count(),
                "color": "blue",
                "url": f"{reverse('service_now_dashboard')}?request_type={ServiceNowChangeRequest.CHANGE_REQUEST_NORMAL}",
            },
            {
                "name": "Standard",
                "request_count": self.date_filtered_queryset.filter(
                    request_type=ServiceNowChangeRequest.CHANGE_REQUEST_STANDARD
                ).count(),
                "color": "blue",
                "url": f"{reverse('service_now_dashboard')}?request_type={ServiceNowChangeRequest.CHANGE_REQUEST_STANDARD}",
            },
            {
                "name": "Emergency",
                "request_count": self.date_filtered_queryset.filter(
                    request_type=ServiceNowChangeRequest.CHANGE_REQUEST_EMERGENCY
                ).count(),
                "color": "gold",
                "url": f"{reverse('service_now_dashboard')}?request_type={ServiceNowChangeRequest.CHANGE_REQUEST_EMERGENCY}",
            },
        ]

    def get_overview_tiles(self):
        selected_segments = self.request.GET.getlist("segment")
        selected_segment_ids = []
        if selected_segments:
            segment_id_form = SegmentIDListValidator(
                data={"segments": selected_segments}
            )
            if segment_id_form.is_valid():
                selected_segment_ids = [
                    segment.id for segment in segment_id_form.cleaned_data["segments"]
                ]
        segments = (
            Division.objects.filter(
                servicenowchangerequest__in=self.date_filtered_queryset
            )
            .with_number_of_change_requests()
            .with_selected_ids(selected_segment_ids)
        )
        top_three_segments = segments.order_by("-hours")[:3]

        selected_categories = self.request.GET.getlist("categories")
        selected_category_ids = []
        if selected_categories:
            category_id_form = CategoryIDListValidator(
                data={"categories": selected_categories}
            )
            if category_id_form.is_valid():
                selected_category_ids = [
                    category.id
                    for category in category_id_form.cleaned_data["categories"]
                ]
        categories = (
            ServiceNowCategory.objects.filter(
                servicenowchangerequest__in=self.date_filtered_queryset
            )
            .with_number_of_change_requests()
            .with_selected_ids(selected_category_ids)
        )
        top_five_categories = categories.order_by("-hours")[:5]

        return {
            "segments": segments,
            "top_three_segments": top_three_segments,
            "categories": categories,
            "top_five_categories": top_five_categories,
        }

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()

        self.date_filtered_queryset = ServiceNowDateFilter(
            self.request.GET, queryset=queryset
        ).qs.distinct()

        filtered_queryset = ServiceNowChangeRequestFilter(
            self.request.GET, queryset=self.date_filtered_queryset, request=self.request
        ).qs.distinct()

        return filtered_queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        context["summary_tiles"] = self.get_summary_tiles()
        context["overview_tiles"] = self.get_overview_tiles()
        context["date_filter_form"] = ServiceNowChangeRequestDateFilterForm(
            self.request.GET
        )

        has_filters_applied = not not self.request.GET
        context["filtered"] = has_filters_applied
        context["filters_applied"] = {
            "segment_selected": self.request.GET.get("segment", False),
            "category_selected": self.request.GET.get("categories", False),
        }

        return context


class ServiceNowExportView(GenericExportListView):
    serializer_class = ServiceNowChangeRequestSerializer
    filename = "ServiceNowChangeRequests.xlsx"
    worksheet_title = "Data"

    def get_worksheet_columns(self):
        columns = [
            ("Service Now ID #", 20),
            ("Request Type", 20),
            ("Short Description", 80),
            ("Segment", 20),
            ("Category", 20),
            ("Sub-Category", 20),
            ("Estimated Hours", 20),
            ("Date Opened", 20),
            ("Date Closed", 20),
        ]
        return columns

    def format_row(self, obj):
        row = [
            obj["service_now_id"],
            obj["request_type"],
            obj["short_description"],
            obj["segment"],
            obj["categories"],
            obj["subcategories"],
            obj["estimated_hours"],
            obj["date_opened"],
            obj["date_closed"],
        ]
        return row

    def get_queryset(self) -> QuerySet:
        queryset = ServiceNowChangeRequest.objects.all()

        date_filtered_queryset = ServiceNowDateFilter(
            self.request.GET, queryset=queryset
        ).qs.distinct()

        filtered_queryset = ServiceNowChangeRequestFilter(
            self.request.GET, queryset=date_filtered_queryset, request=self.request
        ).qs.distinct()

        self.date_filtered_queryset = date_filtered_queryset

        return filtered_queryset
