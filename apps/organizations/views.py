from typing import Any, Dict
from django.contrib import messages
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.forms import inlineformset_factory
from django.http import HttpRequest, HttpResponse
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse, reverse_lazy
from django.views.generic import ListView, CreateView, UpdateView, DeleteView

from apps.users.models import Role, ModelPermission, RoleAssignment, User
from apps.utils.mixins import CompanyAdminRequiredMixin
from .mixins import CompanyQuerysetMixin
from .forms import RoleForm


class RoleListView(CompanyAdminRequiredMixin, CompanyQuerysetMixin, ListView):
    """
    Displays a paginated list of all roles for the current company.
    Only accessible by company administrators.
    """

    model = Role
    template_name = "organizations/role_list.html"
    context_object_name = "roles"
    paginate_by = 20

    def get_queryset(self):
        """Filter roles to current user's company."""
        queryset = super().get_queryset()
        return (
            queryset.select_related("company")
            .prefetch_related("permissions__content_type", "roleassignment_set__user")
            .order_by("name")
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Role Management"
        return context


class RoleCreateView(CompanyAdminRequiredMixin, CreateView):
    """
    Handles creation of new roles for the current company.
    """

    model = Role
    form_class = RoleForm
    template_name = "organizations/role_form.html"
    success_url = reverse_lazy("role_list")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["company"] = self.request.user.company
        return kwargs

    def form_valid(self, form):
        """Automatically assign the role to the current user's company."""
        form.instance.company = self.request.user.company
        messages.success(
            self.request, f'Role "{form.instance.name}" created successfully.'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Create Role"
        context["submit_text"] = "Create Role"
        return context


class RoleUpdateView(CompanyAdminRequiredMixin, CompanyQuerysetMixin, UpdateView):
    """
    Handles updating existing roles for the current company.
    """

    model = Role
    form_class = RoleForm
    template_name = "organizations/role_form.html"
    success_url = reverse_lazy("role_list")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["company"] = self.request.user.company
        return kwargs

    def form_valid(self, form):
        messages.success(
            self.request, f'Role "{form.instance.name}" updated successfully.'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = f"Update Role: {self.object.name}"
        context["submit_text"] = "Update Role"
        return context


class RoleDeleteView(CompanyAdminRequiredMixin, CompanyQuerysetMixin, DeleteView):
    """
    Handles deletion of roles for the current company.
    """

    model = Role
    template_name = "organizations/role_confirm_delete.html"
    success_url = reverse_lazy("role_list")

    def delete(self, request, *args, **kwargs):
        role_name = self.get_object().name
        response = super().delete(request, *args, **kwargs)
        messages.success(request, f'Role "{role_name}" deleted successfully.')
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = f"Delete Role: {self.object.name}"
        # Get count of users assigned to this role
        context["assigned_users_count"] = RoleAssignment.objects.filter(
            role=self.object
        ).count()
        return context


class RoleDetailView(CompanyAdminRequiredMixin, CompanyQuerysetMixin, UpdateView):
    """
    Detailed view for managing role permissions and user assignments.
    This combines role editing with permission management and user assignment.
    """

    model = Role
    template_name = "organizations/role_detail.html"
    fields = ["name", "description"]

    def get_success_url(self):
        return reverse("role_detail", kwargs={"pk": self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        role = self.object

        # Get all available content types (models) for permission assignment
        available_content_types = ContentType.objects.filter(
            app_label__in=[
                "projects",
                "users",
                "organizations",
                "ideas",
                "programs",
                "locations",
                "planner",
                "reports",
            ]
        ).order_by("app_label", "model")

        # Get current permissions for this role
        current_permissions = {
            perm.content_type_id: perm
            for perm in ModelPermission.objects.filter(role=role)
        }

        # Get users in the same company for assignment
        company_users = User.objects.filter(
            company=role.company, is_active=True
        ).order_by("first_name", "last_name")

        # Get currently assigned users
        assigned_users = User.objects.filter(roleassignment__role=role).order_by(
            "first_name", "last_name"
        )

        context.update(
            {
                "page_title": f"Manage Role: {role.name}",
                "available_content_types": available_content_types,
                "current_permissions": current_permissions,
                "company_users": company_users,
                "assigned_users": assigned_users,
                "submit_text": "Update Role",
            }
        )
        return context

    @transaction.atomic
    def post(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse:
        """Handle role update, permission changes, and user assignments."""
        self.object = self.get_object()

        # Handle basic role form
        form = self.get_form()
        if form.is_valid():
            form.save()

            # Handle permission updates
            self._update_permissions(request)

            # Handle user assignments
            self._update_user_assignments(request)

            messages.success(
                request, f'Role "{self.object.name}" updated successfully.'
            )
            return redirect(self.get_success_url())
        else:
            return self.form_invalid(form)

    def _update_permissions(self, request: HttpRequest) -> None:
        """Update model permissions for this role based on form data."""
        role = self.object

        # Clear existing permissions
        ModelPermission.objects.filter(role=role).delete()

        # Process new permissions
        for key, value in request.POST.items():
            if key.startswith("permission_") and value == "on":
                # Parse permission key: permission_{content_type_id}_{permission_type}
                parts = key.split("_")
                if len(parts) == 3:
                    content_type_id = int(parts[1])
                    permission_type = parts[2]

                    # Get or create ModelPermission for this content type
                    permission, created = ModelPermission.objects.get_or_create(
                        role=role,
                        content_type_id=content_type_id,
                        defaults={
                            "can_create": False,
                            "can_read": False,
                            "can_update": False,
                            "can_delete": False,
                        },
                    )

                    # Set the specific permission
                    setattr(permission, f"can_{permission_type}", True)
                    permission.save()

    def _update_user_assignments(self, request: HttpRequest) -> None:
        """Update user assignments for this role based on form data."""
        role = self.object

        # Get selected user IDs from form
        selected_user_ids = request.POST.getlist("assigned_users")
        selected_user_ids = [int(uid) for uid in selected_user_ids if uid.isdigit()]

        # Clear existing assignments
        RoleAssignment.objects.filter(role=role).delete()

        # Create new assignments
        for user_id in selected_user_ids:
            try:
                user = User.objects.get(id=user_id, company=role.company)
                RoleAssignment.objects.create(role=role, user=user)
            except User.DoesNotExist:
                continue  # Skip invalid user IDs
