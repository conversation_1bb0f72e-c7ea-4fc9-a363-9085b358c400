import auto_prefetch
from django.db import models


class Company(auto_prefetch.Model):
    name = models.CharField(max_length=255, unique=True)
    logo = models.FileField(upload_to="company_logos/", null=True, blank=True)

    def __str__(self):
        return self.name


class CompanyAdminManager(auto_prefetch.Manager):
    """
    Custom manager for CompanyAdmin model with helper methods
    for validating and retrieving company admins.
    """

    def get_queryset(self) -> models.QuerySet:
        """
        Returns the base queryset with no filters applied.
        """
        return super().get_queryset()

    def active(self) -> models.QuerySet:
        """
        Returns only active company admin records.
        """
        return self.get_queryset().filter(is_active=True)

    def for_user(self, user) -> models.QuerySet:
        """
        Returns all active CompanyAdmin records for the given user.
        """
        return self.active().filter(user=user)

    def is_company_admin(self, user, company: "Company") -> bool:
        """
        Checks if the given user is an active admin of the given company.
        """
        return self.active().filter(user=user, company=company).exists()


class CompanyAdmin(auto_prefetch.Model):
    company = models.ForeignKey(
        "organizations.Company", on_delete=models.CASCADE, related_name="admins"
    )
    user = models.ForeignKey(
        "users.User", on_delete=models.CASCADE, related_name="company_admins"
    )
    is_active = models.BooleanField(default=True)

    objects = CompanyAdminManager()

    def __str__(self) -> str:
        return f"{self.user.email} - {self.company.name}"


class AuthenticatedDomains(auto_prefetch.Model):
    """
    Domains that are authorized to access our site and that we have a domain for.
    """

    company = models.ForeignKey(
        Company, on_delete=models.CASCADE, related_name="domains"
    )

    domain = models.CharField(max_length=255, unique=True)

    def __str__(self):
        return self.domain
