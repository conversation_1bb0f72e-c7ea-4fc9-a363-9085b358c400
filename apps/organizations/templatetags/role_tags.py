from django import template

register = template.Library()


@register.filter
def get_permission(permissions_dict, content_type_id):
    """
    Get permission object for a specific content type ID.
    Usage: {{ current_permissions|get_permission:content_type.id }}
    """
    return permissions_dict.get(content_type_id)


@register.filter
def has_permission(permission_obj, permission_type):
    """
    Check if a permission object has a specific permission type.
    Usage: {{ permission|has_permission:"create" }}
    """
    if not permission_obj:
        return False
    return getattr(permission_obj, f"can_{permission_type}", False)


@register.simple_tag
def permission_checked(current_permissions, content_type_id, permission_type):
    """
    Template tag to check if a permission checkbox should be checked.
    Usage: {% permission_checked current_permissions content_type.id "create" %}
    """
    permission_obj = current_permissions.get(content_type_id)
    if not permission_obj:
        return ""

    if getattr(permission_obj, f"can_{permission_type}", False):
        return "checked"
    return ""
