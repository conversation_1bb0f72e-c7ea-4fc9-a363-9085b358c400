from typing import Any
from typing import Type


from django.contrib.auth.views import redirect_to_login
from django.core.exceptions import PermissionDenied, ImproperlyConfigured
from django.db import models
from django.db.models import QuerySet
from django.http import HttpRequest
from django.shortcuts import redirect
from django.template.response import TemplateResponse

from apps.organizations.models import Company


class CompanyQuerysetMixin:
    """
    A reusable mixin that restricts querysets to the company
    of the currently logged-in company admin.
    """

    def get_company(self) -> Company:
        """
        Returns the company for the current user if they have a company.
        """
        user = self.request.user

        return user.company

    def filter_queryset_by_company(self, queryset: QuerySet) -> QuerySet:
        """
        Restricts the queryset to the company of the current user
        (unless the user is a superuser).
        """
        company = self.get_company()
        if company:
            return queryset.filter(company=company)
        return queryset

    def get_queryset(self) -> QuerySet:
        """
        Overrides the queryset from the parent view to restrict it
        to the company of the current user.
        """
        queryset = super().get_queryset()
        return self.filter_queryset_by_company(queryset)


class RolePermissionMixin:
    """
    A reusable mixin that enforces company-role-based access control for views.

    This mixin checks whether the authenticated user has permission to
    access the given view based on their role within their company.

    It expects that:
      • The user model has a `company` foreign key.
      • The `Role` model defines permissions per model via ModelPermission.
      • Users have roles assigned via RoleAssignment.

    Usage:
        class ProjectListView(RolePermissionMixin, ListView):
            model = Project
            required_permission = "read"  # "create", "read", "update", "delete"
    """

    required_permission: str | None = None  # e.g. "read", "create", "update", "delete"

    # Optional: Custom error handling properties
    permission_denied_template: str | None = None
    permission_denied_redirect_url: str | None = None
    permission_denied_context: dict | None = None

    def dispatch(self, request: HttpRequest, *args: Any, **kwargs: Any):
        """
        Check permissions before dispatching the request.
        This runs after the request is attached to self.
        """
        self.request = request
        try:
            self._validate_permission_access()
        except PermissionDenied:
            return self.handle_no_permission()
        return super().dispatch(request, *args, **kwargs)

    def handle_no_permission(self):
        """
        Handle permission denied cases with custom templates or redirects.
        Can be overridden by subclasses for specific behavior.
        """
        # If user is anonymous, redirect to login
        if self.request.user.is_anonymous:
            return redirect_to_login(self.request.get_full_path())

        # Use custom redirect URL if specified
        if self.permission_denied_redirect_url:
            return redirect(self.permission_denied_redirect_url)

        # Use custom template if specified
        if self.permission_denied_template:
            context = self.permission_denied_context or {}
            return TemplateResponse(
                request=self.request,
                template=self.permission_denied_template,
                context=context,
            )

        # Default behavior - raise PermissionDenied
        raise PermissionDenied

    # ----------------------------------------------------------------------
    # Core validation methods
    # ----------------------------------------------------------------------

    def get_model(self) -> Type[models.Model]:
        """
        Returns the model associated with this view.

        Must be implemented by the subclass or have a `model` attribute defined.
        Raises:
            ImproperlyConfigured: If no model is defined or returned.
        """
        if hasattr(self, "model") and self.model is not None:
            return self.model
        raise ImproperlyConfigured(
            f"{self.__class__.__name__} must define a `model` attribute or override `get_model()`."
        )

    def _validate_permission_access(self) -> None:
        """
        Validates whether the current user has permission to access this view.

        Raises:
            PermissionDenied: If the user does not have the required role permission.
        """
        user = self.request.user

        # 1️⃣ Superusers always have full access
        if getattr(user, "is_superuser", False):
            return

        # 2️⃣ Company admins have full access
        if getattr(user, "is_company_admin", False):
            return

        # 3️⃣ Check authentication and company membership
        if not user.is_authenticated or not getattr(user, "company", None):
            raise PermissionDenied(
                "You must belong to a company to access this resource."
            )

        # 4️⃣ Ensure required_permission is defined
        if not self.required_permission:
            raise ImproperlyConfigured(
                f"{self.__class__.__name__} must define `required_permission` (e.g. 'read', 'create', 'update', 'delete')."
            )

        # 5️⃣ Check role-based permission
        model_class = self.get_model()
        if not self._has_role_permission(user, model_class, self.required_permission):
            raise PermissionDenied(
                f"You do not have permission to {self.required_permission} {model_class.__name__}."
            )

    # ----------------------------------------------------------------------
    # Role-based checks
    # ----------------------------------------------------------------------

    def _has_role_permission(
        self,
        user: Any,
        model_class: Type[models.Model],
        permission: str,
    ) -> bool:
        """
        Determines if the user's role grants access to the specified model permission.

        Checks the ModelPermission table for any role the user has that grants
        the requested permission on the given model.

        Args:
            user: The authenticated user
            model_class: The model class to check permissions for
            permission: The permission type ("create", "read", "update", "delete")

        Returns:
            True if the user has permission, False otherwise.
        """
        from django.contrib.contenttypes.models import ContentType
        from apps.users.models import ModelPermission, RoleAssignment

        # Get the ContentType for this model
        try:
            content_type = ContentType.objects.get_for_model(model_class)
        except ContentType.DoesNotExist:
            return False

        # Map permission name to field name
        permission_field = f"can_{permission}"

        # Get all roles for this user within their company
        user_roles = (
            RoleAssignment.objects.filter(user=user, role__company=user.company)
            .values_list("role_id", flat=True)
            .distinct()
        )

        if not user_roles:
            # User has no roles assigned
            return False

        # Check if any of the user's roles has the required permission
        return ModelPermission.objects.filter(
            role_id__in=user_roles,
            content_type=content_type,
            **{permission_field: True},
        ).exists()
