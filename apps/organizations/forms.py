from django import forms
from django.contrib.contenttypes.models import ContentType

from apps.users.models import Role, ModelPermission, RoleAssignment, User


class RoleForm(forms.ModelForm):
    """
    Form for creating and updating roles.
    """

    class Meta:
        model = Role
        fields = ["name", "description"]
        widgets = {
            "name": forms.TextInput(
                attrs={"class": "Form-input", "placeholder": "Enter role name"}
            ),
            "description": forms.Textarea(
                attrs={
                    "class": "Form-textarea",
                    "rows": 3,
                    "placeholder": "Enter role description (optional)",
                }
            ),
        }

    def __init__(self, company=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.company = company

        # Make name field required and add validation
        self.fields["name"].required = True
        self.fields["description"].required = False

    def clean_name(self):
        """Ensure role name is unique within the company."""
        name = self.cleaned_data.get("name")
        if not name:
            return name

        # Check for existing role with same name in company
        existing_roles = Role.objects.filter(name__iexact=name, company=self.company)

        # Exclude current instance if updating
        if self.instance and self.instance.pk:
            existing_roles = existing_roles.exclude(pk=self.instance.pk)

        if existing_roles.exists():
            raise forms.ValidationError(
                f'A role with the name "{name}" already exists in your company.'
            )

        return name


class RolePermissionForm(forms.Form):
    """
    Form for managing permissions for a specific role.
    This form dynamically generates permission fields based on available content types.
    """

    def __init__(self, role=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.role = role

        if role:
            self._setup_permission_fields()

    def _setup_permission_fields(self):
        """Setup permission fields for each available content type."""
        # Get available content types
        available_content_types = ContentType.objects.filter(
            app_label__in=[
                "projects",
                "users",
                "organizations",
                "ideas",
                "programs",
                "locations",
                "planner",
                "reports",
            ]
        ).order_by("app_label", "model")

        # Get current permissions for this role
        current_permissions = {
            perm.content_type_id: perm
            for perm in ModelPermission.objects.filter(role=self.role)
        }

        # Create permission fields for each content type
        for content_type in available_content_types:
            field_prefix = f"permission_{content_type.id}"
            current_perm = current_permissions.get(content_type.id)

            # Create individual permission checkboxes
            for perm_type in ["create", "read", "update", "delete"]:
                field_name = f"{field_prefix}_{perm_type}"
                initial_value = False

                if current_perm:
                    initial_value = getattr(current_perm, f"can_{perm_type}", False)

                self.fields[field_name] = forms.BooleanField(
                    required=False,
                    initial=initial_value,
                    label=f"{perm_type.title()} {content_type.model.title()}",
                    widget=forms.CheckboxInput(
                        attrs={
                            "class": "Form-checkbox",
                            "data-content-type": content_type.id,
                            "data-permission": perm_type,
                        }
                    ),
                )


class RoleAssignmentForm(forms.Form):
    """
    Form for assigning users to a role.
    """

    assigned_users = forms.ModelMultipleChoiceField(
        queryset=User.objects.none(),
        widget=forms.CheckboxSelectMultiple(attrs={"class": "Form-checkbox-list"}),
        required=False,
        label="Assign Users to Role",
        help_text="Select users who should have this role.",
    )

    def __init__(self, role=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.role = role

        if role and role.company:
            # Filter users to same company
            self.fields["assigned_users"].queryset = User.objects.filter(
                company=role.company, is_active=True
            ).order_by("first_name", "last_name")

            # Set initial values to currently assigned users
            currently_assigned = User.objects.filter(
                roleassignment__role=role
            ).values_list("id", flat=True)

            self.fields["assigned_users"].initial = list(currently_assigned)


class RoleFilterForm(forms.Form):
    """
    Form for filtering roles in the list view.
    """

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "Form-input",
                "placeholder": "Search roles by name or description...",
            }
        ),
        label="Search",
    )

    has_users = forms.ChoiceField(
        choices=[
            ("", "All Roles"),
            ("yes", "Roles with Users"),
            ("no", "Roles without Users"),
        ],
        required=False,
        widget=forms.Select(attrs={"class": "Form-select"}),
        label="User Assignment Status",
    )

    has_permissions = forms.ChoiceField(
        choices=[
            ("", "All Roles"),
            ("yes", "Roles with Permissions"),
            ("no", "Roles without Permissions"),
        ],
        required=False,
        widget=forms.Select(attrs={"class": "Form-select"}),
        label="Permission Status",
    )
