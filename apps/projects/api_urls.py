from django.urls import path, re_path

from .api_views import (
    PersonDetailAPIView,
    PersonDetailByEmailAPIView,
    PersonEmailAvailableAPIView,
    PersonListCreateAPIView,
    ProjectCreateAPIView,
    ProjectDetailAPIView,
    ProjectListAPIView,
    ProjectListAPIView2,
    ProjectUpdateAPIView,
    RaidItemAPIView,
)

urlpatterns = [
    path("", ProjectListAPIView.as_view(), name="api_project_list"),
    path("project-list/", ProjectListAPIView2.as_view(), name="api_project_list_2"),
    path("raid/", RaidItemAPIView.as_view(), name="raid_api_view"),
    path("create/", ProjectCreateAPIView.as_view(), name="api_project_create"),
    path("<int:pk>/", ProjectDetailAPIView.as_view(), name="api_project_detail"),
    path("<int:pk>/update/", ProjectUpdateAPIView.as_view(), name="api_project_update"),
    path("people/", PersonListCreateAPIView.as_view(), name="api_person_list_create"),
    path("people/<int:pk>/", PersonDetailAPIView.as_view(), name="api_person_detail"),
    re_path(
        r"^people/(?P<email>[\w.%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4})/$",
        PersonDetailByEmailAPIView.as_view(),
        name="api_person_by_email_detail",
    ),
    re_path(
        r"^people/email_available/(?P<email>[\w.%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4})/$",
        PersonEmailAvailableAPIView.as_view(),
        name="api_person_email_available",
    ),
]
