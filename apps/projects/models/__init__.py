from __future__ import annotations
import json
import auto_prefetch
import operator
import re
from collections import Counter
from datetime import date, datetime, timedelta
from functools import reduce
from typing import Iterable, List, Optional, Set, Union, cast

import reversion
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import GenericRelation
from django.db import models, transaction
from django.db.models import (
    Case,
    Count,
    Exists,
    ExpressionWrapper,
    F,
    OuterRef,
    Q,
    Subquery,
    Sum,
    Transform,
    Value,
    When,
)
from django.db.models.functions import Coalesce, Least
from django.db.models.query import QuerySet
from django.urls import reverse
from django.utils import timezone
from django_lifecycle import AFTER_SAVE, BEFORE_SAVE, LifecycleModelMixin, hook
from taggit.managers import TaggableManager

from apps.attachments.models import Attachment
from apps.comments.models import Comment, Commentable
from apps.links.models import Link
from apps.locations.models import Location
from apps.utils.calendar import month_year_iter


from .business_segment import BusinessSegment
from .business_segment_person import BusinessSegmentPerson
from .division import Division
from .pillars import StrategicPillar, SubPillar
from .pod import Pod
from .project_attachment import ProjectAttachment
from .project_executive_action import ProjectExecutiveAction
from .project_expenditures import (
    ProjectCapitalExpenditure,
    ProjectCapitalExpenditureManager,
    ProjectOperationalExpenditure,
)
from .project_health import ProjectHealth
from .project_impacts import ProjectDivisionImpact, ProjectSupportImpact
from .project_import_data import ProjectImportData
from .project_in_scope import InScope
from .project_link import ProjectLink
from .project_measure_of_success import MeasureOfSuccess
from .project_operations_need import ProjectOperationsNeed
from .project_out_of_scope import OutOfScope
from .project_percent_complete import ProjectPercentComplete
from .project_planned_activity import ProjectPlannedActivity
from .project_recent_accomplishment import ProjectRecentAccomplishment
from .project_risk import ProjectRisk
from .project_savings import ProjectSavings
from .project_type import ProjectType
from .starred_project import StarredProject
from .utils import WeeklyModel

User = get_user_model()


class Ceil(Transform):
    function = "CEILING"
    lookup_name = "ceil"


DEFAULT_PERIOD = 30


class ProjectQuerySet(QuerySet):
    def search(self, query: str) -> ProjectQuerySet:
        quoted_re = re.compile('"(.*?)"')
        keywords = re.findall(quoted_re, query)
        keywords += re.sub(quoted_re, "", query).split()
        params = []
        fields = ("name", "summary", "tags__name")
        for keyword in keywords:
            for field in fields:
                params.append(Q(**{field + "__icontains": keyword}))
        queryset = self.filter(reduce(operator.or_, params))
        return cast(ProjectQuerySet, queryset)

    def filtered_for_project(self, only_active: bool = True) -> ProjectQuerySet:
        queryset = self.exclude(
            Q(phase=Project.PHASE_IDEA) | Q(phase=Project.PHASE_PROPOSE)
        )
        if only_active:
            queryset = queryset.filter(active=True)
        return cast(ProjectQuerySet, queryset)

    def with_latest_percentage(self) -> ProjectQuerySet:
        queryset = self.annotate(
            latest_percentage=Subquery(
                ProjectPercentComplete.objects.filter(project=OuterRef("pk"))
                .order_by("-year", "-week")
                .values("percentage")[:1]
            )
        )
        return cast(ProjectQuerySet, queryset)

    def with_latest_health(self) -> ProjectQuerySet:
        queryset = self.annotate(
            latest_health=Subquery(
                ProjectHealth.objects.filter(project=OuterRef("pk"))
                .order_by("-year", "-week")
                .values("health")[:1]
            )
        )
        return cast(ProjectQuerySet, queryset)

    def with_savings_calculations(
        self, start_date: date = None, end_date: date = None
    ) -> ProjectQuerySet:
        if not end_date:
            end_date = date.today()
        if not start_date:
            start_date = end_date - relativedelta(years=1)

        project_savings = (
            ProjectSavings.objects.exclude(year__lt=start_date.year)
            .exclude(year__gt=end_date.year)
            .exclude(month__lt=start_date.month, year=start_date.year)
            .exclude(month__gt=end_date.month, year=end_date.year)
        )

        queryset = (
            self.annotate(total_savings=Sum("projectsavings__savings"))
            .annotate(
                date_range_savings=Sum(
                    "projectsavings__savings",
                    filter=(Q(projectsavings__in=project_savings)),
                )
            )
            .annotate(
                savings_difference=ExpressionWrapper(
                    F("total_savings") - F("annual_savings_target"),
                    output_field=models.IntegerField(),
                )
            )
            .filter(
                Q(savings_start_date__isnull=False) & Q(savings_end_date__isnull=False)
            )
        )

        return cast(ProjectQuerySet, queryset)

    def with_latest_health_value(self) -> ProjectQuerySet:
        whens = [
            When(health=ProjectHealth.HEALTH.GREEN, then=Value(1)),
            When(health=ProjectHealth.HEALTH.YELLOW, then=Value(2)),
            When(health=ProjectHealth.HEALTH.RED, then=Value(3)),
        ]

        queryset = self.annotate(
            latest_health_value=Subquery(
                ProjectHealth.objects.filter(project=OuterRef("pk"))
                .annotate(
                    health_value=Case(
                        *whens, default=Value(0), output_field=models.IntegerField()
                    )
                )
                .order_by("-year", "-week")
                .values("health_value")[:1]
            )
        )
        return cast(ProjectQuerySet, queryset)

    def with_health_history(self, period: int = DEFAULT_PERIOD) -> ProjectQuerySet:
        queryset = self.annotate(
            health_history_1=Subquery(
                ProjectHealth.objects.filter(
                    project=OuterRef("pk"),
                    modified__gte=timezone.now() - timedelta(days=period),
                )
                .order_by("-modified")
                .values("health")[:1]
            ),
            health_history_2=Subquery(
                ProjectHealth.objects.filter(
                    project=OuterRef("pk"),
                    modified__range=(
                        timezone.now() - timedelta(days=period * 2),
                        timezone.now() - timedelta(days=period),
                    ),
                )
                .order_by("-modified")
                .values("health")[:1]
            ),
            health_history_3=Subquery(
                ProjectHealth.objects.filter(
                    project=OuterRef("pk"),
                    modified__range=(
                        timezone.now() - timedelta(days=period * 3),
                        timezone.now() - timedelta(days=period * 2),
                    ),
                )
                .order_by("-modified")
                .values("health")[:1]
            ),
        )
        return cast(ProjectQuerySet, queryset)

    def with_ready_to_convert(self):
        forty_five_days = (datetime.today() + timedelta(days=45)).date()

        queryset = self.annotate(has_executive_owners=Count("executive_owners"))

        when = When(
            ready_to_begin=True,
            committed_to_spend=True,
            summary__isnull=False,
            start_date__lte=forty_five_days,
            has_executive_owners__gt=0,
            has_technology_components__in=[True, False],
            then=True,
        )

        queryset = queryset.annotate(
            ready_to_convert=Case(
                when, output_field=models.BooleanField(), default=False
            )
        )
        return queryset

    def with_starred_by_user(self, user: User) -> ProjectQuerySet:
        queryset = self.annotate(
            starred=Exists(
                StarredProject.objects.filter(project=OuterRef("pk"), starred_by=user)
            )
        )
        return cast(ProjectQuerySet, queryset)

    def with_division_impact_total(self) -> ProjectQuerySet:
        increase_revenue_whens = [
            When(impact_increase_revenue=False, then=Value(0)),
            When(impact_increase_revenue=True, then=Value(1)),
        ]
        decrease_operational_cost_whens = [
            When(impact_decrease_operational_cost=False, then=Value(0)),
            When(impact_decrease_operational_cost=True, then=Value(1)),
        ]
        increase_productivity_whens = [
            When(impact_increase_productivity=False, then=Value(0)),
            When(impact_increase_productivity=True, then=Value(1)),
        ]
        improve_quality_whens = [
            When(impact_improve_quality=False, then=Value(0)),
            When(impact_improve_quality=True, then=Value(1)),
        ]
        mitigate_risk_whens = [
            When(impact_mitigate_risk=False, then=Value(0)),
            When(impact_mitigate_risk=True, then=Value(1)),
        ]
        division_impact_subquery = Subquery(
            ProjectDivisionImpact.objects.filter(project=OuterRef("pk"))
            .annotate(
                impact_increase_revenue_value=Case(
                    *increase_revenue_whens, output_field=models.IntegerField()
                )
            )
            .annotate(
                impact_decrease_operational_cost_value=Case(
                    *decrease_operational_cost_whens, output_field=models.IntegerField()
                )
            )
            .annotate(
                impact_increase_productivity_value=Case(
                    *increase_productivity_whens, output_field=models.IntegerField()
                )
            )
            .annotate(
                impact_improve_quality_value=Case(
                    *improve_quality_whens, output_field=models.IntegerField()
                )
            )
            .annotate(
                impact_mitigate_risk_value=Case(
                    *mitigate_risk_whens, output_field=models.IntegerField()
                )
            )
            .annotate(
                division_total=ExpressionWrapper(
                    F("impact_increase_revenue_value")
                    + F("impact_decrease_operational_cost_value")
                    + F("impact_increase_productivity_value")
                    + F("impact_improve_quality_value")
                    + F("impact_mitigate_risk_value"),
                    output_field=models.IntegerField(),
                )
            )
            .order_by()
            .values("project")
            .annotate(total=Sum("division_total"))
            .values("total")
        )
        queryset = self.annotate(
            division_impact_total=Coalesce(division_impact_subquery, 0)
        )
        return cast(ProjectQuerySet, queryset)

    def with_support_impact_total(self) -> ProjectQuerySet:
        impact_whens = [
            When(impact=ProjectSupportImpact.IMPACT_NONE, then=Value(0)),
            When(impact=ProjectSupportImpact.IMPACT_SINGLE_TEAM, then=Value(1)),
            When(impact=ProjectSupportImpact.IMPACT_MULTIPLE_TEAMS, then=Value(2)),
            When(impact=ProjectSupportImpact.IMPACT_2_TO_3_DEPARTMENTS, then=Value(3)),
            When(impact=ProjectSupportImpact.IMPACT_4_TO_5_DEPARTMENTS, then=Value(4)),
            When(impact=ProjectSupportImpact.IMPACT_6_PLUS_DEPARTMENTS, then=Value(5)),
        ]
        support_impact_subquery = Subquery(
            ProjectSupportImpact.objects.filter(project=OuterRef("pk"))
            .annotate(
                support_total=Case(*impact_whens, output_field=models.IntegerField())
            )
            .order_by()
            .values("support_total")
        )
        queryset = self.annotate(
            support_impact_total=Coalesce(support_impact_subquery, 0)
        )
        return cast(ProjectQuerySet, queryset)

    def with_impact_total(self) -> ProjectQuerySet:
        queryset = (
            self.with_division_impact_total()
            .with_support_impact_total()
            .annotate(
                impact_total=ExpressionWrapper(
                    F("division_impact_total") + F("support_impact_total"),
                    output_field=models.IntegerField(),
                )
            )
        )
        return cast(ProjectQuerySet, queryset)

    def with_strategic_value(self) -> ProjectQuerySet:
        funding_size_whens = [
            When(funding_size=Project.MONEY_AMOUNT_GREATER_THAN_5M, then=Value(1)),
            When(funding_size=Project.MONEY_AMOUNT_1M_TO_5M, then=Value(2)),
            When(funding_size=Project.MONEY_AMOUNT_250K_TO_1M, then=Value(3)),
            When(funding_size=Project.MONEY_AMOUNT_25K_TO_250K, then=Value(4)),
            When(funding_size=Project.MONEY_AMOUNT_LESS_THAN_25k, then=Value(5)),
            When(funding_size=Project.MONEY_AMOUNT_NONE, then=Value(5)),
        ]
        annualized_savings_whens = [
            When(
                annualized_savings=Project.MONEY_AMOUNT_GREATER_THAN_5M, then=Value(5)
            ),
            When(annualized_savings=Project.MONEY_AMOUNT_1M_TO_5M, then=Value(4)),
            When(annualized_savings=Project.MONEY_AMOUNT_250K_TO_1M, then=Value(3)),
            When(annualized_savings=Project.MONEY_AMOUNT_25K_TO_250K, then=Value(2)),
            When(annualized_savings=Project.MONEY_AMOUNT_LESS_THAN_25k, then=Value(1)),
            When(annualized_savings=Project.MONEY_AMOUNT_NONE, then=Value(1)),
        ]
        payback_period_whens = [
            When(
                payback_period=Project.PAYBACK_PERIOD_GREATER_THAN_3_YEARS,
                then=Value(1),
            ),
            When(payback_period=Project.PAYBACK_PERIOD_2_TO_3_YEARS, then=Value(2)),
            When(payback_period=Project.PAYBACK_PERIOD_1_TO_2_YEARS, then=Value(3)),
            When(payback_period=Project.PAYBACK_PERIOD_LESS_THAN_1_YEAR, then=Value(4)),
            When(payback_period=Project.PAYBACK_PERIOD_NONE, then=Value(4)),
        ]
        priority_whens = [
            When(priority=Project.PRIORITY_REGULATORY, then=Value(4)),
            When(priority=Project.PRIORITY_HIGH, then=Value(3)),
            When(priority=Project.PRIORITY_MEDIUM, then=Value(2)),
            When(priority=Project.PRIORITY_LOW, then=Value(1)),
        ]
        complexity_whens = [
            When(complexity=Project.COMPLEXITY_HIGH, then=Value(3)),
            When(complexity=Project.COMPLEXITY_MEDIUM, then=Value(2)),
            When(complexity=Project.COMPLEXITY_LOW, then=Value(1)),
        ]
        failure_risk_whens = [
            When(current_environment=Project.CURRENT_ENVIRONMENT_HIGH, then=Value(3)),
            When(
                current_environment=Project.CURRENT_ENVIRONMENT_MODERATE, then=Value(2)
            ),
            When(current_environment=Project.CURRENT_ENVIRONMENT_LOW, then=Value(1)),
        ]
        failure_severity_whens = [
            When(failure_severity=Project.FAILURE_SEVERITY_HIGH, then=Value(3)),
            When(failure_severity=Project.FAILURE_SEVERITY_MODERATE, then=Value(2)),
            When(failure_severity=Project.FAILURE_SEVERITY_LOW, then=Value(1)),
        ]
        response_to_audit_whens = [
            When(response_to_audit=Project.RESPONSE_TO_AUDIT_NO, then=Value(1)),
            When(response_to_audit=Project.RESPONSE_TO_AUDIT_YES, then=Value(1.5)),
            When(
                response_to_audit=Project.RESPONSE_TO_AUDIT_VIOLATION, then=Value(1000)
            ),
        ]
        expected_duration_whens = [
            When(expected_duration=Project.DURATION_12_PLUS_MONTHS, then=Value(4)),
            When(expected_duration=Project.DURATION_6_TO_12_MONTHS, then=Value(3)),
            When(expected_duration=Project.DURATION_1_TO_6_MONTHS, then=Value(2)),
            When(expected_duration=Project.DURATION_SWE, then=Value(1)),
        ]
        completion_whens = [
            When(percentage__gte=76, then=Value(1)),
            When(percentage__range=(50, 75), then=Value(2)),
            When(percentage__range=(26, 49), then=Value(3)),
            When(percentage__range=(1, 25), then=Value(4)),
            When(percentage=0, then=Value(0)),
        ]

        queryset = (
            self.annotate(
                funding_size_value=Case(
                    *funding_size_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                annualized_savings_score=Case(
                    *annualized_savings_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                payback_period_score=Case(
                    *payback_period_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                priority_score=Case(
                    *priority_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                complexity_score=Case(
                    *complexity_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                failure_risk_score=Case(
                    *failure_risk_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                failure_severity_score=Case(
                    *failure_severity_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                response_to_audit_score=Case(
                    *response_to_audit_whens,
                    default=Value(1),
                    output_field=models.DecimalField(),
                )
            )
            .with_impact_total()
            .annotate(
                expected_duration_score=Case(
                    *expected_duration_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .with_latest_health_value()
            .annotate(
                completion_score=Subquery(
                    ProjectPercentComplete.objects.filter(project=OuterRef("pk"))
                    .annotate(
                        score=Case(
                            *completion_whens,
                            default=Value(0),
                            output_field=models.IntegerField(),
                        )
                    )
                    .order_by("-year", "-week")
                    .values("score")[:1]
                )
            )
            # (funding_size + annualized_savings + payback_period)
            # + (priority + complexity + (failure_risk * failure_severity)) * response_to_audit
            .annotate(
                strategic_value=ExpressionWrapper(
                    Least(
                        (
                            (
                                F("funding_size_value")
                                + F("annualized_savings_score")
                                + F("payback_period_score")
                            )
                            + (
                                (
                                    F("priority_score")
                                    + F("complexity_score")
                                    + (
                                        F("failure_risk_score")
                                        * F("failure_severity_score")
                                    )
                                )
                                * F("response_to_audit_score")
                            )
                        ),
                        40,
                    ),
                    output_field=models.DecimalField(),
                )
            )
        )
        return cast(ProjectQuerySet, queryset)

    def with_grade(self) -> ProjectQuerySet:
        modified_whens = [
            When(modified__lt=timezone.now() - timedelta(days=30), then=Value(0)),
            When(modified__gte=timezone.now() - timedelta(days=30), then=Value(10)),
        ]
        has_percent_complete_whens = [
            When(has_percent_complete=False, then=Value(0)),
            When(has_percent_complete=True, then=Value(10)),
        ]
        expenditure_whens = [
            When(
                capital_actuals__isnull=True, opex_actuals__isnull=True, then=Value(0)
            ),
            When(
                capital_actuals__isnull=False, opex_actuals__isnull=True, then=Value(10)
            ),
            When(
                capital_actuals__isnull=True, opex_actuals__isnull=False, then=Value(10)
            ),
            When(
                capital_actuals__isnull=False,
                opex_actuals__isnull=False,
                then=Value(10),
            ),
        ]
        has_health_whens = [
            When(has_health=False, then=Value(0)),
            When(has_health=True, then=Value(10)),
        ]
        has_recent_accomplishments_whens = [
            When(has_recent_accomplishments=False, then=Value(0)),
            When(has_recent_accomplishments=True, then=Value(5)),
        ]
        has_planned_activities_whens = [
            When(has_planned_activities=False, then=Value(0)),
            When(has_planned_activities=True, then=Value(5)),
        ]
        has_technology_component_whens = [
            When(has_technology_components=False, then=Value(10)),
            When(has_technology_components=True, then=Value(10)),
            When(has_technology_components__isnull=True, then=Value(0)),
        ]
        executive_owners_when = [
            When(executive_owners__isnull=True, then=Value(0)),
            When(executive_owners__isnull=False, then=Value(10)),
        ]
        project_managers_when = [
            When(project_managers__isnull=True, then=Value(0)),
            When(project_managers__isnull=False, then=Value(10)),
        ]
        end_date_whens = [
            When(end_date__lt=timezone.now(), then=Value(0)),
            When(end_date__gte=timezone.now(), then=Value(10)),
        ]

        queryset = (
            self.annotate(
                modified_grade=Case(
                    *modified_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                has_percent_complete=Exists(
                    ProjectPercentComplete.objects.filter(
                        project=OuterRef("pk"), percentage__gt=0
                    )
                )
            )
            .annotate(
                percent_complete_grade=Case(
                    *has_percent_complete_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                expenditure_grade=Case(
                    *expenditure_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                has_health=Exists(
                    ProjectHealth.objects.exclude(health="").filter(
                        project=OuterRef("pk")
                    )
                )
            )
            .annotate(
                health_grade=Case(
                    *has_health_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                has_recent_accomplishments=Exists(
                    ProjectRecentAccomplishment.objects.filter(project=OuterRef("pk"))
                )
            )
            .annotate(
                has_in_scope=Exists(InScope.objects.filter(project=OuterRef("pk")))
            )
            .annotate(
                has_out_of_scope=Exists(
                    OutOfScope.objects.filter(project=OuterRef("pk"))
                )
            )
            .annotate(
                has_measure_of_success=Exists(
                    MeasureOfSuccess.objects.filter(project=OuterRef("pk"))
                )
            )
            .annotate(
                recent_accomplishments_grade=Case(
                    *has_recent_accomplishments_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                has_planned_activities=Exists(
                    ProjectPlannedActivity.objects.filter(project=OuterRef("pk"))
                )
            )
            .annotate(
                planned_activities_grade=Case(
                    *has_planned_activities_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                technology_components_grade=Case(
                    *has_technology_component_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                executive_owners_grade=Case(
                    *executive_owners_when,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                project_managers_grade=Case(
                    *project_managers_when,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                end_date_grade=Case(
                    *end_date_whens,
                    default=Value(0),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                grade=ExpressionWrapper(
                    F("modified_grade")
                    + F("percent_complete_grade")
                    + F("expenditure_grade")
                    + F("health_grade")
                    + F("recent_accomplishments_grade")
                    + F("planned_activities_grade")
                    + F("technology_components_grade")
                    + F("executive_owners_grade")
                    + F("project_managers_grade")
                    + F("end_date_grade"),
                    output_field=models.IntegerField(),
                )
            )
        )
        return cast(ProjectQuerySet, queryset)

    def with_expenditures(self) -> ProjectQuerySet:
        queryset = (
            self.annotate(
                capital_forecast_total=Subquery(
                    ProjectCapitalExpenditure.objects.filter(project=OuterRef("pk"))
                    .order_by()
                    .values("project")
                    .annotate(total=Sum("forecast"))
                    .values("total")
                )
            )
            .annotate(
                capital_actuals_total=Subquery(
                    ProjectCapitalExpenditure.objects.filter(project=OuterRef("pk"))
                    .order_by()
                    .values("project")
                    .annotate(total=Sum("actuals"))
                    .values("total")
                )
            )
            .annotate(
                capital_forecast_remaining=ExpressionWrapper(
                    F("capital_forecast_total") - F("capital_actuals_total"),
                    output_field=models.IntegerField(),
                )
            )
            .annotate(
                operational_forecast_total=Subquery(
                    ProjectOperationalExpenditure.objects.filter(project=OuterRef("pk"))
                    .order_by()
                    .values("project")
                    .annotate(total=Sum("forecast"))
                    .values("total")
                )
            )
            .annotate(
                operational_actuals_total=Subquery(
                    ProjectOperationalExpenditure.objects.filter(project=OuterRef("pk"))
                    .order_by()
                    .values("project")
                    .annotate(total=Sum("actuals"))
                    .values("total")
                )
            )
            .annotate(
                operational_forecast_remaining=ExpressionWrapper(
                    F("operational_forecast_total") - F("operational_actuals_total"),
                    output_field=models.IntegerField(),
                )
            )
        )
        return cast(ProjectQuerySet, queryset)

    def with_my_projects(self, user: User) -> ProjectQuerySet:
        queryset = (
            self.filter(
                Q(created_by=user)
                | Q(executive_owners=user)
                | Q(finance_leads=user)
                | Q(business_leads=user)
                | Q(business_analysts=user)
                | Q(other_stakeholders=user)
                | Q(project_managers=user)
            )
            .filter(project_state=Project.PROJECT_STATE_ACTIVE)
            .distinct()
        )

        return cast(ProjectQuerySet, queryset)


class ProjectManager(auto_prefetch.Manager):
    def get_queryset(self) -> ProjectQuerySet:
        return ProjectQuerySet(self.model, using=self._db)

    def search(self, query: str) -> ProjectQuerySet:
        return self.get_queryset().search(query)

    def filtered_for_project(self, only_active: bool = True) -> ProjectQuerySet:
        return self.get_queryset().filtered_for_project(only_active)

    def with_latest_percentage(self) -> ProjectQuerySet:
        return self.get_queryset().with_latest_percentage()

    def with_savings_calculations(
        self, start_date: date = None, end_date: date = None
    ) -> ProjectQuerySet:
        return self.get_queryset().with_savings_calculations(start_date, end_date)

    def with_latest_health(self) -> ProjectQuerySet:
        return self.get_queryset().with_latest_health()

    def with_latest_health_value(self) -> ProjectQuerySet:
        return self.get_queryset().with_latest_health_value()

    def with_health_history(self, period: int = DEFAULT_PERIOD) -> ProjectQuerySet:
        return self.get_queryset().with_health_history(period)

    def with_starred_by_user(self, user: User) -> ProjectQuerySet:
        return self.get_queryset().with_starred_by_user(user)

    def with_division_impact_total(self) -> ProjectQuerySet:
        return self.get_queryset().with_division_impact_total()

    def with_support_impact_total(self) -> ProjectQuerySet:
        return self.get_queryset().with_support_impact_total()

    def with_impact_total(self) -> ProjectQuerySet:
        return self.get_queryset().with_impact_total()

    def with_strategic_value(self) -> ProjectQuerySet:
        return self.get_queryset().with_strategic_value()

    def with_grade(self) -> ProjectQuerySet:
        return self.get_queryset().with_grade()

    def with_expenditures(self) -> ProjectQuerySet:
        return self.get_queryset().with_expenditures()

    def new_projects_count(self, user: User, period: int = DEFAULT_PERIOD) -> int:
        return (
            self.get_queryset()
            .filter(active=True)
            .filter(created__gte=timezone.now() - timedelta(days=period))
            .count()
        )

    def new_projects_change(self, user: User, period: int = DEFAULT_PERIOD) -> int:
        this_period_count = (
            self.get_queryset()
            .filter(active=True)
            .filter(created__gte=timezone.now() - timedelta(days=period))
            .count()
            * 1.0
        )
        last_period_count = (
            self.get_queryset()
            .filter(active=True)
            .filter(
                created__range=(
                    timezone.now() - timedelta(days=period * 2),
                    timezone.now() - timedelta(days=period),
                )
            )
            .count()
            * 1.0
        )
        sign = 1 if this_period_count >= last_period_count else -1
        percent = (
            0
            if last_period_count == 0
            else int(this_period_count / last_period_count * 100)
        )
        return sign * percent

    def on_hold_projects_count(self, user: User) -> int:
        return (
            self.get_queryset()
            .filter(active=True)
            .filter(project_state=Project.PROJECT_STATE_ON_HOLD)
            .count()
        )

    def on_hold_projects_percent(self, user: User) -> int:
        on_hold_count = (
            self.get_queryset()
            .filter(active=True)
            .filter(project_state=Project.PROJECT_STATE_ON_HOLD)
            .count()
        )
        on_hold_and_active_count = (
            self.get_queryset()
            .filter(active=True)
            .filter(
                Q(project_state=Project.PROJECT_STATE_ON_HOLD)
                | Q(project_state=Project.PROJECT_STATE_ACTIVE)
            )
            .count()
        )
        on_hold_percentage = (
            round(on_hold_count / on_hold_and_active_count * 100)
            if on_hold_and_active_count
            else 0
        )
        return on_hold_percentage

    def health_projects_count(self, user: User, health: str) -> int:
        return (
            Project.objects.annotate(
                latest_health=Subquery(
                    ProjectHealth.objects.filter(project=OuterRef("pk"))
                    .order_by("-year", "-week")
                    .values("health")[:1]
                )
            )
            .filter(active=True)
            .filter(project_state=self.model.PROJECT_STATE_ACTIVE, latest_health=health)
            .count()
        )

    def health_projects_percent(self, user: User, health: str) -> int:
        health_list = (
            Project.objects.annotate(
                latest_health=Subquery(
                    ProjectHealth.objects.filter(project=OuterRef("pk"))
                    .order_by("-year", "-week")
                    .values("health")[:1]
                )
            )
            .filter(active=True)
            .filter(
                Q(project_state=self.model.PROJECT_STATE_ON_HOLD)
                | Q(project_state=self.model.PROJECT_STATE_ACTIVE)
            )
            .values_list("latest_health", flat=True)
        )
        total_count = len(health_list)
        health_count = Counter(health_list).get(health)
        health_percentage = (
            round(health_count / total_count * 100) if health_count else 0
        )
        return health_percentage

    def with_my_projects(self, user: User) -> ProjectQuerySet:
        return self.get_queryset().with_my_projects(user=user)


@reversion.register(
    follow=[
        "project_types",
        "primary_division",
        "other_involved_divisions",
        "strategic_pillars",
        "business_leads",
        "executive_owners",
        "project_managers",
        "other_stakeholders",
        "business_analysts",
        "created_by",
        "current_health",
        "percent_complete",
        "recent_accomplishments",
        "planned_activities",
        "executive_actions",
        "in_scope",
        "out_of_scope",
        "measure_of_success",
        "links",
    ]
)
class Project(LifecycleModelMixin, Commentable):
    PRIORITY_LOW = "low"
    PRIORITY_MEDIUM = "medium"
    PRIORITY_HIGH = "high"
    PRIORITY_REGULATORY = "regulatory"
    PRIORITY_CHOICES = (
        (PRIORITY_REGULATORY, "Regulatory"),
        (PRIORITY_HIGH, "High"),
        (PRIORITY_MEDIUM, "Medium"),
        (PRIORITY_LOW, "Low"),
    )
    PRIORITY_DICT = dict(PRIORITY_CHOICES)
    PRIORITY_VALUES = {
        PRIORITY_REGULATORY: 4,
        PRIORITY_HIGH: 3,
        PRIORITY_MEDIUM: 2,
        PRIORITY_LOW: 1,
    }

    COMPLEXITY_LOW = "low"
    COMPLEXITY_MEDIUM = "medium"
    COMPLEXITY_HIGH = "high"
    COMPLEXITY_CHOICES = (
        (COMPLEXITY_HIGH, "High"),
        (COMPLEXITY_MEDIUM, "Medium"),
        (COMPLEXITY_LOW, "Low"),
    )
    COMPLEXITY_DICT = dict(COMPLEXITY_CHOICES)
    COMPLEXITY_VALUES = {COMPLEXITY_HIGH: 3, COMPLEXITY_MEDIUM: 2, COMPLEXITY_LOW: 1}

    DURATION_SWE = "small work effort"
    DURATION_1_TO_6_MONTHS = "1-6 months"
    DURATION_6_TO_12_MONTHS = "6-12 months"
    DURATION_12_PLUS_MONTHS = "12+ months"
    DURATION_CHOICES = (
        (DURATION_SWE, "<1 Month"),
        (DURATION_1_TO_6_MONTHS, "1-6 Months"),
        (DURATION_6_TO_12_MONTHS, "6-12 Months"),
        (DURATION_12_PLUS_MONTHS, "12+ Months"),
    )
    DURATION_DICT = dict(DURATION_CHOICES)
    DURATION_VALUES = {
        DURATION_SWE: 1,
        DURATION_1_TO_6_MONTHS: 2,
        DURATION_6_TO_12_MONTHS: 3,
        DURATION_12_PLUS_MONTHS: 4,
    }

    PROJECT_RIGOR_SWE = "small work effort"
    PROJECT_RIGOR_STANDARD = "standard"
    PROJECT_RIGOR_HIGH = "high"
    PROJECT_RIGOR_CHOICES = (
        (PROJECT_RIGOR_SWE, "Small Work Effort"),
        (PROJECT_RIGOR_STANDARD, "Standard Rigor"),
        (PROJECT_RIGOR_HIGH, "High Rigor"),
    )
    PROJECT_RIGOR_DICT = dict(PROJECT_RIGOR_CHOICES)
    PROJECT_RIGOR_VALUES = {
        PROJECT_RIGOR_SWE: 1,
        PROJECT_RIGOR_STANDARD: 2,
        PROJECT_RIGOR_HIGH: 3,
    }

    RISK_LOW = "low"
    RISK_MEDIUM = "medium"
    RISK_HIGH = "high"
    RISK_CHOICES = ((RISK_LOW, "Low"), (RISK_MEDIUM, "Medium"), (RISK_HIGH, "High"))
    RISK_DICT = dict(RISK_CHOICES)

    PROJECT_STATE_ACTIVE = "active"
    PROJECT_STATE_ARCHIVED = "archived"
    PROJECT_STATE_COMPLETE = "complete"
    PROJECT_STATE_CANCELLED = "cancelled"
    PROJECT_STATE_ON_HOLD = "on hold"
    PROJECT_STATE_INACTIVE = "inactive"
    PROJECT_STATE_DRAFT = "draft"
    PROJECT_STATE_SUBMITTED = "submitted"
    PROJECT_STATE_RETURNED = "returned"
    PROJECT_STATE_DEFERRED = "deferred"
    PROJECT_STATE_CHOICES = (
        (PROJECT_STATE_DEFERRED, "Deferred"),
        (PROJECT_STATE_DRAFT, "Draft"),
        (PROJECT_STATE_SUBMITTED, "Submitted"),
        (PROJECT_STATE_RETURNED, "Returned"),
        (PROJECT_STATE_ACTIVE, "Active"),
        (PROJECT_STATE_COMPLETE, "Complete"),
        (PROJECT_STATE_CANCELLED, "Cancelled"),
        (PROJECT_STATE_ON_HOLD, "On Hold"),
        (PROJECT_STATE_ARCHIVED, "Archived"),
    )
    PROJECT_STATUS_CHOICES = (
        (PROJECT_STATE_ACTIVE, "Active"),
        (PROJECT_STATE_INACTIVE, "Inactive"),
    )
    PROJECT_STATE_DICT = dict(PROJECT_STATE_CHOICES)

    PHASE_IDEA = "idea"
    PHASE_PROPOSE = "propose"
    PHASE_INITIATION = "initiation"
    PHASE_PLANNING = "planning"
    PHASE_EXECUTION = "execution"
    PHASE_CONTROL = "control"
    PHASE_CHOICES = (
        (PHASE_IDEA, "Idea"),
        (PHASE_PROPOSE, "Propose"),
        (PHASE_INITIATION, "Initiation"),
        (PHASE_PLANNING, "Planning"),
        (PHASE_EXECUTION, "Execution"),
        (PHASE_CONTROL, "Close"),
    )
    PHASE_DICT = dict(PHASE_CHOICES)

    COMPANY_CODE_PORK = "2000"
    COMPANY_CODE_DIRECT = "2100"
    COMPANY_CODE_SKIN = "2200"
    COMPANY_CODE_FOODS = "3000"
    COMPANY_CODE_CHOICES = (
        (COMPANY_CODE_PORK, "2000 - Pork Group"),
        (COMPANY_CODE_DIRECT, "2100 - Smithfield Direct, LLC"),
        (COMPANY_CODE_SKIN, "2200 - American Skin"),
        (COMPANY_CODE_FOODS, "3000 - Smithfield Foods"),
    )
    COMPANY_CODE_DICT = dict(COMPANY_CODE_CHOICES)

    MONEY_AMOUNT_NONE = "none"
    MONEY_AMOUNT_LESS_THAN_25k = "less than 25k"
    MONEY_AMOUNT_25K_TO_250K = "25k to 250k"
    MONEY_AMOUNT_250K_TO_1M = "250k to 1m"
    MONEY_AMOUNT_1M_TO_5M = "1m to 5m"
    MONEY_AMOUNT_GREATER_THAN_5M = "greater than 5m"
    MONEY_AMOUNT_CHOICES = (
        (MONEY_AMOUNT_NONE, "None"),
        (MONEY_AMOUNT_LESS_THAN_25k, "Less than 25k"),
        (MONEY_AMOUNT_25K_TO_250K, "25k to 250k"),
        (MONEY_AMOUNT_250K_TO_1M, "250k to 1M"),
        (MONEY_AMOUNT_1M_TO_5M, "1M to 5M"),
        (MONEY_AMOUNT_GREATER_THAN_5M, "Greater than 5M"),
    )
    MONEY_AMOUNT_DICT = dict(MONEY_AMOUNT_CHOICES)
    MONEY_AMOUNT_VALUES = {
        MONEY_AMOUNT_NONE: 1,
        MONEY_AMOUNT_LESS_THAN_25k: 1,
        MONEY_AMOUNT_25K_TO_250K: 2,
        MONEY_AMOUNT_250K_TO_1M: 3,
        MONEY_AMOUNT_1M_TO_5M: 4,
        MONEY_AMOUNT_GREATER_THAN_5M: 5,
    }

    FUNDING_SOURCE_ITT = "it&t"
    FUNDING_SOURCE_BUSINESS = "business"
    FUNDING_SOURCE_TBD = "to be determined"
    FUNDING_SOURCE_CHOICES = (
        (FUNDING_SOURCE_ITT, "IT&T"),
        (FUNDING_SOURCE_BUSINESS, "Business"),
        (FUNDING_SOURCE_TBD, "To Be Determined"),
    )
    FUNDING_SOURCE_DICT = dict(FUNDING_SOURCE_CHOICES)

    PAYBACK_PERIOD_NONE = "None"
    PAYBACK_PERIOD_LESS_THAN_1_YEAR = "<1 year"
    PAYBACK_PERIOD_1_TO_2_YEARS = "1-2 years"
    PAYBACK_PERIOD_2_TO_3_YEARS = "2-3 years"
    PAYBACK_PERIOD_GREATER_THAN_3_YEARS = "3+ years"
    PAYBACK_PERIOD_CHOICES = (
        (PAYBACK_PERIOD_NONE, "None"),
        (PAYBACK_PERIOD_LESS_THAN_1_YEAR, "<1 Year"),
        (PAYBACK_PERIOD_1_TO_2_YEARS, "1-2 Years"),
        (PAYBACK_PERIOD_2_TO_3_YEARS, "2-3 Years"),
        (PAYBACK_PERIOD_GREATER_THAN_3_YEARS, "3+ Years"),
    )
    PAYBACK_PERIOD_DICT = dict(PAYBACK_PERIOD_CHOICES)
    PAYBACK_PERIOD_VALUES = {
        PAYBACK_PERIOD_NONE: 4,
        PAYBACK_PERIOD_LESS_THAN_1_YEAR: 4,
        PAYBACK_PERIOD_1_TO_2_YEARS: 3,
        PAYBACK_PERIOD_2_TO_3_YEARS: 2,
        PAYBACK_PERIOD_GREATER_THAN_3_YEARS: 1,
    }

    FAILURE_SEVERITY_NONE_USER_SELECTED = -1
    FAILURE_SEVERITY_LOW = 0
    FAILURE_SEVERITY_MODERATE = 1
    FAILURE_SEVERITY_HIGH = 2
    FAILURE_SEVERITY_CHOICES = (
        (FAILURE_SEVERITY_NONE_USER_SELECTED, "None"),
        (FAILURE_SEVERITY_LOW, "Low"),
        (FAILURE_SEVERITY_MODERATE, "Moderate"),
        (FAILURE_SEVERITY_HIGH, "High"),
    )
    FAILURE_SEVERITY_DICT = dict(FAILURE_SEVERITY_CHOICES)
    FAILURE_SEVERITY_VALUES = {
        FAILURE_SEVERITY_HIGH: 3,
        FAILURE_SEVERITY_MODERATE: 2,
        FAILURE_SEVERITY_LOW: 1,
        FAILURE_SEVERITY_NONE_USER_SELECTED: 0,
    }

    CURRENT_ENVIRONMENT_NONE_USER_SELECTED = -1
    CURRENT_ENVIRONMENT_LOW = 0
    CURRENT_ENVIRONMENT_MODERATE = 1
    CURRENT_ENVIRONMENT_HIGH = 2
    CURRENT_ENVIRONMENT_CHOICES = (
        (CURRENT_ENVIRONMENT_NONE_USER_SELECTED, "None"),
        (CURRENT_ENVIRONMENT_LOW, "Low"),
        (CURRENT_ENVIRONMENT_MODERATE, "Moderate"),
        (CURRENT_ENVIRONMENT_HIGH, "High"),
    )
    CURRENT_ENVIRONMENT_DICT = dict(CURRENT_ENVIRONMENT_CHOICES)
    CURRENT_ENVIRONMENT_VALUES = {
        CURRENT_ENVIRONMENT_HIGH: 3,
        CURRENT_ENVIRONMENT_MODERATE: 2,
        CURRENT_ENVIRONMENT_LOW: 1,
        CURRENT_ENVIRONMENT_NONE_USER_SELECTED: 0,
    }

    RESPONSE_TO_AUDIT_NO = 0
    RESPONSE_TO_AUDIT_YES = 1
    RESPONSE_TO_AUDIT_VIOLATION = 2
    RESPONSE_TO_AUDIT_CHOICES = (
        (RESPONSE_TO_AUDIT_NO, "No"),
        (RESPONSE_TO_AUDIT_YES, "Yes"),
        (RESPONSE_TO_AUDIT_VIOLATION, "Violation"),
    )
    RESPONSE_TO_AUDIT_DICT = dict(RESPONSE_TO_AUDIT_CHOICES)
    RESPONSE_TO_AUDIT_VALUES = {
        RESPONSE_TO_AUDIT_NO: 1,
        RESPONSE_TO_AUDIT_YES: 1.5,
        RESPONSE_TO_AUDIT_VIOLATION: 1000,
    }

    ESTIMATION_CONFIDENCE_VERY_LOW = "Very Low"
    ESTIMATION_CONFIDENCE_LOW = "Low"
    ESTIMATION_CONFIDENCE_MEDIUM = "Medium"
    ESTIMATION_CONFIDENCE_HIGH = "High"
    ESTIMATION_CONFIDENCE_VERY_HIGH = "Very High"
    ESTIMATION_CONFIDENCE_CHOICES = (
        (4, ESTIMATION_CONFIDENCE_VERY_HIGH),
        (3, ESTIMATION_CONFIDENCE_HIGH),
        (2, ESTIMATION_CONFIDENCE_MEDIUM),
        (1, ESTIMATION_CONFIDENCE_LOW),
        (0, ESTIMATION_CONFIDENCE_VERY_LOW),
    )
    ESTIMATION_CONFIDENCE_DICT = dict(ESTIMATION_CONFIDENCE_CHOICES)

    DEPARTMENTS_INVOLVED_1 = "1"
    DEPARTMENTS_INVOLVED_2_TO_3 = "2-3"
    DEPARTMENTS_INVOLVED_4_TO_5 = "4-5"
    DEPARTMENTS_INVOLVED_6_PLUS = "6+"
    DEPARTMENTS_INVOLVED_CHOICES = (
        (DEPARTMENTS_INVOLVED_1, "1"),
        (DEPARTMENTS_INVOLVED_2_TO_3, "2-3"),
        (DEPARTMENTS_INVOLVED_4_TO_5, "4-5"),
        (DEPARTMENTS_INVOLVED_6_PLUS, "6+"),
    )
    DEPARTMENTS_INVOLVED_DICT = dict(DEPARTMENTS_INVOLVED_CHOICES)

    VENDORS_INVOLVED_0 = "0"
    VENDORS_INVOLVED_1_TO_3 = "1-3"
    VENDORS_INVOLVED_4_TO_5 = "4-5"
    VENDORS_INVOLVED_6_PLUS = "6+"
    VENDORS_INVOLVED_CHOICES = (
        (VENDORS_INVOLVED_0, "0"),
        (VENDORS_INVOLVED_1_TO_3, "1-3"),
        (VENDORS_INVOLVED_4_TO_5, "4-5"),
        (VENDORS_INVOLVED_6_PLUS, "6+"),
    )
    VENDORS_INVOLVED_DICT = dict(VENDORS_INVOLVED_CHOICES)

    ASSOCIATED_PROJECTS_NONE = "none"
    ASSOCIATED_PROJECTS_RELATED_TO = "related_to"
    ASSOCIATED_PROJECTS_DEPENDENT_ON = "dependent_on"
    ASSOCIATED_PROJECTS_HAS_DEPENDENCIES = "has_dependencies"
    ASSOCIATED_PROJECTS_CHOICES = (
        (ASSOCIATED_PROJECTS_NONE, "None"),
        (ASSOCIATED_PROJECTS_RELATED_TO, "Related Projects"),
        (ASSOCIATED_PROJECTS_DEPENDENT_ON, "Dependent on other Projects"),
        (ASSOCIATED_PROJECTS_HAS_DEPENDENCIES, "Dependencies to this Project"),
    )
    ASSOCIATED_PROJECTS_DICT = dict(ASSOCIATED_PROJECTS_CHOICES)

    SCHEDULE_MOTIVATION_NONE = "none"
    SCHEDULE_MOTIVATION_RESOURCE_CONSTRAINT = "resource_constraint"
    SCHEDULE_MOTIVATION_BUSINESS_PROCESS_ACCOMMODATION = (
        "business_process_accommodation"
    )
    SCHEDULE_MOTIVATION_REGULATORY_MANDATE = "regulatory_mandate"
    SCHEDULE_MOTIVATION_CHOICES = (
        (SCHEDULE_MOTIVATION_NONE, "None"),
        (SCHEDULE_MOTIVATION_RESOURCE_CONSTRAINT, "Resource Constraint"),
        (
            SCHEDULE_MOTIVATION_BUSINESS_PROCESS_ACCOMMODATION,
            "Business Process Accommodation",
        ),
        (SCHEDULE_MOTIVATION_REGULATORY_MANDATE, "Regulatory Mandate"),
    )
    SCHEDULE_MOTIVATION_DICT = dict(SCHEDULE_MOTIVATION_CHOICES)

    SIMILAR_EXPERIENCE_HIGH = "high"
    SIMILAR_EXPERIENCE_MEDIUM = "medium"
    SIMILAR_EXPERIENCE_LOW = "low"
    SIMILAR_EXPERIENCE_NONE = "none"
    SIMILAR_EXPERIENCE_CHOICES = (
        (SIMILAR_EXPERIENCE_HIGH, "High"),
        (SIMILAR_EXPERIENCE_MEDIUM, "Medium"),
        (SIMILAR_EXPERIENCE_LOW, "Low"),
        (SIMILAR_EXPERIENCE_NONE, "None"),
    )
    SIMILAR_EXPERIENCE_DICT = dict(SIMILAR_EXPERIENCE_CHOICES)

    INFRASTRUCTURE_READINESS_HIGH = "high"
    INFRASTRUCTURE_READINESS_MEDIUM = "medium"
    INFRASTRUCTURE_READINESS_LOW = "low"
    INFRASTRUCTURE_READINESS_NONE = "none"
    INFRASTRUCTURE_READINESS_CHOICES = (
        (INFRASTRUCTURE_READINESS_HIGH, "High"),
        (INFRASTRUCTURE_READINESS_MEDIUM, "Medium"),
        (INFRASTRUCTURE_READINESS_LOW, "Low"),
        (INFRASTRUCTURE_READINESS_NONE, "None"),
    )
    INFRASTRUCTURE_READINESS_DICT = dict(INFRASTRUCTURE_READINESS_CHOICES)

    EXTERNAL_USER_PRIVILEGES_NONE = "none"
    EXTERNAL_USER_PRIVILEGES_VIEW = "view"
    EXTERNAL_USER_PRIVILEGES_DOWNLOAD = "download"
    EXTERNAL_USER_PRIVILEGES_EDIT = "edit"
    EXTERNAL_USER_PRIVILEGES_CHOICES = (
        (EXTERNAL_USER_PRIVILEGES_NONE, "None"),
        (EXTERNAL_USER_PRIVILEGES_VIEW, "View Only"),
        (EXTERNAL_USER_PRIVILEGES_DOWNLOAD, "View and Access/Download Files"),
        (EXTERNAL_USER_PRIVILEGES_EDIT, "Create, Edit, & View Data"),
    )
    EXTERNAL_USER_PRIVILEGES_DICT = dict(EXTERNAL_USER_PRIVILEGES_CHOICES)

    POST_PROJECT_SUPPORT_NONE = "none"
    POST_PROJECT_SUPPORT_LOW = "low"
    POST_PROJECT_SUPPORT_MEDIUM = "medium"
    POST_PROJECT_SUPPORT_HIGH = "high"
    POST_PROJECT_SUPPORT_CHOICES = (
        (POST_PROJECT_SUPPORT_NONE, "None"),
        (POST_PROJECT_SUPPORT_LOW, "Low"),
        (POST_PROJECT_SUPPORT_MEDIUM, "Medium"),
        (POST_PROJECT_SUPPORT_HIGH, "High"),
    )
    POST_PROJECT_SUPPORT_DICT = dict(POST_PROJECT_SUPPORT_CHOICES)

    COMMUNICATION_NEED_HIGH = "high"
    COMMUNICATION_NEED_MEDIUM = "medium"
    COMMUNICATION_NEED_LOW = "low"
    COMMUNICATION_NEED_CHOICES = (
        (COMMUNICATION_NEED_HIGH, "The majority of the company"),
        (
            COMMUNICATION_NEED_MEDIUM,
            "The majority of employees in one or more of the following businesses: Hog Production, Fresh, Packaged, or International",
        ),
        (COMMUNICATION_NEED_LOW, "Specific departments or project teams"),
    )
    COMMUNICATION_NEED_DICT = dict(COMMUNICATION_NEED_CHOICES)

    company = models.ForeignKey(
        "organizations.Company", on_delete=models.SET_NULL, null=True
    )

    location = models.ForeignKey(
        Location, on_delete=models.SET_NULL, blank=True, null=True
    )
    private = models.BooleanField(default=False, blank=True)
    ready_to_begin = models.BooleanField(default=False, blank=True)
    get_to_green = models.TextField(blank=True, null=True)
    idea_id = models.CharField(max_length=50, blank=True, null=True)
    name = models.CharField(max_length=255)
    project_rigor = models.CharField(
        max_length=50, choices=PROJECT_RIGOR_CHOICES, blank=True, null=True
    )
    project_types = models.ManyToManyField(ProjectType, blank=True)
    primary_division = models.ForeignKey(
        Division, on_delete=models.PROTECT, blank=True, null=True
    )
    other_involved_divisions = models.ManyToManyField(
        Division, related_name="other_divisions", blank=True
    )
    business_segment = models.ForeignKey(
        BusinessSegment, on_delete=models.PROTECT, blank=True, null=True
    )
    strategic_pillars = models.ManyToManyField(StrategicPillar)
    strategic_subpillars = models.ManyToManyField(
        SubPillar, blank=True
    )  # deprecated; do not use
    pods = models.ManyToManyField(Pod, blank=True)
    summary = models.TextField(
        max_length=5000, help_text="What will be delivered by the project?"
    )
    tags = TaggableManager(blank=True)
    business_case = models.TextField(
        max_length=5000,
        help_text="Describe the justification and expected benefit for this initiative.",
        blank=True,
        null=True,
    )
    executive_owners = models.ManyToManyField(
        "users.User", related_name="executive_owners", blank=True
    )
    finance_leads = models.ManyToManyField(
        "users.User", related_name="finance_leads", blank=True
    )
    business_leads = models.ManyToManyField(
        "users.User", related_name="business_leads", blank=True
    )
    business_analysts = models.ManyToManyField(
        "users.User", related_name="business_analysts", blank=True
    )
    other_stakeholders = models.ManyToManyField(
        "users.User", related_name="other_stakeholders", blank=True
    )
    project_managers = models.ManyToManyField(
        "users.User", related_name="project_managers", blank=True
    )
    location_engineers = models.ManyToManyField(
        "users.User", related_name="location_engineers", blank=True
    )
    location_managers = models.ManyToManyField(
        "users.User", related_name="location_managers", blank=True
    )
    operations_directors = models.ManyToManyField(
        "users.User", related_name="operations_directors", blank=True
    )
    engineer_directors = models.ManyToManyField(
        "users.User", related_name="engineer_directors", blank=True
    )
    vps = models.ManyToManyField("users.User", related_name="vps", blank=True)
    svps = models.ManyToManyField("users.User", related_name="svps", blank=True)
    coos = models.ManyToManyField("users.User", related_name="coos", blank=True)
    internal_savings_initiative = models.BooleanField(
        choices=((False, "No"), (True, "Yes")), blank=True, null=True
    )
    committed_to_spend = models.BooleanField(
        choices=((False, "No"), (True, "Yes")), blank=True, null=True
    )
    funding_size = models.CharField(
        max_length=50, choices=MONEY_AMOUNT_CHOICES, blank=True, null=True
    )
    annualized_savings = models.CharField(
        max_length=50, choices=MONEY_AMOUNT_CHOICES, blank=True, null=True
    )
    profitability_index = models.IntegerField(blank=True, null=True)
    capital_budget = models.CharField(
        max_length=50, choices=MONEY_AMOUNT_CHOICES, blank=True, null=True
    )
    expense_budget = models.CharField(
        max_length=50, choices=MONEY_AMOUNT_CHOICES, blank=True, null=True
    )
    funding_source = models.CharField(
        max_length=50, choices=FUNDING_SOURCE_CHOICES, blank=True, null=True
    )
    payback_period = models.CharField(
        max_length=50, choices=PAYBACK_PERIOD_CHOICES, blank=True, null=True
    )
    capital_expenditure = models.BooleanField(
        choices=((False, "No"), (True, "Yes")), blank=True, null=True
    )
    capital_budget_amount = models.BigIntegerField(
        default=0, blank=True, null=True
    )  # dollars
    expense_budget_amount = models.BigIntegerField(
        default=0, blank=True, null=True
    )  # dollars
    annual_savings_target = models.BigIntegerField(blank=True, null=True)  # dollars
    capital_forecast = models.BigIntegerField(default=0, blank=True, null=True)
    capital_actuals = models.BigIntegerField(default=0, blank=True, null=True)
    car_number = models.CharField(max_length=50, blank=True, null=True)
    expense_io_number = models.CharField(max_length=50, blank=True, null=True)
    capital_io_number = models.CharField(max_length=50, blank=True, null=True)
    opex_expenditure = models.BooleanField(
        choices=((False, "No"), (True, "Yes")), blank=True, null=True
    )
    opex_forecast = models.BigIntegerField(default=0, blank=True, null=True)
    opex_actuals = models.BigIntegerField(default=0, blank=True, null=True)
    company_code = models.CharField(
        max_length=50, choices=COMPANY_CODE_CHOICES, blank=True, null=True
    )
    cost_center = models.CharField(max_length=100, blank=True, null=True)
    gl_account = models.CharField(max_length=50, blank=True, null=True)
    expected_duration = models.CharField(
        max_length=50, choices=DURATION_CHOICES, blank=True, null=True
    )
    savings_start_date = models.DateField(blank=True, null=True)
    savings_end_date = models.DateField(blank=True, null=True)
    start_date = models.DateField(blank=True, null=True)
    end_date = models.DateField(blank=True, null=True)
    is_infrastructure = models.BooleanField(
        choices=((False, "No"), (True, "Yes")), blank=True, null=True
    )
    has_technology_components = models.BooleanField(
        choices=((False, "No"), (True, "Yes")), blank=True, null=True
    )
    technology_components = models.TextField(
        max_length=5000,
        blank=True,
        help_text="What are the technical components?",
        null=True,
    )
    corporate_communication_involvement = models.BooleanField(
        choices=((False, "No"), (True, "Yes")), blank=True, null=True
    )
    corporate_communication_needs = models.CharField(
        max_length=50, choices=COMMUNICATION_NEED_CHOICES, blank=True, null=True
    )
    corporate_communication_needs_description = models.TextField(
        max_length=5000, blank=True
    )
    sap_impact = models.BooleanField(
        choices=((False, "No"), (True, "Yes")), blank=True, null=True
    )
    priority = models.CharField(
        choices=PRIORITY_CHOICES, max_length=50, blank=True, null=True
    )
    complexity = models.CharField(
        max_length=50, choices=COMPLEXITY_CHOICES, blank=True, null=True
    )
    project_state = models.CharField(
        max_length=50, choices=PROJECT_STATE_CHOICES, default=PROJECT_STATE_ACTIVE
    )
    phase = models.CharField(
        max_length=50,
        choices=PHASE_CHOICES,
        default=PHASE_INITIATION,
        blank=True,
        null=True,
    )
    departments_involved = models.CharField(
        max_length=50, choices=DEPARTMENTS_INVOLVED_CHOICES, blank=True, null=True
    )
    vendors_involved = models.CharField(
        max_length=50, choices=VENDORS_INVOLVED_CHOICES, blank=True, null=True
    )
    associated_projects = models.CharField(
        max_length=50, choices=ASSOCIATED_PROJECTS_CHOICES, blank=True, null=True
    )
    schedule_motivation = models.CharField(
        max_length=50, choices=SCHEDULE_MOTIVATION_CHOICES, blank=True, null=True
    )
    similar_experience = models.CharField(
        max_length=50, choices=SIMILAR_EXPERIENCE_CHOICES, blank=True, null=True
    )
    infrastructure_readiness = models.CharField(
        max_length=50, choices=INFRASTRUCTURE_READINESS_CHOICES, blank=True, null=True
    )
    external_user_privileges = models.CharField(
        max_length=50, choices=EXTERNAL_USER_PRIVILEGES_CHOICES, blank=True, null=True
    )
    post_project_support = models.CharField(
        max_length=50, choices=POST_PROJECT_SUPPORT_CHOICES, blank=True, null=True
    )
    created_by = models.ForeignKey(
        "users.User", editable=False, on_delete=models.PROTECT
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)
    active = models.BooleanField(default=True)
    current_environment = models.IntegerField(
        choices=CURRENT_ENVIRONMENT_CHOICES, blank=True, null=True
    )
    failure_severity = models.IntegerField(
        choices=FAILURE_SEVERITY_CHOICES, blank=True, null=True
    )
    response_to_audit = models.IntegerField(
        choices=RESPONSE_TO_AUDIT_CHOICES, blank=True, null=True
    )
    estimation_confidence = models.IntegerField(
        choices=ESTIMATION_CONFIDENCE_CHOICES, blank=True, null=True
    )
    program = models.ForeignKey(
        "programs.Program", on_delete=models.SET_NULL, blank=True, null=True
    )

    attachments = GenericRelation(Attachment)
    comments = GenericRelation(Comment)
    links = GenericRelation(Link)

    objects = ProjectManager()

    def __str__(self) -> str:
        return self.name

    def get_absolute_url(self):
        return reverse("project_detail", kwargs={"pk": self.pk})

    @property
    def priority_display(self) -> str:
        return self.PRIORITY_DICT.get(self.priority, "")

    @property
    def complexity_display(self) -> str:
        return self.COMPLEXITY_DICT.get(self.complexity, "")

    @property
    def expected_duration_display(self) -> str:
        return self.DURATION_DICT.get(self.expected_duration, "")

    @property
    def project_rigor_display(self) -> str:
        return self.PROJECT_RIGOR_DICT.get(self.project_rigor, "")

    @property
    def project_state_display(self) -> str:
        return self.PROJECT_STATE_DICT.get(self.project_state, "None")

    @property
    def phase_display(self) -> str:
        return self.PHASE_DICT.get(self.phase, "None")

    @property
    def company_code_display(self) -> str:
        return self.COMPANY_CODE_DICT.get(self.company_code, "None")

    @property
    def funding_size_display(self) -> str:
        return self.MONEY_AMOUNT_DICT.get(self.funding_size, "")

    @property
    def annualized_savings_display(self) -> str:
        return self.MONEY_AMOUNT_DICT.get(self.annualized_savings, "")

    @property
    def capital_budget_display(self) -> str:
        return self.MONEY_AMOUNT_DICT.get(self.capital_budget, "")

    @property
    def expense_budget_display(self) -> str:
        return self.MONEY_AMOUNT_DICT.get(self.expense_budget, "")

    @property
    def funding_source_display(self) -> str:
        return self.FUNDING_SOURCE_DICT.get(self.funding_source, "")

    @property
    def payback_period_display(self) -> str:
        return self.PAYBACK_PERIOD_DICT.get(self.payback_period, "")

    @property
    def current_environment_display(self) -> str:
        return self.CURRENT_ENVIRONMENT_DICT.get(self.current_environment, "")

    @property
    def failure_severity_display(self) -> str:
        return self.FAILURE_SEVERITY_DICT.get(self.failure_severity, "")

    @property
    def response_to_audit_display(self) -> str:
        return self.RESPONSE_TO_AUDIT_DICT.get(self.response_to_audit, "")

    @property
    def estimation_confidence_display(self) -> str:
        return self.ESTIMATION_CONFIDENCE_DICT.get(self.estimation_confidence, "")

    @property
    def departments_involved_display(self) -> str:
        return self.DEPARTMENTS_INVOLVED_DICT.get(self.departments_involved, "")

    @property
    def vendors_involved_display(self) -> str:
        return self.VENDORS_INVOLVED_DICT.get(self.vendors_involved, "")

    @property
    def associated_projects_display(self) -> str:
        return self.ASSOCIATED_PROJECTS_DICT.get(self.associated_projects, "")

    @property
    def schedule_motivation_display(self) -> str:
        return self.SCHEDULE_MOTIVATION_DICT.get(self.schedule_motivation, "")

    @property
    def similar_experience_display(self) -> str:
        return self.SIMILAR_EXPERIENCE_DICT.get(self.similar_experience, "")

    @property
    def infrastructure_readiness_display(self) -> str:
        return self.INFRASTRUCTURE_READINESS_DICT.get(self.infrastructure_readiness, "")

    @property
    def external_user_privileges_display(self) -> str:
        return self.EXTERNAL_USER_PRIVILEGES_DICT.get(self.external_user_privileges, "")

    @property
    def post_project_support_display(self) -> str:
        return self.POST_PROJECT_SUPPORT_DICT.get(self.post_project_support, "")

    @property
    def corporate_communications_needs_display(self) -> str:
        return self.COMMUNICATION_NEED_DICT.get(self.corporate_communication_needs, "")

    @property
    def initiation_date(self) -> Union[datetime, None]:
        try:
            meet_item = self.meetingitem_set.with_results().filter(
                phase=Project.PHASE_INITIATION, vote_result__icontains="Accept"
            )[0]
            if meet_item.meeting:
                return meet_item.meeting.meeting_date
        except IndexError:
            pass
        return None

    @property
    def planning_date(self) -> Union[datetime, None]:
        try:
            meet_item = self.meetingitem_set.with_results().filter(
                phase=Project.PHASE_PLANNING, vote_result__icontains="Accept"
            )[0]
            if meet_item.meeting:
                return meet_item.meeting.meeting_date
        except IndexError:
            pass
        return None

    @property
    def execution_date(self) -> Union[datetime, None]:
        try:
            meet_item = self.meetingitem_set.with_results().filter(
                phase=Project.PHASE_EXECUTION, vote_result__icontains="Accept"
            )[0]
            if meet_item.meeting:
                return meet_item.meeting.meeting_date
        except IndexError:
            pass
        return None

    @property
    def control_date(self) -> Union[datetime, None]:
        try:
            meet_item = self.meetingitem_set.with_results().filter(
                phase=Project.PHASE_CONTROL, vote_result__icontains="Accept"
            )[0]
            if meet_item.meeting:
                return meet_item.meeting.meeting_date
        except IndexError:
            pass
        return None

    @property
    def project_comments(self) -> QuerySet[Comment]:
        """Return the comments related to a project"""
        return self.comments.all()

    @property
    def next_plan_project(self):
        """Returns the next plan project"""
        from apps.planner.models import Plan, PlanProject

        next_plan = Plan.objects.get_next()
        return PlanProject.objects.filter(project=self, plan=next_plan).first()

    @property
    def proposal_comments(self) -> QuerySet[Comment]:
        """Return the comments related to a projects proposal"""
        return self.next_plan_project.comments.all()

    @property
    def relevant_plan_projects(self) -> QuerySet["PlanProject"]:
        """
        Returns all plan projects for this project with valid capital/expense data.
        """
        from apps.planner.models import PlanProject

        return (
            PlanProject.objects.filter(project=self)
            .exclude(capital_amount__isnull=True, expense_amount__isnull=True)
            .order_by("plan__year")
        )

    @property
    def capital_finance_json(self) -> str:
        """
        JSON representation of the project's capital financial status.
        """
        return json.dumps(
            {
                "spend": self.capital_spend_percentage,
                "capital_remaining": self.capital_remaining_percentage,
            }
        )

    @property
    def expense_finance_json(self) -> str:
        """
        JSON representation of the project's expense financial status.
        """
        return json.dumps(
            {
                "spend": self.expense_spend_percentage,
                "expense_remaining": self.expense_remaining_percentage,
            }
        )

    def get_rigor(self) -> str:
        if (
            self.expected_duration
            in [None, "", self.DURATION_SWE, self.DURATION_1_TO_6_MONTHS]
            and self.departments_involved in [None, "", self.DEPARTMENTS_INVOLVED_1]
            and self.vendors_involved in [None, "", self.VENDORS_INVOLVED_0]
            and self.associated_projects
            in [
                None,
                "",
                self.ASSOCIATED_PROJECTS_NONE,
                self.ASSOCIATED_PROJECTS_RELATED_TO,
                self.ASSOCIATED_PROJECTS_DEPENDENT_ON,
            ]
            and self.schedule_motivation
            in [
                None,
                "",
                self.SCHEDULE_MOTIVATION_NONE,
                self.SCHEDULE_MOTIVATION_RESOURCE_CONSTRAINT,
            ]
            and self.similar_experience
            in [
                None,
                "",
                self.SIMILAR_EXPERIENCE_HIGH,
                self.SIMILAR_EXPERIENCE_MEDIUM,
                self.SIMILAR_EXPERIENCE_LOW,
            ]
            and self.infrastructure_readiness
            in [
                None,
                "",
                self.INFRASTRUCTURE_READINESS_HIGH,
                self.INFRASTRUCTURE_READINESS_MEDIUM,
            ]
            and self.external_user_privileges
            in [None, "", self.EXTERNAL_USER_PRIVILEGES_NONE]
            and self.post_project_support in [None, "", self.POST_PROJECT_SUPPORT_NONE]
        ):
            return self.PROJECT_RIGOR_SWE
        elif (
            self.vendors_involved
            in [self.VENDORS_INVOLVED_4_TO_5, self.VENDORS_INVOLVED_6_PLUS]
            or self.external_user_privileges
            in [
                self.EXTERNAL_USER_PRIVILEGES_DOWNLOAD,
                self.EXTERNAL_USER_PRIVILEGES_EDIT,
            ]
            or self.post_project_support == self.POST_PROJECT_SUPPORT_HIGH
        ):
            return self.PROJECT_RIGOR_HIGH
        else:
            return self.PROJECT_RIGOR_STANDARD

    def get_risk(self) -> str:
        if (
            self.funding_size == self.MONEY_AMOUNT_GREATER_THAN_5M
            or self.vendors_involved
            in [self.VENDORS_INVOLVED_4_TO_5, self.VENDORS_INVOLVED_6_PLUS]
            or self.schedule_motivation == self.SCHEDULE_MOTIVATION_REGULATORY_MANDATE
            or self.similar_experience
            in [self.SIMILAR_EXPERIENCE_LOW, self.SIMILAR_EXPERIENCE_NONE]
            or self.infrastructure_readiness == self.INFRASTRUCTURE_READINESS_NONE
            or self.external_user_privileges
            in [
                self.EXTERNAL_USER_PRIVILEGES_DOWNLOAD,
                self.EXTERNAL_USER_PRIVILEGES_EDIT,
            ]
        ):
            return self.RISK_HIGH
        elif (
            self.funding_size
            in [self.MONEY_AMOUNT_250K_TO_1M, self.MONEY_AMOUNT_1M_TO_5M]
            or self.vendors_involved == self.VENDORS_INVOLVED_1_TO_3
            or self.associated_projects == self.ASSOCIATED_PROJECTS_HAS_DEPENDENCIES
            or self.schedule_motivation
            == self.SCHEDULE_MOTIVATION_BUSINESS_PROCESS_ACCOMMODATION
            or self.similar_experience == self.SIMILAR_EXPERIENCE_MEDIUM
            or self.infrastructure_readiness == self.INFRASTRUCTURE_READINESS_LOW
            or self.external_user_privileges == self.EXTERNAL_USER_PRIVILEGES_VIEW
            or self.post_project_support
            in [self.POST_PROJECT_SUPPORT_MEDIUM, self.POST_PROJECT_SUPPORT_HIGH]
        ):
            return self.RISK_MEDIUM
        else:
            return self.RISK_LOW

    def get_complexity(self) -> str:
        if (
            self.departments_involved == self.DEPARTMENTS_INVOLVED_6_PLUS
            or self.vendors_involved
            in [self.VENDORS_INVOLVED_4_TO_5, self.VENDORS_INVOLVED_6_PLUS]
            or self.infrastructure_readiness == self.INFRASTRUCTURE_READINESS_NONE
            or self.external_user_privileges
            in [
                self.EXTERNAL_USER_PRIVILEGES_DOWNLOAD,
                self.EXTERNAL_USER_PRIVILEGES_EDIT,
            ]
            or self.post_project_support == self.POST_PROJECT_SUPPORT_HIGH
        ):
            return self.COMPLEXITY_HIGH
        elif (
            self.departments_involved == self.DEPARTMENTS_INVOLVED_4_TO_5
            or self.vendors_involved == self.VENDORS_INVOLVED_1_TO_3
            or self.infrastructure_readiness
            in [self.INFRASTRUCTURE_READINESS_MEDIUM, self.INFRASTRUCTURE_READINESS_LOW]
            or self.external_user_privileges == self.EXTERNAL_USER_PRIVILEGES_VIEW
            or self.post_project_support
            in [self.POST_PROJECT_SUPPORT_LOW, self.POST_PROJECT_SUPPORT_MEDIUM]
        ):
            return self.COMPLEXITY_MEDIUM
        else:
            return self.COMPLEXITY_LOW

    def get_strategic_value(self) -> int:
        # financials = funding size + annualized savings + payback period (max 14)
        # funding size scoring is inverted - if it costs more to fund the project,
        # the strategic value score is reduced.
        FUNDING_SIZE_VALUES = {5: 1, 4: 2, 3: 3, 2: 4, 1: 5}
        funding_size = self.MONEY_AMOUNT_VALUES.get(self.funding_size, 0)
        funding_size_value = FUNDING_SIZE_VALUES.get(funding_size, 0)
        annualized_savings_score = self.MONEY_AMOUNT_VALUES.get(
            self.annualized_savings, 0
        )
        payback_period_score = self.PAYBACK_PERIOD_VALUES.get(self.payback_period, 0)

        financials_score = (
            funding_size_value + annualized_savings_score + payback_period_score
        )

        # impact = (priority + complexity + (failure risk * failure severity)) * response to audit
        priority_score = self.PRIORITY_VALUES.get(self.priority, 0)
        complexity_score = self.COMPLEXITY_VALUES.get(self.complexity, 0)
        risk_rating = self.CURRENT_ENVIRONMENT_VALUES.get(
            self.current_environment, 0
        ) * self.FAILURE_SEVERITY_VALUES.get(self.failure_severity, 0)
        response_to_audit_score = self.RESPONSE_TO_AUDIT_VALUES.get(
            self.response_to_audit, 1
        )

        priority_complexity_risk = priority_score + complexity_score + risk_rating
        if not priority_complexity_risk:
            priority_complexity_risk = 1
        impact_score = priority_complexity_risk * response_to_audit_score

        strategic_value_score = financials_score + impact_score
        if strategic_value_score >= 40:
            return 40
        return int(round(strategic_value_score))

    @property
    def current_health(self) -> Optional[ProjectHealth]:
        return self.projecthealth_set.first()

    @property
    def percent_complete(self) -> Optional[ProjectPercentComplete]:
        return self.projectpercentcomplete_set.first()

    @property
    def recent_accomplishments(
        self,
    ) -> Union[QuerySet, Iterable[ProjectRecentAccomplishment]]:
        return self.projectrecentaccomplishment_set.filter(active=True)

    @property
    def planned_activities(self) -> Union[QuerySet, Iterable[ProjectPlannedActivity]]:
        return self.projectplannedactivity_set.filter(active=True)

    @property
    def executive_actions(self) -> Union[QuerySet, Iterable[ProjectExecutiveAction]]:
        return self.projectexecutiveaction_set.filter(active=True)

    @property
    def in_scope(self) -> Union[QuerySet, Iterable[InScope]]:
        return self.inscope_set.filter(active=True)

    @property
    def out_of_scope(self):
        return self.outofscope_set.filter(active=True)

    @property
    def measure_of_success(self):
        return self.measureofsuccess_set.filter(active=True)

    @property
    def support_impact(self) -> Optional[ProjectSupportImpact]:
        return ProjectSupportImpact.objects.filter(project=self).first()

    def get_division_impact_total(self) -> int:
        return ProjectDivisionImpact.objects.impact_total_for_project(self)

    def get_support_impact_total(self) -> int:
        support_impact = self.support_impact
        return support_impact.total if support_impact else 0

    def get_impact_total(self) -> int:
        return self.get_division_impact_total() + self.get_support_impact_total()

    @property
    def overdue(self) -> bool:
        if not self.end_date:
            return False
        return (
            self.project_state == self.PROJECT_STATE_ACTIVE
            and self.end_date < timezone.now().date()
        )

    @property
    def end_date_status(self) -> str:
        today = timezone.now().date()
        if self.end_date is None:
            return "red"
        elif self.end_date < today:
            return "red"
        elif self.end_date < today + timedelta(days=3):
            return "yellow"
        else:
            return "green"

    @property
    def modified_status(self) -> str:
        now = timezone.now()
        if self.project_state != Project.PROJECT_STATE_ACTIVE:
            return "black"
        elif self.modified > now - timedelta(weeks=1):
            return "green"
        elif self.modified > now - timedelta(weeks=2):
            return "yellow"
        else:
            return "red"

    @property
    def project_length(self) -> int:
        if not self.start_date or not self.end_date:
            return 0
        start = 12 * self.start_date.year + self.start_date.month - 1
        end = 12 * self.end_date.year + self.end_date.month
        return end - start

    @property
    def project_team(self) -> List[User]:
        team = set()
        for user in self.executive_owners.all():
            team.add(user)
        for user in self.business_leads.all():
            team.add(user)
        for user in self.business_analysts.all():
            team.add(user)
        for user in self.finance_leads.all():
            team.add(user)
        for user in self.other_stakeholders.all():
            team.add(user)
        for user in self.project_managers.all():
            team.add(user)
        return list(team)

    @property
    def savings_monitoring_length(self) -> int:
        if not self.savings_start_date or not self.end_date:
            return 0
        start = 12 * self.savings_start_date.year + self.savings_start_date.month - 1
        end = 12 * self.savings_end_date.year + self.savings_end_date.month
        return end - start

    def get_capital_forecast_total(self) -> int:
        return ProjectCapitalExpenditure.objects.get_project_forecast_total(self)

    def get_capital_actuals_total(self) -> int:
        return ProjectCapitalExpenditure.objects.get_project_actuals_total(self)

    def get_operational_forecast_total(self) -> int:
        return ProjectOperationalExpenditure.objects.get_project_forecast_total(self)

    def get_operational_actuals_total(self) -> int:
        return ProjectOperationalExpenditure.objects.get_project_actuals_total(self)

    def get_savings_total(self) -> int:
        return ProjectSavings.objects.get_project_savings_total(self)

    @property
    def capital_budget_remaining(self) -> int:
        budget = self.capital_budget_amount or 0
        spend = self.capital_actuals or 0
        remaining = budget - spend
        return max(remaining, 0)

    @property
    def capital_spend_percentage(self) -> float:
        budget = (self.capital_budget_amount or 0) * 1.0
        spend = (self.capital_actuals or 0) * 1.0
        if not budget or not spend:
            return 0.0
        return spend / budget

    @property
    def capital_remaining_percentage(self) -> float:
        budget = (self.capital_budget_amount or 0) * 1.0
        remaining = self.capital_budget_remaining * 1.0
        spend = (self.capital_actuals or 0) * 1.0
        if not budget or not spend:
            return 100.0
        return remaining / budget

    @property
    def expense_budget_remaining(self) -> int:
        budget = self.expense_budget_amount or 0
        spend = self.opex_actuals or 0
        remaining = budget - spend
        return max(remaining, 0)

    @property
    def expense_spend_percentage(self) -> float:
        budget = (self.expense_budget_amount or 0) * 1.0
        spend = (self.opex_actuals or 0) * 1.0
        if not budget or not spend:
            return 0.0
        return spend / budget

    @property
    def expense_remaining_percentage(self) -> float:
        budget = (self.expense_budget_amount or 0) * 1.0
        remaining = self.expense_budget_remaining * 1.0
        spend = (self.opex_actuals or 0) * 1.0
        if not budget or not spend:
            return 100.0
        return remaining / budget

    def get_unique_executive_actions(self) -> Set[str]:
        executive_actions: Iterable[ProjectExecutiveAction] = (
            self.projectexecutiveaction_set.all()
        )
        return {executive_action.action for executive_action in executive_actions}

    def needs_get_to_green(self) -> bool:
        current_health = self.current_health
        non_green_healths = [ProjectHealth.HEALTH.YELLOW, ProjectHealth.HEALTH.RED]
        return (
            current_health.budget_health in non_green_healths
            or current_health.scope_health in non_green_healths
            or current_health.schedule_health in non_green_healths
        )

    def needs_corporate_communication(self) -> bool:
        return self.corporate_communication_needs in [
            Project.COMMUNICATION_NEED_MEDIUM,
            Project.COMMUNICATION_NEED_HIGH,
        ]

    def is_starred_by_user(self, user: User) -> bool:
        return self.starredproject_set.filter(starred_by=user).exists()

    def has_project_dates(self) -> bool:
        return bool(self.start_date and self.end_date)

    def has_tollgate_dates(self) -> bool:
        if self.project_rigor == self.PROJECT_RIGOR_SWE:
            return False
        return bool(
            self.initiation_date
            or self.planning_date
            or self.execution_date
            or self.control_date
        )

    def has_monitoring_dates(self) -> bool:
        return bool(self.savings_start_date and self.savings_end_date)

    def user_has_view_access(self, user) -> bool:
        """
        Check if the user has permission to view this project.

        Args:
            user: The user to check permissions for

        Returns:
            bool: True if user has read permission, False otherwise
        """
        # Superusers always have access
        if getattr(user, "is_superuser", False):
            return True

        # Company admins have access
        if getattr(user, "is_company_admin", False):
            return True

        # Check if user is authenticated and has a company
        if not user.is_authenticated or not getattr(user, "company", None):
            return False

        # Check role-based permissions
        return self._check_user_permission(user, "read")

    def user_has_modify_access(self, user) -> bool:
        """
        Check if the user has permission to modify this project.

        Args:
            user: The user to check permissions for

        Returns:
            bool: True if user has update permission, False otherwise
        """
        # Superusers always have access
        if getattr(user, "is_superuser", False):
            return True

        # Company admins have access
        if getattr(user, "is_company_admin", False):
            return True

        # Check if user is authenticated and has a company
        if not user.is_authenticated or not getattr(user, "company", None):
            return False

        # Check role-based permissions
        return self._check_user_permission(user, "update")

    def _check_user_permission(self, user, permission: str) -> bool:
        """
        Helper method to check if user has a specific permission for this project.

        Args:
            user: The user to check permissions for
            permission: The permission type ("read", "create", "update", "delete")

        Returns:
            bool: True if user has the permission, False otherwise
        """
        from django.contrib.contenttypes.models import ContentType
        from apps.users.models import ModelPermission, RoleAssignment

        # Get the ContentType for Project model
        try:
            content_type = ContentType.objects.get_for_model(Project)
        except ContentType.DoesNotExist:
            return False

        # Map permission name to field name
        permission_field = f"can_{permission}"

        # Get all roles for this user within their company
        user_roles = (
            RoleAssignment.objects.filter(user=user, role__company=user.company)
            .values_list("role_id", flat=True)
            .distinct()
        )

        if not user_roles:
            # User has no roles assigned
            return False

        # Check if any of the user's roles have the required permission for Project model
        return ModelPermission.objects.filter(
            role_id__in=user_roles,
            content_type=content_type,
            **{permission_field: True},
        ).exists()

    @hook(AFTER_SAVE)
    def save_plan_project(self):
        from apps.planner.models import PlanProject

        for plan_project in PlanProject.objects.filter(project=self):
            plan_project.save()  # Update status and ranks by saving

    @hook(BEFORE_SAVE)
    def set_phase(self):
        if (
            self.project_state
            not in [
                self.PROJECT_STATE_DRAFT,
                self.PROJECT_STATE_SUBMITTED,
                self.PROJECT_STATE_DEFERRED,
                self.PROJECT_STATE_ACTIVE,
            ]
            and self.phase != self.PHASE_IDEA
        ):
            self.phase = None

    @hook(AFTER_SAVE)
    def set_capex_expenditure(self):
        if self.projectcapitalexpenditure_set.exclude(forecast=0, actuals=0).count():
            self.capital_expenditure = True

    @hook(AFTER_SAVE)
    def set_opex_expenditure(self):
        if self.projectoperationalexpenditure_set.exclude(
            forecast=0, actuals=0
        ).count():
            self.opex_expenditure = True

    @hook(BEFORE_SAVE)
    def set_payback_period(self):
        if self.annualized_savings == self.MONEY_AMOUNT_NONE:
            self.payback_period = self.PAYBACK_PERIOD_NONE

    @hook(BEFORE_SAVE)
    def set_car_number(self):
        if not self.capital_expenditure:
            self.car_number = None

    @hook(BEFORE_SAVE)
    def set_expense_io_number(self):
        if not self.capital_expenditure:
            self.expense_io_number = None

    @hook(BEFORE_SAVE)
    def set_capital_io_number(self):
        if not self.capital_expenditure:
            self.capital_io_number = None

    @hook(BEFORE_SAVE)
    def set_capital_forecast(self):
        if not self.capital_expenditure:
            self.capital_forecast = None

    @hook(BEFORE_SAVE)
    def set_company_code(self):
        if not self.opex_expenditure:
            self.company_code = None

    @hook(BEFORE_SAVE)
    def set_cost_center(self):
        if not self.opex_expenditure:
            self.cost_center = None

    @hook(BEFORE_SAVE)
    def set_gl_account(self):
        if not self.opex_expenditure:
            self.gl_account = None

    @hook(BEFORE_SAVE)
    def set_opex_forecast(self):
        if not self.opex_expenditure:
            self.opex_forecast = None

    @hook(BEFORE_SAVE)
    def set_technology_components(self):
        if not self.has_technology_components:
            self.technology_components = ""

    @hook(AFTER_SAVE, when="id", is_not=None)
    def set_health(self):
        if not self.pk:
            return
        project_health = self.current_health or ProjectHealth(
            project=self, year=timezone.now().year, week=timezone.now().isocalendar()[1]
        )
        health_updated = False
        if self.project_state != self.PROJECT_STATE_ACTIVE:
            project_health.budget_health = ProjectHealth.HEALTH.NA
            project_health.schedule_health = ProjectHealth.HEALTH.NA
            project_health.scope_health = ProjectHealth.HEALTH.NA
            project_health.health = ProjectHealth.HEALTH.NA
            health_updated = True
        else:
            if project_health.budget_health in [None, "", ProjectHealth.HEALTH.NA]:
                project_health.budget_health = ProjectHealth.HEALTH.GREEN
                health_updated = True
            if project_health.schedule_health in [None, "", ProjectHealth.HEALTH.NA]:
                project_health.schedule_health = ProjectHealth.HEALTH.GREEN
                health_updated = True
            if project_health.scope_health in [None, "", ProjectHealth.HEALTH.NA]:
                project_health.scope_health = ProjectHealth.HEALTH.GREEN
                health_updated = True
        if health_updated or not project_health.pk:
            project_health.save()

    @hook(AFTER_SAVE, when="id", is_not=None)
    def set_financial_tables(self):
        with transaction.atomic():
            start_date = self.start_date
            end_date = self.end_date
            if not start_date or not end_date:
                return
            for year, month in month_year_iter(
                start_date.month, start_date.year, end_date.month, end_date.year
            ):
                ProjectCapitalExpenditure.objects.get_or_create(
                    project=self, year=year, month=month
                )
                ProjectOperationalExpenditure.objects.get_or_create(
                    project=self, year=year, month=month
                )

    @hook(AFTER_SAVE, when="id", is_not=None)
    def set_savings_table(self):
        with transaction.atomic():
            start_date = self.savings_start_date
            end_date = self.savings_end_date
            if not start_date or not end_date:
                return
            for year, month in month_year_iter(
                start_date.month, start_date.year, end_date.month, end_date.year
            ):
                ProjectSavings.objects.get_or_create(
                    project=self, year=year, month=month
                )

    @hook(AFTER_SAVE, when="id", is_not=None)
    def set_location_people(self):
        if (
            self.location
            and self.active
            and self.phase
            in [
                self.PHASE_PROPOSE,
                self.PHASE_INITIATION,
                self.PHASE_PLANNING,
                self.PHASE_EXECUTION,
            ]
        ):
            self.location_engineers.set(list(self.location.location_engineer.all()))
            if self.location.location_manager:
                self.location_managers.set([self.location.location_manager])
            if self.location.operations_director:
                self.operations_directors.set([self.location.operations_director])
            if self.location.engineering_director:
                self.engineer_directors.set([self.location.engineering_director])
            self.vps.set(list(self.location.vp_operations.all()))
            self.svps.set(list(self.location.svp_operations.all()))
            self.coos.set(list(self.location.coo.all()))

    def duplicate(self, duplicator: Optional[User] = None) -> Project:
        duplicate_project = Project.objects.create(
            name=f"COPY - {self.name}",
            program=self.program,
            private=self.private,
            project_rigor=self.project_rigor,
            primary_division=self.primary_division,
            summary=f"<p>COPIED from ID: {self.id}</p>{self.summary}",
            business_case=(
                f"<p>COPIED from ID: {self.id}</p>{self.business_case}"
                if self.business_case
                else ""
            ),
            expected_duration=self.expected_duration,
            internal_savings_initiative=self.internal_savings_initiative,
            committed_to_spend=self.committed_to_spend,
            funding_size=self.funding_size,
            estimation_confidence=self.estimation_confidence,
            annualized_savings=self.annualized_savings,
            capital_budget=self.capital_budget,
            expense_budget=self.expense_budget,
            funding_source=self.funding_source,
            payback_period=self.payback_period,
            capital_expenditure=self.capital_expenditure,
            opex_expenditure=self.opex_expenditure,
            company_code=self.company_code,
            cost_center=self.cost_center,
            has_technology_components=self.has_technology_components,
            technology_components=self.technology_components,
            sap_impact=self.sap_impact,
            priority=self.priority,
            complexity=self.complexity,
            corporate_communication_involvement=self.corporate_communication_involvement,
            corporate_communication_needs=self.corporate_communication_needs,
            corporate_communication_needs_description=self.corporate_communication_needs_description,
            response_to_audit=self.response_to_audit,
            current_environment=self.current_environment,
            failure_severity=self.failure_severity,
            location=self.location,
            created_by=duplicator if duplicator else self.created_by,
        )
        for tag in self.tags.all():
            duplicate_project.tags.add(tag)
        for project_type in self.project_types.all():
            duplicate_project.project_types.add(project_type)
        for subpillar in self.strategic_subpillars.all():
            duplicate_project.strategic_subpillars.add(subpillar)
        for division in self.other_involved_divisions.all():
            duplicate_project.other_involved_divisions.add(division)
        for user in self.executive_owners.all():
            duplicate_project.executive_owners.add(user)
        for user in self.business_leads.all():
            duplicate_project.business_leads.add(user)
        for user in self.business_analysts.all():
            duplicate_project.business_analysts.add(user)
        for user in self.finance_leads.all():
            duplicate_project.finance_leads.add(user)
        for user in self.other_stakeholders.all():
            duplicate_project.other_stakeholders.add(user)

        for user in self.project_managers.all():
            duplicate_project.project_managers.add(user)
        ProjectPercentComplete.objects.create(project=duplicate_project)
        ProjectHealth.objects.create(project=duplicate_project)
        return duplicate_project


__all__ = [
    "Project",
    "WeeklyModel",
    "ProjectRisk",
    "ProjectOperationsNeed",
    "ProjectHealth",
    "ProjectPercentComplete",
    "ProjectRecentAccomplishment",
    "ProjectPlannedActivity",
    "ProjectExecutiveAction",
    "ProjectDivisionImpact",
    "ProjectSupportImpact",
    "ProjectSavings",
    "ProjectCapitalExpenditure",
    "ProjectOperationalExpenditure",
    "ProjectType",
    "ProjectLink",
    "ProjectImportData",
    "ProjectCapitalExpenditureManager",
    "ProjectAttachment",
    "BusinessSegmentPerson",
    "Division",
    "StrategicPillar",
    "BusinessSegmentPerson",
    "Person",
]
