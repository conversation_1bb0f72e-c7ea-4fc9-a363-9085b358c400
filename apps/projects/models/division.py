from __future__ import annotations

from typing import cast

import reversion
from django.db import models
from django.db.models import QuerySet
from django.db.models.aggregates import Count, Sum
from django.db.models.expressions import Case, Value, When


class DivisionQuerySet(QuerySet):
    def with_number_of_change_requests(self) -> DivisionQuerySet:
        queryset = self.annotate(
            change_requests=Count("servicenowchangerequest")
        ).annotate(hours=Sum("servicenowchangerequest__estimated_hours"))
        return cast(DivisionQuerySet, queryset)

    def with_selected_ids(self, ids) -> DivisionQuerySet:
        queryset = self.annotate(
            selected=Case(
                When(id__in=ids, then=Value(1)),
                default=Value(0),
                output_field=models.IntegerField(),
            )
        )
        return cast(DivisionQuerySet, queryset)


class DivisionManager(models.Manager):
    def get_queryset(self) -> DivisionQuerySet:
        return DivisionQuerySet(self.model, using=self._db)

    def with_number_of_change_requests(self) -> DivisionQuerySet:
        return self.get_queryset().with_number_of_change_requests()

    def with_selected_ids(self, ids) -> DivisionQuerySet:
        return self.get_queryset().with_selected_ids(ids)


@reversion.register()
class Division(models.Model):
    company = models.ForeignKey(
        "organizations.Company", on_delete=models.CASCADE, related_name="divisions"
    )
    name = models.CharField(max_length=100)
    abbr = models.CharField(max_length=100, blank=True, null=True)
    objects = DivisionManager()

    class Meta:
        ordering = ["name"]

    def __str__(self):
        return self.abbreviation

    @property
    def abbreviation(self) -> str:
        return self.abbr or self.name
