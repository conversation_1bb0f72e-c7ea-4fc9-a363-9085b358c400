from itertools import groupby
from typing import Iterable, List

from django.db.models import Q

from apps.users.models import User

from apps.sitewide.view_models import HomeViewProject

from .models import Project


def get_project_rigor_completed_step_list(project: Project) -> List[int]:
    project_fields = (
        project.expected_duration,
        project.funding_size,
        project.departments_involved,
        project.vendors_involved,
        project.associated_projects,
        project.schedule_motivation,
        project.similar_experience,
        project.infrastructure_readiness,
        project.external_user_privileges,
        project.post_project_support,
    )
    completed_list = (field is not None for field in project_fields)
    return [index + 1 for index, completed in enumerate(completed_list) if completed]


def has_perms(project, user):
    if not project.private:
        return True

    user_person = User.objects.get(email=user.email)

    user_is_creator = project.created_by == user
    people_with_access = []

    for person in project.executive_owners.all():
        people_with_access.append(person.id)

    for person in project.project_managers.all():
        people_with_access.append(person.id)

    for person in project.business_leads.all():
        people_with_access.append(person.id)

    for person in project.business_analysts.all():
        people_with_access.append(person.id)

    for person in project.other_stakeholders.all():
        people_with_access.append(person.id)

    for person in project.finance_leads.all():
        people_with_access.append(person.id)

    return user.is_superuser or user_is_creator or user_person.id in people_with_access


def has_edit_delete_perms(project, user):
    user_person = User.objects.get(email=user.email)

    user_is_creator = project.created_by == user
    people_with_access = []

    for person in project.executive_owners.all():
        people_with_access.append(person.id)

    for person in project.project_managers.all():
        people_with_access.append(person.id)

    for person in project.business_leads.all():
        people_with_access.append(person.id)

    for person in project.business_analysts.all():
        people_with_access.append(person.id)

    for person in project.finance_leads.all():
        people_with_access.append(person.id)

    return user.is_superuser or user_is_creator or user_person.id in people_with_access


def group_project_by_division(projects: Iterable[Project]):
    projects = sorted(projects, key=lambda x: getattr(x.primary_division, "name", ""))
    project_list = [HomeViewProject.from_project(project) for project in projects]
    grouped_projects = groupby(project_list, key=lambda x: x.primary_division)
    return [
        {"name": grouper.name, "abbr": grouper.abbr, "project_group_list": list(value)}
        for grouper, value in grouped_projects
    ]


def generate_raid_excel_sheet(workbook, queryset, show_closed):
    if not show_closed:
        queryset = queryset.filter(
            Q(risk__status="Active")
            | Q(issue__status="Active")
            | Q(assumption__status="Unvalidated")
            | Q(dependency__status="Active")
        )

    risk_sheet = workbook.active
    assumption_sheet = workbook.create_sheet()
    issue_sheet = workbook.create_sheet()
    dependency_sheet = workbook.create_sheet()
    risk_sheet.title = "Risks"
    assumption_sheet.title = "Assumption"
    issue_sheet.title = "Issue"
    dependency_sheet.title = "Dependency"

    risk_sheet["A1"] = "Area"
    risk_sheet["B1"] = "Date Identified"
    risk_sheet["C1"] = "Identified By"
    risk_sheet["D1"] = "Risk Description"
    risk_sheet["E1"] = "Likelihood Of Occurrence"
    risk_sheet["F1"] = "Impact"
    risk_sheet["G1"] = "Risk Rating"
    risk_sheet["H1"] = "Impact Date"
    risk_sheet["I1"] = "Owner"
    risk_sheet["J1"] = "Mitigation Strategy"
    risk_sheet["K1"] = "Status"

    for index, obj in enumerate(queryset.filter(type="risk")):
        risk_sheet[f"A{index + 2}"] = obj.risk.section
        risk_sheet[f"B{index + 2}"] = obj.risk.date_identified
        risk_sheet[f"C{index + 2}"] = obj.risk.identifier.full_name
        risk_sheet[f"D{index + 2}"] = obj.risk.risk_description
        risk_sheet[f"E{index + 2}"] = obj.risk.likelihood
        risk_sheet[f"F{index + 2}"] = obj.risk.impact
        risk_sheet[f"G{index + 2}"] = obj.risk.risk_rating
        risk_sheet[f"H{index + 2}"] = obj.risk.impact_date
        risk_sheet[f"I{index + 2}"] = obj.risk.risk_owner.full_name
        risk_sheet[f"J{index + 2}"] = obj.risk.mitigation
        risk_sheet[f"K{index + 2}"] = obj.risk.status

    assumption_sheet["A1"] = "Area"
    assumption_sheet["B1"] = "Date Identified"
    assumption_sheet["C1"] = "Identified By"
    assumption_sheet["D1"] = "Assumption Description"
    assumption_sheet["E1"] = "Reason"
    assumption_sheet["F1"] = "Action To Validate"
    assumption_sheet["G1"] = "Impact if Incorrect"
    assumption_sheet["H1"] = "Validated By"
    assumption_sheet["I1"] = "Owner"
    assumption_sheet["J1"] = "Close By"
    assumption_sheet["K1"] = "Impact"
    assumption_sheet["L1"] = "Status"

    for index, obj in enumerate(queryset.filter(type="assumption")):
        assumption_sheet[f"A{index + 2}"] = obj.assumption.section
        assumption_sheet[f"B{index + 2}"] = obj.assumption.date_identified
        assumption_sheet[f"C{index + 2}"] = obj.assumption.identifier.full_name
        assumption_sheet[f"D{index + 2}"] = obj.assumption.assumption
        assumption_sheet[f"E{index + 2}"] = obj.assumption.reason
        assumption_sheet[f"F{index + 2}"] = obj.assumption.validation_action
        assumption_sheet[f"G{index + 2}"] = obj.assumption.impact_if_incorrect
        assumption_sheet[f"H{index + 2}"] = (
            obj.assumption.validated_by.full_name
            if obj.assumption.validated_by
            else None
        )
        assumption_sheet[f"I{index + 2}"] = obj.assumption.owner.full_name
        assumption_sheet[f"J{index + 2}"] = obj.assumption.close_by
        assumption_sheet[f"K{index + 2}"] = obj.assumption.priority
        assumption_sheet[f"L{index + 2}"] = obj.assumption.status

    issue_sheet["A1"] = "Area"
    issue_sheet["B1"] = "Date Identified"
    issue_sheet["C1"] = "Identified By"
    issue_sheet["D1"] = "Issue Description"
    issue_sheet["E1"] = "Owner"
    issue_sheet["F1"] = "Corrective Action"
    issue_sheet["G1"] = "Close By"
    issue_sheet["H1"] = "Rating"
    issue_sheet["I1"] = "Status"

    for index, obj in enumerate(queryset.filter(type="issue")):
        issue_sheet[f"A{index + 2}"] = obj.issue.section
        issue_sheet[f"B{index + 2}"] = obj.issue.date_identified
        issue_sheet[f"C{index + 2}"] = obj.issue.identifier.full_name
        issue_sheet[f"D{index + 2}"] = obj.issue.issue_description
        issue_sheet[f"E{index + 2}"] = obj.issue.issue_owner.full_name
        issue_sheet[f"F{index + 2}"] = obj.issue.corrective_action
        issue_sheet[f"G{index + 2}"] = obj.issue.close_by
        issue_sheet[f"H{index + 2}"] = obj.issue.priority
        issue_sheet[f"I{index + 2}"] = obj.issue.status

    dependency_sheet["A1"] = "Area"
    dependency_sheet["B1"] = "Date Identified"
    dependency_sheet["C1"] = "Identified By"
    dependency_sheet["D1"] = "Type"
    dependency_sheet["E1"] = "Dependency Description"
    dependency_sheet["F1"] = "Dependency Effect"
    dependency_sheet["G1"] = "Owner"
    dependency_sheet["H1"] = "Close By"
    dependency_sheet["I1"] = "Impact"
    dependency_sheet["J1"] = "Status"

    for index, obj in enumerate(queryset.filter(type="dependency")):
        dependency_sheet[f"A{index + 2}"] = obj.dependency.section
        dependency_sheet[f"B{index + 2}"] = obj.dependency.date_identified
        dependency_sheet[f"C{index + 2}"] = obj.dependency.identifier.full_name
        dependency_sheet[f"D{index + 2}"] = obj.dependency.dependency_type
        dependency_sheet[f"E{index + 2}"] = obj.dependency.dependency
        dependency_sheet[f"F{index + 2}"] = obj.dependency.impact
        dependency_sheet[f"G{index + 2}"] = obj.dependency.dependency_owner.full_name
        dependency_sheet[f"H{index + 2}"] = obj.dependency.close_by
        dependency_sheet[f"I{index + 2}"] = obj.dependency.priority
        dependency_sheet[f"J{index + 2}"] = obj.dependency.status

    return workbook
