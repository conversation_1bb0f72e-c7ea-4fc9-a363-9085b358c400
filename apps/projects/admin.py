from django.contrib import admin

from .models import (
    Project,
    Division,
    StrategicPillar,
    BusinessSegment,
    BusinessSegmentPerson,
    ProjectType,
    ProjectRecentAccomplishment,
    ProjectPlannedActivity,
    SubPillar,
    Pod,
    ProjectAttachment,
    ProjectExecutiveAction,
    ProjectCapitalExpenditure,
    ProjectOperationalExpenditure,
    ProjectHealth,
    ProjectDivisionImpact,
    ProjectSupportImpact,
    ProjectImportData,
    InScope,
    ProjectLink,
    ProjectRisk,
    MeasureOfSuccess,
    ProjectOperationsNeed,
    OutOfScope,
    ProjectPercentComplete,
)


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    pass


@admin.register(Division)
class DivisionAdmin(admin.ModelAdmin):
    pass


@admin.register(StrategicPillar)
class StrategicPillarAdmin(admin.ModelAdmin):
    pass


@admin.register(SubPillar)
class SubPillarAdmin(admin.ModelAdmin):
    pass


@admin.register(BusinessSegment)
class BusinessSegmentAdmin(admin.ModelAdmin):
    pass


@admin.register(BusinessSegmentPerson)
class BusinessSegmentPersonAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectType)
class ProjectTypeAdmin(admin.ModelAdmin):
    pass


@admin.register(Pod)
class PodAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectAttachment)
class ProjectAttachmentAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectExecutiveAction)
class ProjectExecutiveActionAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectCapitalExpenditure)
class ProjectCapitalExpenditureAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectOperationalExpenditure)
class ProjectOperationalExpenditureAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectHealth)
class ProjectHealthAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectDivisionImpact)
class ProjectDivisionImpactAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectSupportImpact)
class ProjectSupportImpactAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectImportData)
class ProjectImportDataAdmin(admin.ModelAdmin):
    pass


@admin.register(InScope)
class InScopeAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectLink)
class ProjectLinkAdmin(admin.ModelAdmin):
    pass


@admin.register(MeasureOfSuccess)
class MeasureOfSuccessAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectRisk)
class ProjectRiskAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectRecentAccomplishment)
class ProjectRecentAccomplishmentAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectOperationsNeed)
class ProjectOperationsNeedAdmin(admin.ModelAdmin):
    pass


@admin.register(OutOfScope)
class OutOfScopeAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectPercentComplete)
class ProjectPercentCompleteAdmin(admin.ModelAdmin):
    pass


@admin.register(ProjectPlannedActivity)
class ProjectPlannedActivityAdmin(admin.ModelAdmin):
    pass
