import logging
from datetime import datetime
from typing import List, NamedTuple, Optional

import humanize
from django.urls import reverse
from django.utils.functional import cached_property
from lxml import html
from rest_framework import serializers
from rest_framework_csv.renderers import CSVRenderer

from apps.locations.models import Location
from apps.projects.models import Pod
from apps.users.models import User

from .models import (
    Division,
    Project,
    ProjectExecutiveAction,
    ProjectHealth,
    ProjectPercentComplete,
    ProjectPlannedActivity,
    ProjectRecentAccomplishment,
    ProjectType,
    StrategicPillar,
)

logger = logging.getLogger("site")


class ProjectPersonSerializer(serializers.ModelSerializer):
    # this serializer removes the validators on the email property for validation purposes, because we get_or_create in the ProjectSerializer
    class Meta:
        model = User
        fields = ["first_name", "last_name", "email"]
        extra_kwargs = {"email": {"validators": []}}


class ProjectDivisionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Division
        fields = ["name"]


class PillarSubPillarSerializer(serializers.Serializer):
    pillar = serializers.CharField()
    name = serializers.CharField()


class ProjectTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProjectType
        fields = ["name"]


class ProjectPodSerializer(serializers.ModelSerializer):
    class Meta:
        model = Pod
        fields = ["name"]


class ProjectSerializer(serializers.ModelSerializer):
    executive_owners = ProjectPersonSerializer(many=True, required=False)
    finance_leads = ProjectPersonSerializer(many=True, required=False)
    business_leads = ProjectPersonSerializer(many=True, required=False)
    business_analysts = ProjectPersonSerializer(many=True, required=False)
    other_stakeholders = ProjectPersonSerializer(many=True, required=False)

    project_managers = ProjectPersonSerializer(many=True, required=False)
    project_types = ProjectTypeSerializer(many=True, required=False)
    created_by = ProjectPersonSerializer()
    url = serializers.SerializerMethodField()
    primary_division = serializers.CharField(required=False, allow_null=True)
    strategic_subpillars = PillarSubPillarSerializer(
        many=True, required=False, allow_null=True
    )
    location = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = Project
        fields = [
            "url",
            "id",
            "idea_id",
            "name",
            "location",
            "summary",
            "primary_division",
            "start_date",
            "executive_owners",
            "finance_leads",
            "project_managers",
            "business_leads",
            "business_analysts",
            "other_stakeholders",
            "internal_savings_initiative",
            "committed_to_spend",
            "capital_expenditure",
            "opex_expenditure",
            "annualized_savings",
            "funding_size",
            "funding_source",
            "payback_period",
            "has_technology_components",
            "technology_components",
            "complexity",
            "created_by",
            "priority",
            "tags",
            "current_environment",
            "failure_severity",
            "response_to_audit",
            "estimation_confidence",
            "business_case",
            "private",
            "strategic_subpillars",
            "project_types",
            "ready_to_begin",
            "phase",
        ]

    @staticmethod
    def make_people(data):
        people_list = []
        if data:
            for elem in data:
                try:
                    obj = User.objects.get(email=elem["email"])
                except User.DoesNotExist:
                    obj = User.objects.create(**elem)
                    logger.info(
                        f"User with email {elem['email']} does not exist.  Creating."
                    )

                people_list.append(obj)
        return people_list

    @staticmethod
    def make_user(data):
        if data:
            try:
                user = User.objects.get(email=data["email"])
            except User.DoesNotExist:
                user = User.objects.create(**data)
                logger.info(
                    f"User with email {data['email']} does not exist.  Creating."
                )
            return user
        return None

    @staticmethod
    def make_pillar(data):
        pillar_list = []
        if not data:
            return None
        for elem in data:
            pillar, created = StrategicPillar.objects.get_or_create(name=elem["pillar"])
            pillar_list.append(pillar)
        return pillar_list

    @staticmethod
    def make_project_types(data):
        project_type_list = []
        if not data:
            return project_type_list
        for elem in data:
            project_type, created = ProjectType.objects.get_or_create(name=elem["name"])
            project_type_list.append(project_type)
        return project_type_list

    @staticmethod
    def make_location(name):
        if name is None:
            return None
        location, created = Location.objects.get_or_create(name=name)
        return location

    @staticmethod
    def get_url(obj):
        return reverse("project_detail", kwargs={"pk": obj.id})[1:]

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        rep["tags"] = [tag.name for tag in rep["tags"].all()]
        return rep

    def create(self, validated_data):
        project_types = validated_data.pop("project_types", None)
        project_type_list = self.make_project_types(project_types)

        executive_owners = validated_data.pop("executive_owners", None)
        executive_owner_list = self.make_people(executive_owners)

        finance_leads = validated_data.pop("finance_leads", None)
        finance_lead_list = self.make_people(finance_leads)

        business_leads = validated_data.pop("business_leads", None)
        business_lead_list = self.make_people(business_leads)

        business_analysts = validated_data.pop("business_analysts", None)
        business_analyst_list = self.make_people(business_analysts)

        project_managers = validated_data.pop("project_managers", None)
        project_manager_list = self.make_people(project_managers)

        other_stakeholders = validated_data.pop("other_stakeholders", None)
        stakeholder_list = self.make_people(other_stakeholders)

        strategic_pillar_subpillar_data = validated_data.pop(
            "strategic_subpillars", None
        )
        pillars = self.make_pillar(strategic_pillar_subpillar_data)

        created_by_data = validated_data.pop("created_by")
        self.make_people([created_by_data])

        # created_by = self.make_user(validated_data.pop("created_by", None))
        created_by = self.make_user(created_by_data)

        primary_division = validated_data.pop("primary_division", None)

        location = validated_data.pop("location", None)
        location_obj = self.make_location(location)

        project = Project.objects.create(created_by=created_by, **validated_data)

        if location_obj:
            project.location = location_obj

        if primary_division:
            try:
                division = Division.objects.get(name=primary_division)
                project.primary_division = division
            except Division.DoesNotExist:
                pass

        if pillars:
            project.strategic_pillars.set(pillars)

        project.project_types.add(*project_type_list)
        project.executive_owners.add(*executive_owner_list)
        project.finance_leads.add(*finance_lead_list)
        project.business_leads.add(*business_lead_list)
        project.business_analysts.add(*business_analyst_list)
        project.other_stakeholders.add(*stakeholder_list)

        project.project_managers.add(*project_manager_list)
        project.save()

        ProjectHealth.objects.create(project=project)
        ProjectPercentComplete.objects.create(project=project, percentage=0)

        return project


class ProjectExportCSVRenderer(CSVRenderer):
    header = [
        "id",
        "name",
        # "project_type",
        # "location",
        "program",
        "health",
        "complete",
        "capital_actuals_total",
        "operational_actuals_total",
        "strategic_value",
        "project_state",
        "phase",
        "project_rigor",
        "primary_division",
        "other_involved_divisions",
        "strategic_pillar",
        "project_summary",
        "business_case",
        "executive_owners",
        "business_leads",
        "business_analysts",
        "finance_leads",
        "other_stakeholders",
        "project_manager",
        "expected_duration",
        "start_date",
        "end_date",
        "savings_start_date",
        "savings_end_date",
        "initiation_date",
        "planning_date",
        "execution_date",
        "control_date",
        "internal_savings_initiative",
        "committed_to_spend",
        "funding_size",
        "annualized_savings",
        "capital_budget",
        "expense_budget",
        "funding_source",
        "payback_period",
        "capital_expenditure",
        "capital_forecast_total",
        "car_number",
        "expense_io_number",
        "capital_io_number",
        "opex_expenditure",
        "operational_forecast_total",
        "company_code",
        "cost_center",
        "gl_account",
        "has_technology_components",
        "sap_impact",
        "priority",
        "complexity",
        "division_impact",
        "support_impact",
        "last_update",
        "comments",
        "tags",
    ]
    labels = {
        "id": "Project #",
        "name": "Project Name",
        # "project_type": "Project Type",
        # "location": "Location",
        "program": "Program",
        "health": "Health",
        "complete": "Complete",
        "capital_actuals_total": "Capital Actuals",
        "operational_actuals_total": "OpEx Actuals",
        "strategic_value": "Strategic Value",
        "project_state": "Project State",
        "phase": "Phase",
        "project_rigor": "Project Rigor",
        "primary_division": "Primary Segment",
        "other_involved_divisions": "Other Involved Segments",
        "strategic_pillar": "Strategic Pillar",
        "project_summary": "Project Summary",
        "business_case": "Business Case",
        "executive_owners": "Executive Owner",
        "finance_leads": "Finance Lead",
        "business_leads": "Business Lead",
        "business_analysts": "Business Analyst",
        "other_stakeholders": "Additional Stakeholders",
        "project_manager": "Project Manager",
        "expected_duration": "Expected Duration",
        "start_date": "Start Date",
        "end_date": "End Date",
        "savings_start_date": "Benefit Tracking Start Date",
        "savings_end_date": "Benefit Tracking End Date",
        "initiation_date": "Initiation",
        "planning_date": "Planning",
        "execution_date": "Execution",
        "control_date": "Close",
        "internal_savings_initiative": "Hard Dollar Savings",
        "committed_to_spend": "Committed to Spend",
        "funding_size": "Funding Size",
        "annualized_savings": "Annualized Savings",
        "capital_budget": "Capital Budget",
        "expense_budget": "Expense Budget",
        "funding_source": "Funding Source",
        "payback_period": "Payback Period",
        "capital_expenditure": "Capital Expenditure",
        "capital_forecast_total": "Capital Forecast",
        "car_number": "CAR Number",
        "expense_io_number": "Expense IO Number",
        "capital_io_number": "Capital IO Number",
        "opex_expenditure": "OpEx Expenditure",
        "operational_forecast_total": "OpEx Forecast",
        "company_code": "Company Code",
        "cost_center": "Cost Center",
        "gl_account": "GL Account",
        "has_technology_components": "Technology Components",
        "sap_impact": "SAP Impact",
        "priority": "Priority",
        "complexity": "Complexity",
        "division_impact": "Segment Impact",
        "support_impact": "Support Impact",
        "last_update": "Last Update",
        "comments": "Comments",
        "tags": "Tags",
    }


class ProjectExportSerializer(serializers.ModelSerializer):
    capital_actuals_total = serializers.IntegerField()
    operational_actuals_total = serializers.IntegerField()
    capital_forecast_total = serializers.IntegerField()
    operational_forecast_total = serializers.IntegerField()
    # location = serializers.SerializerMethodField()
    project_type = serializers.SerializerMethodField()
    strategic_value = serializers.SerializerMethodField()
    project_state = serializers.SerializerMethodField()
    phase = serializers.SerializerMethodField()
    project_rigor = serializers.SerializerMethodField()
    primary_division = serializers.SerializerMethodField()
    other_involved_divisions = serializers.SerializerMethodField()
    strategic_pillar = serializers.SerializerMethodField()
    priority = serializers.SerializerMethodField()
    complexity = serializers.SerializerMethodField()
    project_summary = serializers.SerializerMethodField()
    business_case = serializers.SerializerMethodField()
    health = serializers.SerializerMethodField()
    executive_owners = serializers.SerializerMethodField()
    finance_leads = serializers.SerializerMethodField()
    business_leads = serializers.SerializerMethodField()
    business_analysts = serializers.SerializerMethodField()
    other_stakeholders = serializers.SerializerMethodField()

    project_manager = serializers.SerializerMethodField()
    internal_savings_initiative = serializers.SerializerMethodField()
    committed_to_spend = serializers.SerializerMethodField()
    funding_size = serializers.SerializerMethodField()
    annualized_savings = serializers.SerializerMethodField()
    capital_budget = serializers.SerializerMethodField()
    expense_budget = serializers.SerializerMethodField()
    funding_source = serializers.SerializerMethodField()
    payback_period = serializers.SerializerMethodField()
    capital_expenditure = serializers.SerializerMethodField()
    opex_expenditure = serializers.SerializerMethodField()
    company_code = serializers.SerializerMethodField()
    start_date = serializers.DateField(format="%m/%d/%Y")
    end_date = serializers.DateField(format="%m/%d/%Y")
    savings_start_date = serializers.DateField(format="%m/%d/%Y")
    savings_end_date = serializers.DateField(format="%m/%d/%Y")
    initiation_date = serializers.SerializerMethodField()
    planning_date = serializers.SerializerMethodField()
    execution_date = serializers.SerializerMethodField()
    control_date = serializers.SerializerMethodField()
    expected_duration = serializers.SerializerMethodField()
    complete = serializers.SerializerMethodField()
    has_technology_components = serializers.SerializerMethodField()
    sap_impact = serializers.SerializerMethodField()
    division_impact = serializers.IntegerField(source="division_impact_total")
    support_impact = serializers.IntegerField(source="support_impact_total")
    last_update = serializers.DateTimeField(source="modified", format="%m/%d/%Y")
    program = serializers.SerializerMethodField()
    comments = serializers.SerializerMethodField()
    tags = serializers.SerializerMethodField()
    created = serializers.DateTimeField(format="%m/%d/%Y")

    class Meta:
        model = Project
        fields = [
            "id",
            "name",
            "project_type",
            # "location",
            "health",
            "complete",
            "capital_actuals_total",
            "operational_actuals_total",
            "strategic_value",
            "project_state",
            "phase",
            "project_rigor",
            "primary_division",
            "other_involved_divisions",
            "strategic_pillar",
            "project_summary",
            "business_case",
            "executive_owners",
            "finance_leads",
            "business_leads",
            "business_analysts",
            "other_stakeholders",
            "project_manager",
            "expected_duration",
            "start_date",
            "end_date",
            "savings_start_date",
            "savings_end_date",
            "initiation_date",
            "planning_date",
            "execution_date",
            "control_date",
            "internal_savings_initiative",
            "committed_to_spend",
            "funding_size",
            "annualized_savings",
            "capital_budget",
            "expense_budget",
            "funding_source",
            "payback_period",
            "capital_expenditure",
            "capital_forecast_total",
            "car_number",
            "expense_io_number",
            "capital_io_number",
            "opex_expenditure",
            "operational_forecast_total",
            "company_code",
            "cost_center",
            "gl_account",
            "has_technology_components",
            "sap_impact",
            "priority",
            "complexity",
            "division_impact",
            "support_impact",
            "last_update",
            "program",
            "comments",
            "tags",
            "created",
        ]

    def get_tags(self, obj):
        return ",".join(tag.name for tag in obj.tags.all())

    def get_project_type(self, obj: Project) -> str:
        if obj.project_types.count():
            return "; ".join(
                [project_type.name for project_type in obj.project_types.all()]
            )

    # def get_location(self, obj: Project) -> str:
    #     return obj.location.name if obj.location else ""

    def get_comments(self, obj: Project):
        comments = obj.comments.all().order_by("-created")[:3]
        comment_lines = [
            f"{comment.creator.full_name} ({comment.created.strftime('%b %d %Y %H:%M:%S')}): {comment.text}"
            for comment in comments[:3]
        ]
        return ", ".join(comment_lines)

    def get_program(self, obj: Project):
        if obj.program and obj.program.active:
            return obj.program.name
        return ""

    def get_strategic_value(self, obj: Project):
        return obj.get_strategic_value()

    def get_project_state(self, obj: Project) -> str:
        return obj.project_state_display

    def get_phase(self, obj: Project) -> str:
        return obj.phase_display

    def get_project_rigor(self, obj: Project) -> str:
        return obj.project_rigor_display

    def get_primary_division(self, obj: Project) -> str:
        return obj.primary_division.name if obj.primary_division else ""

    def get_other_involved_divisions(self, obj: Project) -> str:
        return ", ".join(
            [division.name for division in obj.other_involved_divisions.all()]
        )

    def get_strategic_pillar(self, obj: Project) -> str:
        return ", ".join([str(i) for i in obj.strategic_pillars.all()])

    def get_priority(self, obj: Project) -> str:
        return obj.priority_display

    def get_complexity(self, obj: Project) -> str:
        return obj.complexity_display

    def get_project_summary(self, obj: Project) -> str:
        doc = html.fragment_fromstring(obj.summary, create_parent=True)
        return doc.text_content()

    def get_business_case(self, obj: Project) -> str:
        if not obj.business_case:
            return ""
        doc = html.fragment_fromstring(obj.business_case, create_parent=True)
        return doc.text_content()

    def get_health(self, obj: Project) -> str:
        return ProjectHealth.HEALTH_DICT.get(obj.latest_health)

    def get_executive_owners(self, obj: Project) -> str:
        return ", ".join([person.full_name for person in obj.executive_owners.all()])

    def get_finance_leads(self, obj: Project) -> str:
        return ", ".join([person.full_name for person in obj.finance_leads.all()])

    def get_business_leads(self, obj: Project) -> str:
        return ", ".join([person.full_name for person in obj.business_leads.all()])

    def get_business_analysts(self, obj: Project) -> str:
        return ", ".join([person.full_name for person in obj.business_analysts.all()])

    def get_other_stakeholders(self, obj: Project) -> str:
        return ", ".join([person.full_name for person in obj.other_stakeholders.all()])

    def get_project_manager(self, obj: Project) -> str:
        return ", ".join([person.full_name for person in obj.project_managers.all()])

    def get_internal_savings_initiative(self, obj: Project) -> str:
        return "Yes" if obj.internal_savings_initiative else "No"

    def get_committed_to_spend(self, obj: Project) -> str:
        return "Yes" if obj.committed_to_spend else "No"

    def get_complete(self, obj: Project) -> str:
        return f"{obj.latest_percentage}%"

    def get_funding_size(self, obj: Project) -> str:
        return obj.funding_size_display

    def get_annualized_savings(self, obj: Project) -> str:
        return obj.annualized_savings_display

    def get_capital_budget(self, obj: Project) -> str:
        return obj.capital_budget_display

    def get_expense_budget(self, obj: Project) -> str:
        return obj.expense_budget_display

    def get_funding_source(self, obj: Project) -> str:
        return obj.funding_source_display

    def get_payback_period(self, obj: Project) -> str:
        return obj.payback_period_display

    def get_capital_expenditure(self, obj: Project) -> str:
        return "Yes" if obj.capital_expenditure else "No"

    def get_opex_expenditure(self, obj: Project) -> str:
        return "Yes" if obj.opex_expenditure else "No"

    def get_initiation_date(self, obj: Project) -> str:
        return obj.initiation_date.strftime("%m/%d/%Y") if obj.initiation_date else ""

    def get_planning_date(self, obj: Project) -> str:
        return obj.planning_date.strftime("%m/%d/%Y") if obj.planning_date else ""

    def get_execution_date(self, obj: Project) -> str:
        return obj.execution_date.strftime("%m/%d/%Y") if obj.execution_date else ""

    def get_control_date(self, obj: Project) -> str:
        return obj.control_date.strftime("%m/%d/%Y") if obj.control_date else ""

    def get_expected_duration(self, obj: Project) -> str:
        return obj.expected_duration_display

    def get_company_code(self, obj: Project) -> str:
        return obj.company_code_display

    def get_has_technology_components(self, obj: Project) -> str:
        return "Yes" if obj.has_technology_components else "No"

    def get_sap_impact(self, obj: Project) -> str:
        return "Yes" if obj.sap_impact else "No"


class ProjectStatusChanges(NamedTuple):
    recent_accomplishment_modified: Optional[datetime]
    planned_activity_modified: Optional[datetime]
    executive_action_modified: Optional[datetime]
    percent_complete: int


class ProjectChangesSerializer(serializers.ModelSerializer):
    executive_owners = serializers.SerializerMethodField()
    finance_leads = serializers.SerializerMethodField()
    business_leads = serializers.SerializerMethodField()
    business_analysts = serializers.SerializerMethodField()
    other_stakeholders = serializers.SerializerMethodField()

    project_managers = serializers.SerializerMethodField()
    health = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    executive_actions = serializers.SerializerMethodField()
    recent_accomplishments = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = [
            "executive_owners",
            "finance_leads",
            "business_leads",
            "business_analysts",
            "other_stakeholders",
            "project_managers",
            "has_technology_components",
            "project_state",
            "phase",
            "health",
            "status",
            "corporate_communication_needs",
            "executive_actions",
            "recent_accomplishments",
        ]

    def get_health(self, obj: Project) -> str:
        current_health = obj.current_health
        if not current_health:
            return ""
        return current_health.health

    def get_executive_owners(self, obj: Project) -> List[User]:
        return list(obj.executive_owners.all())

    def get_finance_leads(self, obj: Project) -> List[User]:
        return list(obj.finance_leads.all())

    def get_business_leads(self, obj: Project) -> List[User]:
        return list(obj.business_leads.all())

    def get_business_analysts(self, obj: Project) -> List[User]:
        return list(obj.business_analysts.all())

    def get_other_stakeholders(self, obj: Project) -> List[User]:
        return list(obj.other_stakeholders.all())

    def get_project_managers(self, obj: Project) -> List[User]:
        return list(obj.project_managers.all())

    def get_status(self, obj: Project) -> ProjectStatusChanges:
        recent_accomplishment: Optional[ProjectRecentAccomplishment] = (
            obj.projectrecentaccomplishment_set.order_by("-modified").first()
        )
        recent_accomplishment_modified = (
            recent_accomplishment.modified if recent_accomplishment else None
        )
        planned_activity: Optional[ProjectPlannedActivity] = (
            obj.projectplannedactivity_set.order_by("-modified").first()
        )
        planned_activity_modified = (
            planned_activity.modified if planned_activity else None
        )
        executive_action: Optional[ProjectExecutiveAction] = (
            obj.projectexecutiveaction_set.order_by("-modified").first()
        )
        executive_action_modified = (
            executive_action.modified if executive_action else None
        )
        percent_complete = (
            obj.percent_complete.percentage if obj.percent_complete else 0
        )
        return ProjectStatusChanges(
            recent_accomplishment_modified,
            planned_activity_modified,
            executive_action_modified,
            percent_complete,
        )

    def get_executive_actions(self, obj: Project) -> List[ProjectExecutiveAction]:
        return list(obj.executive_actions.all())

    def get_recent_accomplishments(
        self, obj: Project
    ) -> List[ProjectRecentAccomplishment]:
        return list(obj.recent_accomplishments.all())


class PersonSerializer(serializers.ModelSerializer):
    is_admin = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = User
        fields = [
            "id",
            "first_name",
            "last_name",
            "email",
            "is_compliance",
            "is_finance",
            "is_solutions_architecture",
            "is_admin",
            "active",
        ]

    @cached_property
    def users_dict(self):
        return {user.email: user for user in User.objects.all()}

    def get_is_admin(self, instance):
        user = self.users_dict.get(instance.email)
        if not user:
            return False
        return user.is_superuser


class ProjectPDFSerializer(serializers.ModelSerializer):
    percent_complete = serializers.SerializerMethodField()
    phase = serializers.SerializerMethodField()
    start_date = serializers.SerializerMethodField()
    end_date = serializers.SerializerMethodField()
    accomplishments = serializers.SerializerMethodField()
    activities = serializers.SerializerMethodField()
    executive_actions = serializers.SerializerMethodField()
    get_to_green = serializers.SerializerMethodField()
    overall_health = serializers.SerializerMethodField()
    budget_health = serializers.SerializerMethodField()
    schedule_health = serializers.SerializerMethodField()
    scope_health = serializers.SerializerMethodField()
    capex_spend = serializers.SerializerMethodField()
    opex_spend = serializers.SerializerMethodField()
    capex_budget = serializers.SerializerMethodField()
    opex_budget = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = [
            "id",
            "name",
            "phase",
            "percent_complete",
            "start_date",
            "end_date",
            "accomplishments",
            "activities",
            "executive_actions",
            "get_to_green",
            "capex_spend",
            "capex_budget",
            "opex_spend",
            "opex_budget",
            "overall_health",
            "budget_health",
            "schedule_health",
            "scope_health",
        ]

    def get_percent_complete(self, instance):
        if instance.percent_complete:
            return f"{instance.percent_complete.percentage}% Complete"
        return " "

    def get_phase(self, instance):
        if instance.phase:
            return instance.phase
        return " "

    def get_start_date(self, instance):
        if instance.start_date:
            return instance.start_date.strftime("%-m/%-d/%Y")
        return " "

    def get_end_date(self, instance):
        if instance.end_date:
            return instance.end_date.strftime("%-m/%-d/%Y")
        return " "

    def get_accomplishments(self, instance):
        if instance.projectrecentaccomplishment_set.filter(active=True).count():
            return [
                accomplishment.text
                for accomplishment in instance.projectrecentaccomplishment_set.filter(
                    active=True
                )
            ]
        return None

    def get_activities(self, instance):
        if instance.projectplannedactivity_set.filter(active=True).count():
            return [
                activity.text
                for activity in instance.projectplannedactivity_set.filter(active=True)
            ]
        return None

    def get_executive_actions(self, instance):
        if instance.projectexecutiveaction_set.filter(active=True).count():
            return [
                {"action": executive_action.action, "text": executive_action.text}
                for executive_action in instance.projectexecutiveaction_set.filter(
                    active=True
                )
            ]
        return None

    def get_get_to_green(self, instance):
        if len(instance.get_to_green) > 0:
            return instance.get_to_green
        return "N/A"

    def get_overall_health(self, instance):
        return instance.projecthealth_set.first().health

    def get_budget_health(self, instance):
        return instance.projecthealth_set.first().budget_health

    def get_schedule_health(self, instance):
        return instance.projecthealth_set.first().schedule_health

    def get_scope_health(self, instance):
        return instance.projecthealth_set.first().scope_health

    def get_capex_spend(self, instance):
        capex_spend = instance.get_capital_actuals_total()
        return f"${humanize.intcomma(capex_spend)}"

    def get_capex_budget(self, instance):
        capex_budget = instance.get_capital_forecast_total()
        return f"${humanize.intcomma(capex_budget)}"

    def get_opex_spend(self, instance):
        opex_spend = instance.get_operational_actuals_total()
        return f"${humanize.intcomma(opex_spend)}"

    def get_opex_budget(self, instance):
        opex_budget = instance.get_operational_forecast_total()
        return f"${humanize.intcomma(opex_budget)}"
