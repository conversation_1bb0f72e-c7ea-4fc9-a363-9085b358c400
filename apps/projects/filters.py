import operator
from typing import List
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from functools import reduce
from typing import Iterable

import django_filters as filters
from django.db.models import OuterRef, Q, Subquery
from django.db.models.query import QuerySet
from django.utils import timezone

from apps.locations.models import Location

from .models import (
    Division,
    ProjectHealth,
    ProjectPercentComplete,
    ProjectType,
    SubPillar,
)
from apps.users.models import User, Role

PHASE_INITIATION = "initiation"
PHASE_PLANNING = "planning"
PHASE_EXECUTION = "execution"
PHASE_CONTROL = "control"
FILTER_PHASE_CHOICES = (
    (PHASE_INITIATION, "Initiation"),
    (PHASE_PLANNING, "Planning"),
    (PHASE_EXECUTION, "Execution"),
    (PHASE_CONTROL, "Close"),
)

PROJECT_STATE_ACTIVE = "active"
PROJECT_STATE_COMPLETE = "complete"
PROJECT_STATE_CANCELLED = "cancelled"
PROJECT_STATE_ON_HOLD = "on hold"
PROJECT_STATE_INACTIVE = "inactive"

FILTER_PROJECT_STATE_CHOICES = (
    (PROJECT_STATE_ACTIVE, "Active"),
    (PROJECT_STATE_COMPLETE, "Complete"),
    (PROJECT_STATE_CANCELLED, "Cancelled"),
    (PROJECT_STATE_ON_HOLD, "On Hold"),
)


class ProjectOrderingFilter(filters.OrderingFilter):
    def filter(self, qs, value):
        if value:
            if "business_lead" in value:
                return qs.order_by("business_leads__first_name")
            elif "-business_lead" in value:
                return qs.order_by("-business_leads__first_name")
            elif "finance_lead" in value:
                return qs.order_by("finance_leads__first_name")
            elif "-finance_lead" in value:
                return qs.order_by("-finance_leads__first_name")
            elif "project_manager" in value:
                return qs.order_by("project_managers__first_name")
            elif "-project_manager" in value:
                return qs.order_by("-project_managers__first_name")
            elif "executive_sponsor" in value:
                return qs.order_by("executive_owners__first_name")
            elif "-executive_sponsor" in value:
                return qs.order_by("-executive_owners__first_name")
            elif "strategic_value" in value:
                return qs.order_by("strategic_value")
            elif "-strategic_value" in value:
                return qs.order_by("-strategic_value")
            elif "primary_division" in value:
                return qs.order_by("primary_division__name")
            elif "-primary_division" in value:
                return qs.order_by("-primary_division__name")
        return super().filter(qs, value)


class ProjectFilter(filters.FilterSet):
    id = filters.AllValuesMultipleFilter()
    starred = filters.BooleanFilter(method="filter_starred")
    health = filters.MultipleChoiceFilter(
        choices=ProjectHealth.HEALTH.choices, method="filter_health"
    )
    phase = filters.MultipleChoiceFilter(
        method="filter_phase", choices=FILTER_PHASE_CHOICES + (("none", "None"),)
    )
    state = filters.MultipleChoiceFilter(
        field_name="project_state", choices=FILTER_PROJECT_STATE_CHOICES
    )
    project_type = filters.MultipleChoiceFilter(
        field_name="project_type",
        choices=[],
        method="filter_project_type",
    )
    complete = filters.RangeFilter(method="filter_complete")
    created_within = filters.NumberFilter(method="filter_created_within")
    project_search = filters.CharFilter(method="filter_search")
    program = filters.NumberFilter(method="filter_program")
    location = filters.MultipleChoiceFilter()
    segment = filters.CharFilter(method="filter_segment")
    department = filters.MultipleChoiceFilter(method="filter_department", choices=[])
    department_is_primary = filters.CharFilter(method="filter_department_is_primary")
    person = filters.ModelChoiceFilter(
        queryset=User.objects.all(), method="filter_person"
    )
    role = filters.ModelMultipleChoiceFilter(
        queryset=Role.objects.none(), method="filter_role"
    )
    strategic_subpillars = filters.ModelMultipleChoiceFilter(
        queryset=SubPillar.objects.all(), method="filter_strategic_subpillars"
    )
    without_program = filters.BooleanFilter(method="filter_without_program")

    order_by = ProjectOrderingFilter(
        fields=[
            "starred",
            "id",
            "latest_health_value",
            "name",
            "program",
            "end_date",
            "latest_percentage",
            "phase",
            "strategic_value",
            "modified",
            "primary_division",
        ]
    )

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)

        self.filters["location"].extra["choices"] = (
            *[(location.id, location.name) for location in Location.objects.all()],
        )

        # Filter divisions by user's company
        divisions = Division.objects.for_company(self.user.company if self.user else None)
        self.filters["department"].extra["choices"] = [
            *[(department.id, department.name) for department in divisions]
        ]

        # Set up role choices based on user's company
        if self.user and self.user.company:
            self.filters["role"].queryset = Role.objects.filter(
                company=self.user.company
            ).order_by("name")

        self.project_type_choices = [
            (project_type.id, project_type.name)
            for project_type in ProjectType.objects.order_by("name")
        ]

    def filter_strategic_subpillars(
        self, queryset: QuerySet, name: str, values
    ) -> QuerySet:
        if values:
            q_list = []
            for value in values:
                if value.name == "any":
                    q_list.append(Q(strategic_subpillars__pillar=value.pillar))
                else:
                    q_list.append(Q(strategic_subpillars=value))
            return queryset.filter(reduce(operator.or_, q_list))
        return queryset

    def filter_starred(self, queryset: QuerySet, name: str, value: bool) -> QuerySet:
        if value:
            queryset = queryset.filter(starred=True)
        return queryset

    def filter_program(self, queryset: QuerySet, name: str, value: Decimal) -> QuerySet:
        if value:
            queryset = queryset.filter(program__pk=value)
        return queryset

    def filter_health(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        queryset = queryset.filter(latest_health__in=value)
        return queryset

    def filter_project_type(
        self, queryset: QuerySet, name: str, value: str
    ) -> QuerySet:
        queryset = queryset.filter(project_types__in=value)
        return queryset

    def filter_phase(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        if "none" in value:
            return queryset.filter(Q(phase__isnull=True) | Q(phase__in=value))
        return queryset.filter(phase__in=value)

    def filter_complete(self, queryset: QuerySet, name: str, value: range) -> QuerySet:
        latest = ProjectPercentComplete.objects.filter(project=OuterRef("pk")).order_by(
            "-modified"
        )
        min_value = value.start or 0
        max_value = value.stop or 100
        queryset = queryset.annotate(
            latest_percentage=Subquery(latest.values("percentage")[:1])
        ).filter(latest_percentage__range=(min_value, max_value))
        return queryset

    def filter_created_within(self, queryset: QuerySet, name: str, value: Decimal):
        queryset = queryset.filter(
            created__gte=timezone.now() - timedelta(days=int(value))
        )
        return queryset

    def filter_search(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        return queryset.search(value)

    # this is used on the Dashboard page, different from list/report pages.
    def filter_segment(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        if value == "My Projects" and self.user:
            return queryset.with_my_projects(user=self.user)
        return queryset.filter(primary_division__name=value)

    def filter_department_is_primary(
        self, queryset: QuerySet, name: str, value: str
    ) -> QuerySet:
        return queryset

    def filter_department(
        self, queryset: QuerySet, name: str, value: Iterable[str]
    ) -> QuerySet:
        filters = []
        is_unassigned = "0" in value
        if is_unassigned:
            value.remove("0")
        if len(value) > 0:
            filters.append(Q(primary_division__id__in=value))
        if "department_is_primary" in self.data.keys():
            filters.append(Q(other_involved_divisions__in=value))
        if is_unassigned:
            filters.append(Q(primary_division__isnull=True))
        return queryset.filter(reduce(operator.or_, filters))

    def filter_person(self, queryset: QuerySet, name: str, value: User) -> QuerySet:
        """
        Filter projects by person.
        If roles are also selected, only show projects where the person has those roles.
        Otherwise, show all projects where the person is a team member.
        """
        if not value:
            return queryset

        # Get selected roles if any
        selected_roles = None
        if "role" in self.data.keys():
            role_ids = self.data.getlist("role", None)
            if role_ids:
                selected_roles = Role.objects.filter(id__in=role_ids)

        # Filter by team membership
        queryset = queryset.filter(team_members=value)

        # If specific roles are selected, further filter by those roles
        if selected_roles:
            from apps.users.models import RoleAssignment

            # Check if the person actually has any of the selected roles
            person_has_roles = RoleAssignment.objects.filter(
                user=value, role__in=selected_roles
            ).exists()

            if not person_has_roles:
                # Person doesn't have any of the selected roles, return empty queryset
                return queryset.none()

            # Person has at least one of the selected roles
            # Filter projects where they have role assignments matching the selected roles
            queryset = queryset.filter(
                projectroleassignment__user=value,
                projectroleassignment__role__in=selected_roles,
            ).distinct()

        return queryset

    def filter_role(
        self, queryset: QuerySet, name: str, values: List[Role]
    ) -> QuerySet:
        """
        Filter projects by roles.
        When used with person filter: handled in filter_person method.
        When used alone: finds projects where users with these roles are assigned.
        """
        if not values:
            return queryset

        # If person filter is also applied, the role filtering is handled in filter_person
        if "person" in self.data.keys() and self.data.get("person"):
            return queryset

        # When used alone, find projects that have team members with these roles
        from apps.users.models import RoleAssignment

        users_with_roles = User.objects.filter(
            id__in=RoleAssignment.objects.filter(role__in=values).values_list(
                "user_id", flat=True
            )
        )

        # Filter projects that have any of these users as team members
        if users_with_roles.exists():
            return queryset.filter(team_members__in=users_with_roles).distinct()

        return queryset

    def filter_without_program(
        self, queryset: QuerySet, name: str, value: bool
    ) -> QuerySet:
        if value:
            queryset = queryset.filter(program__isnull=True)
        return queryset


class PersonOrderingFilter(filters.OrderingFilter):
    def filter(self, qs, value):
        if value:
            if "name" in value:
                return qs.order_by("first_name", "last_name")
            elif "-name" in value:
                return qs.order_by("-first_name", "-last_name")
            else:
                return qs.order_by(*value, "first_name", "last_name")

        return super().filter(qs, value)


class PersonFilter(filters.FilterSet):
    person_search = filters.CharFilter(method="filter_person_search")
    person = filters.ModelChoiceFilter(
        queryset=User.objects.all(), method="filter_person"
    )
    role = filters.ModelChoiceFilter(queryset=Role.objects.all(), method="filter_role")
    status = filters.MultipleChoiceFilter(
        choices=[("active", "Active"), ("inactive", "Inactive")], method="filter_status"
    )

    order_by = PersonOrderingFilter(fields=["name", "email", "role", "status"])

    def filter_person(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        if not value:
            return queryset

        return queryset.filter(pk=value.id)

    def filter_person_search(
        self, queryset: QuerySet, name: str, value: str
    ) -> QuerySet:
        return queryset.search(value)

    def filter_status(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        if len(value) > 1:
            return queryset
        if value[0] == "active":
            return queryset.filter(active=True)
        else:
            return queryset.filter(active=False)

    def filter_role(self, queryset: QuerySet, name: str, value: Role) -> QuerySet:
        """Filter users by role through RoleAssignment"""
        if not value:
            return queryset

        # Import here to avoid circular imports
        from apps.users.models import RoleAssignment

        # Get user IDs that have this role assignment
        user_ids = RoleAssignment.objects.filter(role=value).values_list(
            "user_id", flat=True
        )

        return queryset.filter(id__in=user_ids)


class PersonAPIFilter(filters.FilterSet):
    id = filters.AllValuesMultipleFilter()
    role = filters.ModelChoiceFilter(queryset=Role.objects.all(), method="filter_role")
    active = filters.BooleanFilter()

    def filter_role(self, queryset: QuerySet, name: str, value: Role) -> QuerySet:
        """Filter users by role through RoleAssignment"""
        if not value:
            return queryset

        # Import here to avoid circular imports
        from apps.users.models import RoleAssignment

        # Get user IDs that have this role assignment
        user_ids = RoleAssignment.objects.filter(role=value).values_list(
            "user_id", flat=True
        )

        return queryset.filter(id__in=user_ids)
