import logging
from django.urls import reverse
from rest_framework import status
from rest_framework.authentication import SessionAuthentication
from rest_framework.generics import (
    CreateAPIView,
    ListAPIView,
    ListCreateAPIView,
    RetrieveAPIView,
    UpdateAPIView,
)
from rest_framework.response import Response
from rest_framework.views import APIView
from viewclass_mixins.views import CorsMixin, HttpCacheMixin

from apps.actions.models import ActionType, record
from apps.notifications.models import MessageTypes, notify
from apps.projects.models.raid import RaidReport
from apps.reports.filters import RaidReportFilter
from apps.reports.serializers import RaidReportSerializer

from .filters import Person<PERSON><PERSON>ilter, ProjectFilter
from .models import Project
from apps.users.models import User
from .serializers import (
    PersonSerializer,
    ProjectExportSerializer,
    ProjectSerializer,
)

logger = logging.getLogger("site")


class ProjectListAPIView(CorsMixin, HttpCacheMixin, ListAPIView):
    """
    Retrieve a list a projects.
    """

    queryset = (
        Project.objects.select_related("primary_division")
        .filter(active=True)
        .with_latest_health()
        .with_latest_percentage()
    )
    serializer_class = ProjectSerializer
    filter_class = ProjectFilter
    authentication_classes = [SessionAuthentication]
    cache_timeout = 60
    cache_varies = ["Accept", "Authorization", "Origin"]

    def list(self, request, *args, **kwargs):
        query: str = request.GET.get("search", "")
        if query.isdigit() and Project.objects.filter(pk=query).exists():
            queryset = self.get_queryset().filter(pk=query)
        else:
            queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class ProjectListAPIView2(CorsMixin, HttpCacheMixin, ListAPIView):
    queryset = (
        Project.objects.select_related("primary_division")
        .filter(active=True)
        .with_latest_health()
        .with_strategic_value()
        .with_latest_percentage()
        .with_expenditures()
        .distinct()
    )
    serializer_class = ProjectExportSerializer
    authentication_classes = [SessionAuthentication]
    cache_timeout = 60
    cache_varies = ["Accept", "Authorization", "Origin"]

    def list(self, request, *args, **kwargs):
        query: str = request.GET.get("search", "")
        if query.isdigit() and Project.objects.filter(pk=query).exists():
            queryset = self.get_queryset().filter(pk=query)
        else:
            queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class ProjectDetailAPIView(CorsMixin, HttpCacheMixin, RetrieveAPIView):
    """
    Retrieve a project with the specified id.
    """

    queryset = Project.objects.filter(active=False)
    serializer_class = ProjectSerializer
    authentication_classes = [SessionAuthentication]
    cache_timeout = 60
    cache_varies = ["Accept", "Authorization", "Origin"]


class ProjectCreateAPIView(CorsMixin, CreateAPIView):
    """
    Create a project.
    """

    queryset = Project.objects.all()
    serializer_class = ProjectSerializer
    authentication_classes = [SessionAuthentication]

    def create_action_records(self, project: Project):
        record(
            project=project,
            action_type=ActionType.CREATED_PROJECT,
            editor=project.created_by,
            description="Project was converted",
        )
        for person in project.executive_owners.all():
            text = f"{person.full_name} was assigned to Executive Owner"
            record(
                project=project,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=project.created_by,
                description=text,
            )
        for person in project.finance_leads.all():
            text = f"{person.full_name} was assigned to Finance Lead"
            record(
                project=project,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=project.created_by,
                description=text,
            )
        for person in project.business_leads.all():
            text = f"{person.full_name} was assigned to Business Lead"
            record(
                project=project,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=project.created_by,
                description=text,
            )
        for person in project.business_analysts.all():
            text = f"{person.full_name} was assigned to Business Analyst"
            record(
                project=project,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=project.created_by,
                description=text,
            )
        for person in project.other_stakeholders.all():
            text = f"{person.full_name} was assigned to Additional Stakeholder"
            record(
                project=project,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=project.created_by,
                description=text,
            )

        for person in project.project_managers.all():
            text = f"{person.full_name} was assigned to Project Manager"
            record(
                project=project,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=project.created_by,
                description=text,
            )
        for executive_action in project.executive_actions.all():
            text = f"Executive Action Needed: {executive_action.action_display} - {executive_action.text}"
            record(
                project=project,
                action_type=ActionType.ADDED_EXECUTIVE_ACTION,
                editor=project.created_by,
                description=text,
            )
        for recent_accomplishment in project.recent_accomplishments.all():
            text = f"Recent Accomplishment: {recent_accomplishment.text}"
            record(
                project=project,
                action_type=ActionType.ADDED_RECENT_ACCOMPLISHMENT,
                editor=project.created_by,
                description=text,
            )
        if project.has_technology_components:
            record(
                project=project,
                action_type=ActionType.CHANGED_TECHNICAL_COMPONENT,
                editor=project.created_by,
            )
        if project.corporate_communication_needs:
            record(
                project=project,
                action_type=ActionType.NEEDS_CORPORATE_COMMUNICATION,
                editor=project.created_by,
            )

    def create_notifications(self, project: Project):
        message = "{user} added <a href='{url}'>{name}</a>.".format(
            user=project.created_by,
            url=reverse("project_detail", args=[project.pk]),
            name=project.name,
        )
        notify(
            message_type=MessageTypes.PROJECT_NEW,
            message=message,
            creator=project.created_by,
            content_object=project,
        )

    def create(self, request, *args, **kwargs):
        serializer = ProjectSerializer(data=request.data)
        serializer_valid = serializer.is_valid()
        logger.info(f"ProjectCreateAPIView - Serializer Valid: {serializer_valid}")
        if serializer_valid:
            self.perform_create(serializer)
            data = serializer.data.copy()
            project = Project.objects.get(pk=data["id"])
            project_tags = request.data.get("tags", None)
            if project_tags:
                project.tags.add(*project_tags)
            data["tags"] = [tag.name for tag in project.tags.all()]
            if project.phase == Project.PHASE_INITIATION:
                self.create_action_records(project)
                self.create_notifications(project)
            headers = self.get_success_headers(data)
            return Response(data, status=status.HTTP_201_CREATED, headers=headers)
        logger.error(f"ProjectCreateAPIView - Serializer errors: {serializer.errors}")
        return Response(
            data={"errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST
        )


class ProjectUpdateAPIView(CorsMixin, UpdateAPIView):
    """
    Update a project with the specified id.
    """

    queryset = Project.objects.filter(active=True)
    serializer_class = ProjectSerializer
    authentication_classes = [SessionAuthentication]


class PersonListCreateAPIView(CorsMixin, HttpCacheMixin, ListCreateAPIView):
    """
    Retrieve a list a people or create a person.
    """

    queryset = User.objects.all()
    filter_class = PersonAPIFilter
    serializer_class = PersonSerializer
    pagination_class = None
    authentication_classes = [SessionAuthentication]
    cache_timeout = 10
    cache_varies = ["Accept", "Authorization", "Origin"]


class PersonDetailAPIView(CorsMixin, HttpCacheMixin, RetrieveAPIView):
    """
    Retrieve a person with the specified id.
    """

    queryset = User.objects.all()
    serializer_class = PersonSerializer
    authentication_classes = [SessionAuthentication]
    cache_timeout = 10
    cache_varies = ["Accept", "Authorization", "Origin"]


class PersonDetailByEmailAPIView(CorsMixin, HttpCacheMixin, RetrieveAPIView):
    """
    Retrieve a person with the specified email.
    """

    lookup_field = "email"
    queryset = User.objects.all()
    serializer_class = PersonSerializer
    authentication_classes = [SessionAuthentication]
    cache_timeout = 10
    cache_varies = ["Accept", "Authorization", "Origin"]


class PersonEmailAvailableAPIView(CorsMixin, HttpCacheMixin, APIView):
    authentication_classes = [SessionAuthentication]
    cache_timeout = 10
    cache_varies = ["Accept", "Authorization", "Origin"]

    def get(self, request, email, *args, **kwargs):
        available = not User.objects.filter(email=email).exists()
        return Response(data=available)


class RaidItemAPIView(CorsMixin, HttpCacheMixin, ListAPIView):
    authentication_classes = [SessionAuthentication]
    cache_varies = ["Accept", "Authorization", "Origin"]
    filter_class = RaidReportFilter
    serializer_class = RaidReportSerializer
    queryset = RaidReport.objects.select_related(
        "project_number", "risk", "issue", "assumption", "dependency"
    ).filter(
        project_number__active=True,
        project_number__project_state=Project.PROJECT_STATE_ACTIVE,
    )
