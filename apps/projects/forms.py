import json
from typing import Optional, Any

import reversion
from django import forms
from django.core.exceptions import ImproperlyConfigured
from django.db.models import Case, IntegerField, OuterRef, Q, Subquery, Value, When
from django.utils import timezone

from django.db import transaction


from apps.attachments.models import Attachment
from apps.links.models import Link
from apps.programs.models import Program
from apps.projects.models import InScope, MeasureOfSuccess, OutOfScope
from apps.users.models import User, Role, RoleAssignment
from apps.utils.fields import CheckboxSelect, HTMLField, TagField, USCurrencyField
from apps.utils.forms import TagWidget

from .models import (
    BusinessSegment,
    BusinessSegmentPerson,
    Division,
    Project,
    ProjectCapitalExpenditure,
    ProjectDivisionImpact,
    ProjectExecutiveAction,
    ProjectHealth,
    ProjectLink,
    ProjectOperationalExpenditure,
    ProjectPercentComplete,
    ProjectPlannedActivity,
    ProjectRecentAccomplishment,
    ProjectSavings,
    ProjectSupportImpact,
    ProjectType,
    StrategicPillar,
)
from apps.users.models import User
from .models.raid import RaidAssumption, RaidDependencies, RaidIssues, RaidRisk


def build_subpillar_choice(subpillar):
    if subpillar.name == subpillar.pillar.name:
        return {
            "value": subpillar.id,
            "label": subpillar.pillar.name,
            "chipLabel": subpillar.pillar.name,
        }
    return {
        "value": subpillar.id,
        "label": f"&nbsp;&nbsp;&nbsp{subpillar.name}",
        "chipLabel": f"{subpillar.pillar.name}; {subpillar.name}",
    }


def build_subpillar_choices(field):
    return [build_subpillar_choice(subpillar) for subpillar in field.queryset]


def order_pillar_subpillar_for_faceted_field(queryset):
    is_same = StrategicPillar.objects.filter(name=OuterRef("name"))
    queryset = queryset.annotate(
        is_top_level=Case(
            When(pillar__name=Subquery(is_same.values("name")[:1]), then=Value("0")),
            default=Value("1"),
            output_field=IntegerField(),
        )
    )
    return queryset.order_by("pillar__name", "is_top_level", "name")


class ProjectForm(forms.ModelForm):
    PROJECT_STATE_CHOICES = (
        (Project.PROJECT_STATE_ACTIVE, "Active"),
        (Project.PROJECT_STATE_COMPLETE, "Complete"),
        (Project.PROJECT_STATE_CANCELLED, "Cancelled"),
        (Project.PROJECT_STATE_ON_HOLD, "On Hold"),
    )
    PHASE_CHOICES = (
        (Project.PHASE_INITIATION, "Initiation"),
        (Project.PHASE_PLANNING, "Planning"),
        (Project.PHASE_EXECUTION, "Execution"),
        (Project.PHASE_CONTROL, "Close"),
    )

    summary = HTMLField(
        label="Project Summary", help_text=Project._meta.get_field("summary").help_text
    )
    tags = TagField(
        required=False,
        widget=TagWidget(
            attrs={"data-tagify": True, "placeholder": "add comma separated tags"}
        ),
    )
    business_case = HTMLField(
        required=False,
        label="Business Case",
        help_text=Project._meta.get_field("business_case").help_text,
    )
    private = forms.ChoiceField(
        choices=[(False, "All Members"), (True, "Project Team")],
        required=False,
        label="Visibility",
        widget=forms.RadioSelect(attrs={"class": "SelectButtons"}),
    )
    budget_health = forms.ChoiceField(
        choices=ProjectHealth.HEALTH.choices,
        required=False,
        widget=forms.RadioSelect(attrs={"class": "SelectButtons"}),
    )
    schedule_health = forms.ChoiceField(
        choices=ProjectHealth.HEALTH.choices,
        required=False,
        widget=forms.RadioSelect(attrs={"class": "SelectButtons"}),
    )
    scope_health = forms.ChoiceField(
        choices=ProjectHealth.HEALTH.choices,
        required=False,
        widget=forms.RadioSelect(attrs={"class": "SelectButtons"}),
    )
    health = forms.ChoiceField(
        choices=ProjectHealth.HEALTH.choices,
        label="Overall Health",
        required=False,
        widget=forms.RadioSelect(attrs={"class": "SelectButtons"}),
    )
    modify_overall_health = forms.BooleanField(
        required=False, widget=forms.CheckboxInput(attrs={"switch": True})
    )
    percent_complete = forms.IntegerField(
        label="% Complete",
        min_value=0,
        max_value=100,
        initial=0,
        required=False,
        widget=forms.NumberInput(),
    )
    executive_owners = forms.ModelChoiceField(
        queryset=User.objects.none(),
        label="Executive Owner",
        required=False,
    )
    finance_leads = forms.ModelChoiceField(
        queryset=User.objects.none(),
        label="Finance Lead",
        required=False,
    )

    project_managers = forms.ModelChoiceField(
        queryset=User.objects.none(),
        label="Project Manager",
        required=False,
    )
    expense_io_number = forms.RegexField(
        regex=r"\d{6}",
        required=False,
        label="Expense IO Number",
        error_messages={"invalid": "Value must be 6 digits"},
        widget=forms.TextInput(attrs={"maxlength": 6}),
    )
    capital_io_number = forms.RegexField(
        regex=r"\d{6}",
        required=False,
        label="Capital IO Number",
        error_messages={"invalid": "Value must be 6 digits"},
        widget=forms.TextInput(attrs={"maxlength": 6}),
    )

    attachments = forms.ModelMultipleChoiceField(
        queryset=Attachment.objects.all(), required=False
    )
    links = forms.ModelMultipleChoiceField(queryset=Link.objects.all(), required=False)

    class Meta:
        model = Project
        fields = [
            "name",
            "project_rigor",
            "primary_division",
            "other_involved_divisions",
            "strategic_pillars",
            "summary",
            "private",
            "tags",
            "business_case",
            "business_analysts",
            "business_leads",
            "other_stakeholders",
            "internal_savings_initiative",
            "committed_to_spend",
            "funding_size",
            "annualized_savings",
            "capital_budget",
            "expense_budget",
            "funding_source",
            "payback_period",
            "capital_expenditure",
            "car_number",
            "expense_io_number",
            "capital_io_number",
            "opex_expenditure",
            "company_code",
            "cost_center",
            "gl_account",
            "expected_duration",
            "start_date",
            "end_date",
            "savings_start_date",
            "savings_end_date",
            "has_technology_components",
            "technology_components",
            "corporate_communication_needs",
            "sap_impact",
            "priority",
            "complexity",
            "project_state",
            "phase",
            "get_to_green",
            "current_environment",
            "failure_severity",
            "response_to_audit",
            "estimation_confidence",
            # "location",
            # NOTE: company field is intentionally excluded from fields list
        ]
        labels = {
            "name": "Project Name",
            "primary_division": "Primary Segment",
            "other_involved_divisions": "Other Involved Segments",
            "strategic_pillars": "Strategic Pillar",
            "other_stakeholders": "Additional Stakeholders",
            "car_number": "CAR Number",
            "opex_expenditure": "OpEx Expenditure",
            "gl_account": "GL Account",
            "internal_savings_initiative": "Hard Dollar Savings",
            "savings_start_date": "Benefit Tracking Start Date",
            "savings_end_date": "Benefit Tracking End Date",
            "has_technology_components": "Technology Components",
            "corporate_communication_needs": "What employee groups will need to understand that this project exists and how it will impact them?",
            "sap_impact": "SAP Impact",
            "get_to_green": "Get to Green",
            "current_environment": "Failure Risk",
            "response_to_audit": "Audit Finding Response",
        }
        help_texts = {
            "tags": "",
            "corporate_communication_needs": "Choose a group below that best describes the impact.",
        }
        widgets = {
            "project_rigor": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "internal_savings_initiative": CheckboxSelect(
                attrs={"class": "SelectButtons"}
            ),
            "committed_to_spend": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "expenditure_type": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "expected_duration": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "capital_expenditure": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "opex_expenditure": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "start_date": forms.DateInput(attrs={"type": "date"}),
            "end_date": forms.DateInput(attrs={"type": "date"}),
            "savings_start_date": forms.DateInput(attrs={"type": "date"}),
            "savings_end_date": forms.DateInput(attrs={"type": "date"}),
            "has_technology_components": CheckboxSelect(
                attrs={"class": "SelectButtons"}
            ),
            "corporate_communication_needs": CheckboxSelect(),
            "sap_impact": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "priority": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "complexity": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "project_state": forms.RadioSelect(attrs={"class": "SelectButtons"}),
            "phase": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "current_environment": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "failure_severity": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "response_to_audit": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "estimation_confidence": CheckboxSelect(attrs={"class": "SelectButtons"}),
        }

    def __init__(self, user: User, *args, **kwargs):
        self.instance: Project
        self.user = user
        super().__init__(*args, **kwargs)

        # Field Choices and Defaults
        self.fields["primary_division"].required = True
        self.fields["project_state"].choices = self.PROJECT_STATE_CHOICES
        self.fields["project_state"].required = False
        self.fields["phase"].choices = self.PHASE_CHOICES
        self.fields["project_rigor"].choices = Project.PROJECT_RIGOR_CHOICES
        self.fields["funding_size"].choices = (
            ("", "Select Amount"),
        ) + Project.MONEY_AMOUNT_CHOICES
        self.fields["annualized_savings"].choices = (
            ("", "Select Amount"),
        ) + Project.MONEY_AMOUNT_CHOICES
        self.fields["capital_budget"].choices = (
            ("", "Select Amount"),
        ) + Project.MONEY_AMOUNT_CHOICES
        self.fields["expense_budget"].choices = (
            ("", "Select Amount"),
        ) + Project.MONEY_AMOUNT_CHOICES
        self.fields["funding_source"].choices = (
            ("", "Select amount"),
        ) + Project.FUNDING_SOURCE_CHOICES
        self.fields["payback_period"].choices = Project.PAYBACK_PERIOD_CHOICES
        self.fields["expected_duration"].choices = Project.DURATION_CHOICES
        self.fields["internal_savings_initiative"].choices = (
            (False, "No"),
            (True, "Yes"),
        )
        self.fields["committed_to_spend"].choices = ((False, "No"), (True, "Yes"))
        self.fields["capital_expenditure"].choices = ((False, "No"), (True, "Yes"))
        self.fields["opex_expenditure"].choices = ((False, "No"), (True, "Yes"))
        self.fields["has_technology_components"].choices = (
            (False, "No"),
            (True, "Yes"),
        )
        self.fields[
            "corporate_communication_needs"
        ].choices = Project.COMMUNICATION_NEED_CHOICES
        self.fields["sap_impact"].choices = ((False, "No"), (True, "Yes"))
        self.fields["priority"].choices = Project.PRIORITY_CHOICES
        self.fields["complexity"].choices = Project.COMPLEXITY_CHOICES

        self.fields[
            "executive_owners"
        ].tooltip = "Senior leader with a direct interest in the business case behind the idea. They are responsible for securing the financing and overall resource budget for the initiative."

        self.fields[
            "business_analysts"
        ].tooltip = "Defines and documents business processes associated with the solution. Responsible for solution specifications and technology integration."
        self.fields[
            "finance_leads"
        ].tooltip = "Liaison from the Finance Segment for the initiative."
        self.fields[
            "business_leads"
        ].tooltip = (
            "Subject Matter Expert responsible for generating solution requirements."
        )
        self.fields[
            "other_stakeholders"
        ].tooltip = "Individuals with a vested interest in the initiative because they will be directly affected by its outcomes."

        self.fields[
            "project_managers"
        ].tooltip = "Person responsible for ensuring project meets schedule, scope, and budget expectations."

        self.fields["current_environment"].choices = Project.CURRENT_ENVIRONMENT_CHOICES
        self.fields[
            "current_environment"
        ].help_text = "What is the likelihood of current system failure?"
        self.fields["failure_severity"].choices = Project.FAILURE_SEVERITY_CHOICES
        self.fields["response_to_audit"].choices = Project.RESPONSE_TO_AUDIT_CHOICES
        self.fields[
            "estimation_confidence"
        ].choices = Project.ESTIMATION_CONFIDENCE_CHOICES

        self.fields[
            "estimation_confidence"
        ].tooltip = "<ul><li>Very High - Routine estimate</li><li>High - Sized some similar projects</li><li>Medium - Sized a similar project</li><li>Low - Sized some parts previously</li><li>Very Low - First time sizing a project like this</li></ul>"

        # Always filter User querysets by company, regardless of whether instance has pk
        base_person_queryset = User.objects.filter(
            company=self.user.company, is_active=True
        ).order_by("first_name", "last_name")

        print(base_person_queryset)

        # Executive Owners - active people in user's company
        self.fields["executive_owners"].queryset = base_person_queryset

        # Finance Leads - active people in user's company
        self.fields["finance_leads"].queryset = base_person_queryset

        # Business Leads - active people in user's company
        self.fields["business_leads"].queryset = base_person_queryset

        # Business Analysts - active people in user's company
        self.fields["business_analysts"].queryset = base_person_queryset

        # Other Stakeholders - active people in user's company
        self.fields["other_stakeholders"].queryset = base_person_queryset

        # Project Managers - active people in user's company
        self.fields["project_managers"].queryset = base_person_queryset

        # Set initial values if editing existing instance
        if self.instance.pk:
            self.fields[
                "executive_owners"
            ].initial = self.instance.executive_owners.first()
            self.fields["business_leads"].initial = self.instance.business_leads.first()
            self.fields["finance_leads"].initial = self.instance.finance_leads.first()

            self.fields[
                "project_managers"
            ].initial = self.instance.project_managers.first()

            if self.instance.current_health:
                self.fields[
                    "budget_health"
                ].initial = self.instance.current_health.budget_health
                self.fields[
                    "schedule_health"
                ].initial = self.instance.current_health.schedule_health
                self.fields[
                    "scope_health"
                ].initial = self.instance.current_health.scope_health
                self.fields["health"].initial = self.instance.current_health.health
                self.fields[
                    "modify_overall_health"
                ].initial = not self.instance.current_health.calculate_overall_health

            if self.instance.percent_complete:
                self.fields["percent_complete"].initial = (
                    self.instance.percent_complete.percentage
                    if self.instance.percent_complete
                    else 0
                )
        else:
            # New instance setup
            self.instance.created_by = user
            self.fields["private"].initial = False

        privacy_choice_dict = {
            "True": "Visible only to people assigned to roles.",
            "False": "Visible to everyone in the organization.",
        }
        default_subtitle = (
            privacy_choice_dict["False"]
            if not self.instance.private
            else privacy_choice_dict[str(self.instance.private)]
        )
        self.fields["private"].widget.attrs.update(
            {
                "default_subtitle": default_subtitle,
                "has_switchable_subtitles": True,
                "data-switchable_subtitles": json.dumps(privacy_choice_dict),
            }
        )
        # Admin Only Fields
        if not user.is_superuser:
            del self.fields["health"]
            del self.fields["modify_overall_health"]

    def clean(self):
        start_date = self.cleaned_data.get("start_date")
        end_date = self.cleaned_data.get("end_date")
        savings_start_date = self.cleaned_data.get("savings_start_date")
        savings_end_date = self.cleaned_data.get("savings_end_date")
        # if end_date and end_date < date.today():
        #     self.add_error("end_date", "Cannot set end date in the past.")
        if start_date and end_date and start_date >= end_date:
            self.add_error("end_date", "End Date must be later than start date.")
        if (
            savings_start_date
            and savings_end_date
            and savings_start_date >= savings_end_date
        ):
            self.add_error(
                "savings_end_date", "End Date must be later than start date."
            )

        modify_overall_health = self.cleaned_data.get("modify_overall_health", False)
        health_values = (
            [self.cleaned_data.get("health")]
            if modify_overall_health
            else [
                self.cleaned_data.get("budget_health"),
                self.cleaned_data.get("scope_health"),
                self.cleaned_data.get("schedule_health"),
            ]
        )
        get_to_green = self.cleaned_data.get("get_to_green")

        if get_to_green and (
            "yellow" not in health_values and "red" not in health_values
        ):
            self.cleaned_data["get_to_green"] = ""

        if not get_to_green and ("yellow" in health_values or "red" in health_values):
            self.add_error(
                "get_to_green", "Please explain what you will do to get to green"
            )

        summary = self.cleaned_data.get("summary")
        if not summary:
            self.add_error("summary", "This field is required.")

    def save(self, *args, **kwargs):
        with reversion.create_revision():
            attachments = []
            links = []

            if kwargs.get("commit", True):
                attachments = self.cleaned_data.pop("attachments", [])
                links = self.cleaned_data.pop("links", [])

            # Set the company to the user's company before saving
            # This ensures the company is always the user's company and cannot be changed
            if self.user.company:
                self.instance.company = self.user.company

            instance: Project = super().save(*args, **kwargs)

            for attachment in attachments:
                attachment.content_object = self.instance
                attachment.save()

            for link in links:
                link.content_object = self.instance
                link.save()

            if not kwargs.get("commit", True):
                return instance

            executive_owners: Optional[User] = self.cleaned_data.get("executive_owners")
            finance_leads: Optional[User] = self.cleaned_data.get("finance_leads")

            project_managers: Optional[User] = self.cleaned_data.get("project_managers")
            budget_health: str = self.cleaned_data.get(
                "budget_health", ProjectHealth.HEALTH.GREEN
            )
            schedule_health: str = self.cleaned_data.get(
                "schedule_health", ProjectHealth.HEALTH.GREEN
            )
            scope_health: str = self.cleaned_data.get(
                "scope_health", ProjectHealth.HEALTH.GREEN
            )
            health: str = self.cleaned_data.get("health")
            modify_overall_health = self.cleaned_data.get(
                "modify_overall_health", False
            )
            percent_complete: int = self.cleaned_data.get("percent_complete", 0)
            if not percent_complete:
                percent_complete = 0

            week = timezone.now().isocalendar()[1]
            year = timezone.now().year

            instance.executive_owners.clear()
            if executive_owners is not None:
                instance.executive_owners.add(executive_owners)
            instance.finance_leads.clear()
            if finance_leads is not None:
                instance.finance_leads.add(finance_leads)

            instance.project_managers.clear()
            if project_managers is not None:
                instance.project_managers.add(project_managers)

            try:
                project_health = ProjectHealth.objects.get(
                    project=instance, year=year, week=week
                )
            except ProjectHealth.DoesNotExist:
                project_health = ProjectHealth(project=instance, year=year, week=week)
            project_health.budget_health = budget_health or ProjectHealth.HEALTH.GREEN
            project_health.schedule_health = (
                schedule_health or ProjectHealth.HEALTH.GREEN
            )
            project_health.scope_health = scope_health or ProjectHealth.HEALTH.GREEN
            if health is not None:
                project_health.health = health
                project_health.calculate_overall_health = not modify_overall_health
            project_health.save()
            try:
                project_percent_complete = ProjectPercentComplete.objects.get(
                    project=instance, year=year, week=week
                )
            except ProjectPercentComplete.DoesNotExist:
                project_percent_complete = ProjectPercentComplete(
                    project=instance, year=year, week=week
                )
            project_percent_complete.percentage = percent_complete
            project_percent_complete.save()

        return instance


class ProjectExecutiveActionForm(forms.ModelForm):
    text = forms.CharField(
        max_length=250,
        widget=forms.Textarea(
            attrs={"class": "SortableForm--TextArea TextArea--autosize"}
        ),
    )

    class Meta:
        model = ProjectExecutiveAction
        fields = ["id", "text", "add_to_slide", "action", "weight"]
        widgets = {
            "action": forms.Select(
                attrs={"class": "ProjectForm-executiveAction-action-select"}
            ),
            "weight": forms.HiddenInput(),
        }


class ProjectPlannedActivityForm(forms.ModelForm):
    text = forms.CharField(
        max_length=250,
        widget=forms.Textarea(
            attrs={"class": "SortableForm--TextArea TextArea--autosize"}
        ),
    )

    class Meta:
        model = ProjectPlannedActivity
        fields = ["id", "text", "add_to_slide", "weight"]
        widgets = {"weight": forms.HiddenInput()}
        labels = {"add_to_slide": "Add Activity to slide"}


class ProjectRecentAccomplishmentForm(forms.ModelForm):
    text = forms.CharField(
        max_length=250,
        widget=forms.Textarea(
            attrs={"class": "SortableForm--TextArea TextArea--autosize"}
        ),
    )

    class Meta:
        model = ProjectRecentAccomplishment
        fields = ["id", "text", "add_to_slide", "weight"]
        widgets = {"weight": forms.HiddenInput()}
        labels = {"add_to_slide": "Add Accomplishment to slide"}


class ProjectInScopeForm(forms.ModelForm):
    text = forms.CharField(
        max_length=250,
        widget=forms.Textarea(
            attrs={"class": "SortableForm--TextArea TextArea--autosize"}
        ),
    )

    class Meta:
        model = InScope
        fields = ["id", "text"]
        widgets = {"weight": forms.HiddenInput()}


class ProjectOutOfScopeForm(forms.ModelForm):
    text = forms.CharField(
        max_length=250,
        widget=forms.Textarea(
            attrs={"class": "SortableForm--TextArea TextArea--autosize"}
        ),
    )

    class Meta:
        model = OutOfScope
        fields = ["id", "text"]
        widgets = {"weight": forms.HiddenInput()}


class ProjectMeasureOfSuccessForm(forms.ModelForm):
    text = forms.CharField(
        max_length=250,
        widget=forms.Textarea(
            attrs={"class": "SortableForm--TextArea TextArea--autosize"}
        ),
    )

    class Meta:
        model = MeasureOfSuccess
        fields = ["id", "text"]
        widgets = {"weight": forms.HiddenInput()}


class ProjectFinancialsSectionForm(forms.ModelForm):
    expense_io_number = forms.RegexField(
        regex=r"\d{6}",
        required=False,
        label="Expense IO Number",
        error_messages={"invalid": "Value must be 6 digits"},
        widget=forms.TextInput(attrs={"maxlength": 6}),
    )
    capital_io_number = forms.RegexField(
        regex=r"\d{6}",
        required=False,
        label="Capital IO Number",
        error_messages={"invalid": "Value must be 6 digits"},
        widget=forms.TextInput(attrs={"maxlength": 6}),
    )

    class Meta:
        model = Project
        fields = [
            "capital_expenditure",
            "car_number",
            "expense_io_number",
            "capital_io_number",
            "capital_forecast",
            "opex_expenditure",
            "company_code",
            "cost_center",
            "gl_account",
        ]
        labels = {
            "car_number": "CAR Number",
            "opex_expenditure": "OpEx Expenditure",
            "gl_account": "GL Account",
        }
        widgets = {
            "capital_expenditure": CheckboxSelect(attrs={"class": "SelectButtons"}),
            "opex_expenditure": CheckboxSelect(attrs={"class": "SelectButtons"}),
        }

    def __init__(self, *args, **kwargs):
        self.instance: Project
        super().__init__(*args, **kwargs)
        # Field Choices and Defaults
        self.fields["capital_expenditure"].choices = ((False, "No"), (True, "Yes"))
        self.fields["opex_expenditure"].choices = ((False, "No"), (True, "Yes"))

        if self.instance.capital_actuals:
            self.fields["capital_expenditure"].initial = True

        if self.instance.opex_actuals:
            self.fields["opex_expenditure"].inital = True


class ProjectStatusForm(forms.ModelForm):
    PROJECT_STATE_CHOICES = (
        (Project.PROJECT_STATE_ACTIVE, "Active"),
        (Project.PROJECT_STATE_COMPLETE, "Complete"),
        (Project.PROJECT_STATE_CANCELLED, "Cancelled"),
        (Project.PROJECT_STATE_ON_HOLD, "On Hold"),
    )
    PHASE_CHOICES = (
        (Project.PHASE_INITIATION, "Initiation"),
        (Project.PHASE_PLANNING, "Planning"),
        (Project.PHASE_EXECUTION, "Execution"),
        (Project.PHASE_CONTROL, "Close"),
    )

    budget_health = forms.ChoiceField(
        choices=ProjectHealth.HEALTH.choices,
        required=False,
        widget=forms.RadioSelect(attrs={"class": "SelectButtons"}),
    )
    schedule_health = forms.ChoiceField(
        choices=ProjectHealth.HEALTH.choices,
        required=False,
        widget=forms.RadioSelect(attrs={"class": "SelectButtons"}),
    )
    scope_health = forms.ChoiceField(
        choices=ProjectHealth.HEALTH.choices,
        required=False,
        widget=forms.RadioSelect(attrs={"class": "SelectButtons"}),
    )
    health = forms.ChoiceField(
        choices=ProjectHealth.HEALTH.choices,
        label="Overall Health",
        required=False,
        widget=forms.RadioSelect(attrs={"class": "SelectButtons"}),
    )
    modify_overall_health = forms.BooleanField(
        required=False, widget=forms.CheckboxInput(attrs={"switch": True})
    )
    percent_complete = forms.IntegerField(
        label="% Complete",
        min_value=0,
        max_value=100,
        initial=0,
        required=False,
        widget=forms.NumberInput(),
    )
    phase = forms.ChoiceField(choices=PHASE_CHOICES, widget=forms.Select())

    class Meta:
        model = Project
        fields = ["get_to_green", "phase", "priority", "project_state"]
        labels = {"get_to_green": "Get to Green"}
        widgets = {"priority": CheckboxSelect(attrs={"class": "SelectButtons"})}

    def __init__(self, user: User, *args, **kwargs):
        self.instance: Project
        self.user = user
        super().__init__(*args, **kwargs)

        self.fields["project_state"].required = False
        self.fields["project_state"].choices = self.PROJECT_STATE_CHOICES
        self.fields["phase"].required = False
        # self.fields["phase"].choices = Project.PHASE_CHOICES
        self.fields["priority"].choices = Project.PRIORITY_CHOICES

        if self.instance.pk:
            if self.instance.current_health:
                self.fields[
                    "budget_health"
                ].initial = self.instance.current_health.budget_health
                self.fields[
                    "schedule_health"
                ].initial = self.instance.current_health.schedule_health
                self.fields[
                    "scope_health"
                ].initial = self.instance.current_health.scope_health
                self.fields["health"].initial = self.instance.current_health.health
                self.fields[
                    "modify_overall_health"
                ].initial = not self.instance.current_health.calculate_overall_health
            if self.instance.percent_complete:
                self.fields["percent_complete"].initial = (
                    self.instance.percent_complete.percentage
                    if self.instance.percent_complete
                    else 0
                )

    def clean(self):
        modify_overall_health = self.cleaned_data.get("modify_overall_health", False)
        health_values = (
            [self.cleaned_data.get("health")]
            if modify_overall_health
            else [
                self.cleaned_data.get("budget_health"),
                self.cleaned_data.get("scope_health"),
                self.cleaned_data.get("schedule_health"),
            ]
        )
        get_to_green = self.cleaned_data.get("get_to_green")

        if get_to_green and (
            "yellow" not in health_values and "red" not in health_values
        ):
            self.cleaned_data["get_to_green"] = ""

        if not get_to_green and ("yellow" in health_values or "red" in health_values):
            self.add_error(
                "get_to_green", "Please explain what you will do to get to green"
            )

    def save(self, *args, **kwargs):
        with reversion.create_revision():
            instance: Project = super().save(*args, **kwargs)

            if not kwargs.get("commit", True):
                return instance

            budget_health: str = self.cleaned_data.get(
                "budget_health", ProjectHealth.HEALTH.GREEN
            )
            schedule_health: str = self.cleaned_data.get(
                "schedule_health", ProjectHealth.HEALTH.GREEN
            )
            scope_health: str = self.cleaned_data.get(
                "scope_health", ProjectHealth.HEALTH.GREEN
            )
            health: str = self.cleaned_data.get("health")
            modify_overall_health = self.cleaned_data.get(
                "modify_overall_health", False
            )
            percent_complete: int = self.cleaned_data.get("percent_complete", 0)
            if not percent_complete:
                percent_complete = 0

            week = timezone.now().isocalendar()[1]
            year = timezone.now().year

            try:
                project_health = ProjectHealth.objects.get(
                    project=instance, year=year, week=week
                )
            except ProjectHealth.DoesNotExist:
                project_health = ProjectHealth(project=instance, year=year, week=week)
            project_health.budget_health = budget_health
            project_health.schedule_health = schedule_health
            project_health.scope_health = scope_health
            if health is not None:
                project_health.health = health
                project_health.calculate_overall_health = not modify_overall_health
            project_health.save()
            try:
                project_percent_complete = ProjectPercentComplete.objects.get(
                    project=instance, year=year, week=week
                )
            except ProjectPercentComplete.DoesNotExist:
                project_percent_complete = ProjectPercentComplete(
                    project=instance, year=year, week=week
                )
            project_percent_complete.percentage = percent_complete
            project_percent_complete.save()

        return instance


class ProjectSavingsSectionForm(forms.ModelForm):
    annual_savings_target = USCurrencyField(
        initial=0, max_value=100_000_000, min_value=0
    )

    class Meta:
        model = Project
        fields = ["annual_savings_target"]


class ProjectCapitalExpenditureForm(forms.ModelForm):
    forecast = USCurrencyField(initial=0, max_value=100_000_000, min_value=0)
    actuals = USCurrencyField(initial=0, max_value=100_000_000, min_value=0)

    class Meta:
        model = ProjectCapitalExpenditure
        fields = ["id", "forecast", "actuals"]


class ProjectOperationalExpenditureForm(forms.ModelForm):
    forecast = USCurrencyField(initial=0, max_value=100_000_000, min_value=0)
    actuals = USCurrencyField(initial=0, max_value=100_000_000, min_value=0)

    class Meta:
        model = ProjectOperationalExpenditure
        fields = ["id", "forecast", "actuals"]


class ProjectSavingsForm(forms.ModelForm):
    savings = USCurrencyField(initial=0, max_value=100_000_000, min_value=-100_000_000)

    class Meta:
        model = ProjectSavings
        fields = ["id", "savings"]


class ProjectLinkForm(forms.ModelForm):
    class Meta:
        model = ProjectLink
        fields = ["id", "name", "url"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["name"].required = False
        self.fields["url"].required = False


class ProjectDivisionImpactForm(forms.ModelForm):
    class Meta:
        model = ProjectDivisionImpact
        fields = [
            "id",
            "division",
            "impact_none",
            "impact_increase_revenue",
            "impact_decrease_operational_cost",
            "impact_increase_productivity",
            "impact_improve_quality",
            "impact_mitigate_risk",
        ]
        labels = {
            "impact_none": "No Impact",
            "impact_increase_revenue": "Increase Revenue",
            "impact_decrease_operational_cost": "Decrease Operational Cost",
            "impact_increase_productivity": "Increase Productivity",
            "impact_improve_quality": "Improve Quality",
            "impact_mitigate_risk": "Mitigate Risk",
        }
        widgets = {"division": forms.HiddenInput()}

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field in self.fields.values():
            field.widget.attrs["title"] = field.label


class ProjectSupportImpactForm(forms.ModelForm):
    class Meta:
        model = ProjectSupportImpact
        fields = ["id", "impact"]
        widgets = {"impact": forms.RadioSelect()}

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["impact"].choices = ProjectSupportImpact.IMPACT_CHOICES


class ProjectFilterForm(forms.Form):
    from . import filters

    HEALTH_CHOICES = (
        (ProjectHealth.HEALTH.GREEN, "Green"),
        (ProjectHealth.HEALTH.YELLOW, "Yellow"),
        (ProjectHealth.HEALTH.RED, "Red"),
    )
    PHASE_CHOICES = filters.FILTER_PHASE_CHOICES
    STATE_CHOICES = filters.FILTER_PROJECT_STATE_CHOICES
    CREATED_WITHIN_CHOICES = (
        ("", "Any Time"),
        (1, "Last Day"),
        (7, "Last Week"),
        (30, "Last Month"),
        (365, "Last Year"),
    )

    starred = forms.BooleanField(required=False, widget=forms.HiddenInput())
    health = forms.MultipleChoiceField(
        choices=HEALTH_CHOICES, required=False, widget=forms.CheckboxSelectMultiple()
    )
    phase = forms.MultipleChoiceField(
        choices=PHASE_CHOICES, required=False, widget=forms.CheckboxSelectMultiple()
    )
    state = forms.MultipleChoiceField(
        choices=STATE_CHOICES, required=False, widget=forms.CheckboxSelectMultiple()
    )

    project_type = forms.MultipleChoiceField(
        choices=[],  # Initialized empty, set in __init__
        required=False,
        widget=forms.CheckboxSelectMultiple(),
    )

    person = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name"),
        empty_label="Show All",
        required=False,
    )

    role = forms.ModelMultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        queryset=Role.objects.none(),
        required=False,
    )

    program = forms.ModelChoiceField(
        queryset=Program.objects.filter(active=True).order_by("name"),
        empty_label="Show All",
        label="Program",
        required=False,
    )

    department_is_primary = forms.BooleanField(
        label='Include "Other Segments" in results', required=False
    )

    department = forms.MultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(),
        choices=[],  # Initialized empty, set in __init__
        label="Segment",
        required=False,
    )

    strategic_subpillars = forms.ModelMultipleChoiceField(
        queryset=StrategicPillar.objects.none(),  # Start with an empty queryset
        label="Strategic Pillar",
        required=False,
    )

    created_within = forms.TypedChoiceField(
        label="Date Range",
        choices=CREATED_WITHIN_CHOICES,
        coerce=int,
        required=False,
        widget=forms.RadioSelect(),
    )
    search = forms.CharField(max_length=200, widget=forms.HiddenInput())
    order_by = forms.CharField(widget=forms.HiddenInput())

    def __init__(self, *args, **kwargs):
        user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)

        self.fields["program"].queryset = self.fields["program"].queryset
        self.fields["department"].widget.attrs.update({"clearable": True})
        self.fields["state"].widget.attrs.update({"clearable": True})
        self.fields["project_type"].widget.attrs.update({"clearable": True})
        self.fields["phase"].widget.attrs.update({"clearable": True})
        self.fields["health"].widget.attrs.update({"clearable": True})
        self.fields["department_is_primary"].widget.attrs.update({"switch": True})

        self.fields["department"].choices = self.get_department_choices()
        self.fields["strategic_subpillars"].queryset = StrategicPillar.objects.all()
        self.fields["project_type"].choices = self.get_project_type_choices()

        self.fields["role"].queryset = Role.objects.filter(company=user.company)

    def get_department_choices(self):
        """
        Dynamically set department choices based on the Division model.
        """
        try:
            return [
                (division.pk, division.abbreviation)
                for division in Division.objects.all().order_by("name")
            ]
        except Exception:
            return []

    def get_project_type_choices(self):
        """
        Dynamically set project type choices based on the ProjectType model.
        """
        try:
            return [
                (project_type.id, project_type.name)
                for project_type in ProjectType.objects.order_by("name")
            ]
        except Exception:
            return []


class PersonForm(forms.ModelForm):
    divisions = forms.ModelMultipleChoiceField(
        queryset=Division.objects.none(),  # Start with an empty queryset
        widget=forms.CheckboxSelectMultiple(),
        required=False,
    )

    class Meta:
        model = User
        fields = ["first_name", "last_name", "email"]

    def __init__(self, user, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["divisions"].queryset = Division.objects.all()


class ProjectTrackerPersonForm(forms.ModelForm):
    """
    Form for managing a user's profile, roles, and divisions within their company.

    - Replaces legacy boolean role flags with a dynamic Role system.
    - Filters roles by the company of the current user.
    - Maintains existing division assignment logic for BRMs.
    """

    roles: forms.ModelMultipleChoiceField = forms.ModelMultipleChoiceField(
        queryset=Role.objects.none(),
        widget=forms.CheckboxSelectMultiple(),
        required=False,
        label="Roles",
        help_text="Select one or more roles for this user.",
    )

    divisions: forms.ModelMultipleChoiceField = forms.ModelMultipleChoiceField(
        queryset=Division.objects.none(),
        widget=forms.CheckboxSelectMultiple(),
        required=False,
        label="Divisions (for BRMs)",
    )

    class Meta:
        model = User
        fields = ["first_name", "last_name", "email", "roles", "divisions"]

    def __init__(self, user: User, *args: Any, **kwargs: Any) -> None:
        """
        Initialize the form and filter role/division querysets by the current user's company.
        """
        super().__init__(*args, **kwargs)
        self.request_user: User = user

        # ✅ Limit roles to the same company
        self.fields["roles"].queryset = Role.objects.filter(company=user.company)

        # ✅ Limit divisions to company divisions
        self.fields["divisions"].queryset = Division.objects.all()

        # ✅ Prepopulate selected roles
        existing_roles = RoleAssignment.objects.filter(
            user=self.instance,
            role__company=user.company,
        ).values_list("role_id", flat=True)
        self.fields["roles"].initial = list(existing_roles)

    @transaction.atomic
    def save(self, commit: bool = True) -> User:
        """
        Save the user instance, assign roles dynamically via RoleAssignment,
        and update division BRM relationships.
        """
        instance: User = super().save(commit=commit)

        # --------------------------
        # ✅ Update Role Assignments
        # --------------------------
        RoleAssignment.objects.filter(user=instance).delete()

        selected_roles = self.cleaned_data.get("roles", [])

        for role in selected_roles:
            RoleAssignment.objects.create(
                user=instance,
                role=role,
            )
        return instance


class FinancialPlannerPersonForm(forms.ModelForm):
    """
    Form for managing a user's financial planning roles and segment access.

    - Replaces legacy boolean role flags with a dynamic Role system.
    - Filters roles to the current user's company.
    - Maintains segment visibility logic for 'Senior Leadership' or related roles.
    """

    roles: forms.ModelMultipleChoiceField = forms.ModelMultipleChoiceField(
        queryset=Role.objects.none(),
        widget=forms.CheckboxSelectMultiple(),
        required=False,
        label="Roles",
        help_text="Select one or more roles for this user.",
    )

    business_segments: forms.ModelMultipleChoiceField = forms.ModelMultipleChoiceField(
        label="Business Segments (for Senior Leadership)",
        queryset=BusinessSegment.objects.none(),
        widget=forms.CheckboxSelectMultiple(),
        required=False,
    )

    class Meta:
        model = User
        fields = ["first_name", "last_name", "email", "roles", "business_segments"]

    def __init__(self, user: User, *args: Any, **kwargs: Any) -> None:
        """
        Initialize the form with roles and segments limited to the current user's company.
        """
        super().__init__(*args, **kwargs)
        self.request_user: User = user

        # ✅ Limit roles to company-specific roles
        self.fields["roles"].queryset = Role.objects.filter(company=user.company)

        # ✅ Limit segments to company-related ones
        self.fields["business_segments"].queryset = BusinessSegment.objects.filter(
            company=user.company
        )

        # ✅ Prepopulate selected roles
        existing_roles = RoleAssignment.objects.filter(
            user=self.instance, role__company=user.company
        ).values_list("role_id", flat=True)
        self.fields["roles"].initial = list(existing_roles)

        # ✅ Prepopulate segments
        self.fields["business_segments"].initial = list(
            BusinessSegmentPerson.objects.filter(user=self.instance).values_list(
                "business_segment_id", flat=True
            )
        )

    @transaction.atomic
    def save(self, commit: bool = True) -> User:
        """
        Save the user, update their role assignments, and sync business segments.
        """
        instance: User = super().save(commit=commit)

        # --------------------------
        # ✅ Update Role Assignments
        # --------------------------
        RoleAssignment.objects.filter(user=instance).delete()

        selected_roles = self.cleaned_data.get("roles", [])

        for role in selected_roles:
            RoleAssignment.objects.create(
                user=instance,
                role=role,
            )

        return instance


class IdeaTrackerPersonForm(ProjectTrackerPersonForm):
    roles: forms.ModelMultipleChoiceField = forms.ModelMultipleChoiceField(
        queryset=Role.objects.none(),
        widget=forms.CheckboxSelectMultiple(),
        required=False,
        label="Roles",
        help_text="Select one or more roles for this user.",
    )

    class Meta:
        model = User
        fields = [
            "first_name",
            "last_name",
            "email",
            "roles",
        ]

    def __init__(self, user, *args, **kwargs):
        super(forms.ModelForm, self).__init__(*args, **kwargs)
        self.fields["roles"].queryset = Role.objects.filter(company=user.company)

        existing_roles = RoleAssignment.objects.filter(
            user=self.instance,
            role__company=user.company,
        ).values_list("role_id", flat=True)
        self.fields["roles"].initial = list(existing_roles)


class PersonFilterForm(forms.Form):
    person_search = forms.CharField(
        max_length=200,
        widget=forms.TextInput(
            attrs={
                "class": "Person-filterForm-search",
                "placeholder": "Search People...",
            }
        ),
        required=False,
    )

    person = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name"),
        empty_label="Show All",
        required=False,
    )

    role = forms.ModelMultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        queryset=Role.objects.none(),
        required=False,
    )

    status = forms.MultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(),
        choices=[("active", "Active"), ("inactive", "Inactive")],
        required=False,
    )

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        """
        Initialize the form and filter role/division querysets by the current user's company.
        """
        user = kwargs.pop("user", None)

        super().__init__(*args, **kwargs)

        if not user or not hasattr(user, "company"):
            raise ImproperlyConfigured(
                "A valid user with a company is required to initialize PersonFilterForm."
            )

        # ✅ Limit roles to the same company
        self.fields["role"].queryset = Role.objects.filter(company=user.company)


class RaidRisksForm(forms.ModelForm):
    class Meta:
        model = RaidRisk
        fields = [
            "section",
            "date_identified",
            "identifier",
            "risk_description",
            "likelihood",
            "impact",
            "status",
            "mitigation",
            "risk_owner",
            "impact_date",
        ]
        labels = {
            "section": "Area",
            "date_identified": "Date Identified",
            "identifier": "Identified By",
            "risk_description": "Risk Description",
            "likelihood": "Likelihood of Occurrence",
            "impact": "Impact",
            "impact_date": "Impact Date",
            "risk_owner": "Owner",
            "status": "Status",
            "mitigation": "Mitigation Strategy",
        }

        widgets = {
            "date_identified": forms.DateInput(attrs={"type": "date"}),
            "impact_date": forms.DateInput(attrs={"type": "date"}),
            "likelihood": forms.RadioSelect(attrs={"class": "SelectButtons"}),
            "status": forms.RadioSelect(attrs={"class": "SelectButtons"}),
            "impact": forms.RadioSelect(attrs={"class": "SelectButtons"}),
        }

    def __init__(self, user, *args, **kwargs):
        super().__init__(*args, **kwargs)
        person = User.objects.filter(email=user).first()
        self.fields["identifier"].initial = person
        if not self.instance.pk:
            del self.fields["status"]


class RaidAssumptionForm(forms.ModelForm):
    class Meta:
        model = RaidAssumption
        fields = [
            "section",
            "date_identified",
            "identifier",
            "assumption",
            "reason",
            "validation_action",
            "impact_if_incorrect",
            "validated_by",
            "owner",
            "close_by",
            "priority",
            "status",
        ]

        labels = {
            "section": "Area",
            "date_identified": "Date Identified",
            "identifier": "Identified By",
            "assumption": "Assumption Description",
            "reason": "Reason",
            "validation_action": "Action to Validate",
            "impact_if_incorrect": "Impact if Incorrect",
            "validated_by": "Validated By",
            "priority": "Impact",
            "close_by": "Close By",
            "owner": "Owner",
            "status": "Status",
        }

        widgets = {
            "date_identified": forms.DateInput(attrs={"type": "date"}),
            "close_by": forms.DateInput(attrs={"type": "date"}),
            "priority": forms.RadioSelect(attrs={"class": "SelectButtons"}),
            "status": forms.RadioSelect(attrs={"class": "SelectButtons"}),
        }

    def __init__(self, user, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["validated_by"].required = False
        self.fields["reason"].required = False
        self.fields["impact_if_incorrect"].required = False
        person = User.objects.filter(email=user).first()
        self.fields["identifier"].initial = person
        if not self.instance.pk:
            del self.fields["status"]


class RaidIssuesForm(forms.ModelForm):
    class Meta:
        model = RaidIssues
        fields = [
            "section",
            "date_identified",
            "identifier",
            "issue_description",
            "corrective_action",
            "priority",
            "close_by",
            "issue_owner",
            "status",
        ]

        labels = {
            "section": "Area",
            "date_identified": "Date Identified",
            "identifier": "Identified By",
            "issue_description": "Issue Description",
            "issue_owner": "Owner",
            "close_by": "Close By",
            "corrective_action": "Corrective Action",
            "priority": "Rating",
            "status": "Status",
        }

        widgets = {
            "date_identified": forms.DateInput(attrs={"type": "date"}),
            "close_by": forms.DateInput(attrs={"type": "date"}),
            "priority": forms.RadioSelect(attrs={"class": "SelectButtons"}),
            "status": forms.RadioSelect(attrs={"class": "SelectButtons"}),
        }

    def __init__(self, user, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["corrective_action"].required = False
        self.fields["close_by"].required = False
        person = User.objects.filter(email=user).first()
        self.fields["identifier"].initial = person
        if not self.instance.pk:
            del self.fields["status"]


class RaidDependenciesForm(forms.ModelForm):
    class Meta:
        model = RaidDependencies
        fields = [
            "section",
            "date_identified",
            "identifier",
            "dependency",
            "impact",
            "dependency_type",
            "priority",
            "dependency_owner",
            "close_by",
            "status",
        ]

        labels = {
            "section": "Area",
            "date_identified": "Date Identified",
            "identifier": "Identified By",
            "dependency_type": "Type",
            "dependency": "Dependency Description",
            "impact": "Dependency Effect",
            "dependency_owner": "Owner",
            "close_by": "Close By",
            "priority": "Impact",
            "status": "Status",
        }

        widgets = {
            "date_identified": forms.DateInput(attrs={"type": "date"}),
            "close_by": forms.DateInput(attrs={"type": "date"}),
            "dependency_type": forms.RadioSelect(attrs={"class": "SelectButtons"}),
            "priority": forms.RadioSelect(attrs={"class": "SelectButtons"}),
            "status": forms.RadioSelect(attrs={"class": "SelectButtons"}),
        }

    def __init__(self, user, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["impact"].required = False
        self.fields["close_by"].required = False
        person = User.objects.filter(email=user).first()
        self.fields["identifier"].initial = person
        if not self.instance.pk:
            del self.fields["status"]
