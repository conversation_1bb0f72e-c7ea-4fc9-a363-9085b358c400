import json
from typing import Any, Dict, List, cast

from dateutil.relativedelta import relativedelta
from django.contrib import messages
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.db.models import Q, QuerySet
from django.forms.models import ModelForm
from django.http import (
    HttpResponseNotAllowed,
    HttpResponseRedirect,
    JsonResponse,
)
from django.http.request import HttpRequest
from django.http.response import HttpResponse
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.text import slugify
from django.views.generic import (
    CreateView,
    DeleteView,
    DetailView,
    ListView,
    UpdateView,
)
from django.views.generic.base import View
from django.views.generic.edit import BaseFormView, ModelFormMixin
from extra_views import (
    CreateWithInlinesView,
    InlineFormSetFactory,
    NamedFormsetsMixin,
    UpdateWithInlinesView,
)
from openpyxl import Workbook
from rest_framework.authentication import SessionAuthentication
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from apps.actions.models import ActionType, record
from apps.attachments.mixins import AttachmentJSONMixin
from apps.attachments.models import Attachment
from apps.attachments.serializers import AttachmentSerializer
from apps.comments.forms import CommentForm
from apps.comments.models import Comment
from apps.documents.models import Rigor
from apps.links.mixins import LinkJSONMixin
from apps.links.models import Link
from apps.links.serializers import LinkSerializer
from apps.meetings.models import Meeting, MeetingItem
from apps.notifications.models import MessageTypes, notify
from apps.projects.models import InScope, MeasureOfSuccess, OutOfScope
from apps.projects.models.raid import (
    RaidAssumption,
    RaidDependencies,
    RaidIssues,
    RaidReport,
    RaidRisk,
)
from apps.utils.calendar import month_year_iter
from apps.utils.forms import collect_form_errors
from apps.utils.mixins import (
    CompanyAdminRequiredMixin,
    FormWizardMixin,
    LoginRequiredMixin,
    ProjectDeactivatedMixin,
    SuccessUrlNextMixin,
)
from apps.organizations.mixins import RolePermissionMixin

from apps.organizations.mixins import CompanyQuerysetMixin, RolePermissionMixin
from apps.utils.mixins import CompanyCompanyAdminRequiredMixin
from apps.users.models import User

from ..utils.views import get_site
from .filters import PersonFilter, ProjectFilter
from .forms import (
    FinancialPlannerPersonForm,
    IdeaTrackerPersonForm,
    PersonFilterForm,
    PersonForm,
    ProjectCapitalExpenditureForm,
    ProjectExecutiveActionForm,
    ProjectFinancialsSectionForm,
    ProjectForm,
    ProjectInScopeForm,
    ProjectMeasureOfSuccessForm,
    ProjectOperationalExpenditureForm,
    ProjectOutOfScopeForm,
    ProjectPlannedActivityForm,
    ProjectRecentAccomplishmentForm,
    ProjectSavingsForm,
    ProjectSavingsSectionForm,
    ProjectStatusForm,
    ProjectTrackerPersonForm,
    RaidAssumptionForm,
    RaidDependenciesForm,
    RaidIssuesForm,
    RaidRisksForm,
)
from .models import (
    Division,
    Project,
    ProjectCapitalExpenditure,
    ProjectExecutiveAction,
    ProjectOperationalExpenditure,
    ProjectPlannedActivity,
    ProjectRecentAccomplishment,
    ProjectSavings,
    StarredProject,
)
from .serializers import (
    ProjectChangesSerializer,
    ProjectExportCSVRenderer,
    ProjectExportSerializer,
)
from .utils import generate_raid_excel_sheet, get_project_rigor_completed_step_list
from .view_models import ProjectRigorStepEntry, RigorProject


class ProjectRecentAccomplishmentCreateInline(InlineFormSetFactory):
    model = ProjectRecentAccomplishment
    form_class = ProjectRecentAccomplishmentForm
    factory_kwargs = {"can_delete": True, "extra": 1}


class ProjectRecentAccomplishmentUpdateInline(InlineFormSetFactory):
    model = ProjectRecentAccomplishment
    form_class = ProjectRecentAccomplishmentForm
    factory_kwargs = {"can_delete": True, "extra": 0}


class ProjectPlannedActivityCreateInline(InlineFormSetFactory):
    model = ProjectPlannedActivity
    form_class = ProjectPlannedActivityForm
    factory_kwargs = {"can_delete": True, "extra": 1}


class ProjectPlannedActivityUpdateInline(InlineFormSetFactory):
    model = ProjectPlannedActivity
    form_class = ProjectPlannedActivityForm
    factory_kwargs = {"can_delete": True, "extra": 0}


class ProjectExecutiveActionCreateInline(InlineFormSetFactory):
    model = ProjectExecutiveAction
    form_class = ProjectExecutiveActionForm
    factory_kwargs = {"can_delete": True, "extra": 1}


class ProjectExecutiveActionUpdateInline(InlineFormSetFactory):
    model = ProjectExecutiveAction
    form_class = ProjectExecutiveActionForm
    factory_kwargs = {"can_delete": True, "extra": 0}


class ProjectInScopeCreateInline(InlineFormSetFactory):
    model = InScope
    form_class = ProjectInScopeForm
    factory_kwargs = {"can_delete": True, "extra": 1}


class ProjectInScopeUpdateInline(InlineFormSetFactory):
    model = InScope
    form_class = ProjectInScopeForm
    factory_kwargs = {"can_delete": True, "extra": 0}


class ProjectOutOfScopeUpdateInline(InlineFormSetFactory):
    model = OutOfScope
    form_class = ProjectOutOfScopeForm
    factory_kwargs = {"can_delete": True, "extra": 0}


class ProjectOutOfScopeCreateInline(InlineFormSetFactory):
    model = OutOfScope
    form_class = ProjectOutOfScopeForm
    factory_kwargs = {"can_delete": True, "extra": 1}


class ProjectMeasureOfSuccessUpdateInline(InlineFormSetFactory):
    model = MeasureOfSuccess
    form_class = ProjectMeasureOfSuccessForm
    factory_kwargs = {"can_delete": True, "extra": 0}


class ProjectMeasureOfSuccessCreateInline(InlineFormSetFactory):
    model = MeasureOfSuccess
    form_class = ProjectMeasureOfSuccessForm
    factory_kwargs = {"can_delete": True, "extra": 1}


class ProjectCapitalExpenditureInline(InlineFormSetFactory):
    model = ProjectCapitalExpenditure
    form_class = ProjectCapitalExpenditureForm
    factory_kwargs = {"can_delete": False}

    def get_factory_kwargs(self) -> dict:
        project: Project = self.object
        kwargs = super().get_factory_kwargs()
        num_forms = project.project_length
        kwargs.update({"max_num": num_forms, "extra": num_forms})
        return kwargs

    def construct_formset(self):
        project: Project = self.object
        start_date = project.start_date
        end_date = project.end_date
        formset = super().construct_formset()
        for (year, month), form in zip(
            month_year_iter(
                start_date.month, start_date.year, end_date.month, end_date.year
            ),
            formset.forms,
        ):
            if not form.instance.pk:
                form.instance.year = year
                form.instance.month = month
        return formset


class ProjectOperationalExpenditureInline(InlineFormSetFactory):
    model = ProjectOperationalExpenditure
    form_class = ProjectOperationalExpenditureForm
    factory_kwargs = {"can_delete": False}

    def get_factory_kwargs(self) -> dict:
        project: Project = self.object
        kwargs = super().get_factory_kwargs()
        num_forms = project.project_length
        kwargs.update({"max_num": num_forms, "extra": num_forms})
        return kwargs

    def construct_formset(self):
        project: Project = self.object
        start_date = project.start_date
        end_date = project.end_date
        formset = super().construct_formset()
        for (year, month), form in zip(
            month_year_iter(
                start_date.month, start_date.year, end_date.month, end_date.year
            ),
            formset.forms,
        ):
            if not form.instance.pk:
                form.instance.year = year
                form.instance.month = month
        return formset


class ProjectSavingsInline(InlineFormSetFactory):
    model = ProjectSavings
    form_class = ProjectSavingsForm
    factory_kwargs = {"can_delete": False}

    def get_factory_kwargs(self) -> dict:
        project: Project = self.object
        kwargs = super().get_factory_kwargs()
        num_forms = project.savings_monitoring_length or 12
        kwargs.update({"max_num": num_forms, "extra": num_forms})
        return kwargs

    def construct_formset(self):
        project: Project = self.object
        start_date = project.savings_start_date or timezone.now()
        end_date = project.savings_end_date or start_date + relativedelta(years=1)
        formset = super().construct_formset()
        for (year, month), form in zip(
            month_year_iter(
                start_date.month, start_date.year, end_date.month, end_date.year
            ),
            formset.forms,
        ):
            if not form.instance.pk:
                form.instance.year = year
                form.instance.month = month
        return formset


class ProjectListView(LoginRequiredMixin, RolePermissionMixin, ListView):
    model = Project
    required_permission = "read"
    queryset = (
        Project.objects.filtered_for_project()
        .with_latest_health()
        .with_latest_health_value()
        .with_latest_percentage()
        .with_strategic_value()
    )
    paginate_by = 10
    template_name = "projects/project_list.html"

    def get_context_data(self, *, object_list=None, **kwargs):
        context = super().get_context_data()
        context["project_pks"] = [project.pk for project in self.object_list]
        return context

    def get_queryset(self) -> QuerySet:
        user = self.request.user
        queryset = super().get_queryset()
        queryset = queryset.with_starred_by_user(user)
        queryset = ProjectFilter(self.request.GET, queryset=queryset).qs.distinct()
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)
        return queryset

    def get(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        query: str = request.GET.get("project_search", "")
        if query.isdigit() and Project.objects.filter(pk=query).exists():
            return redirect("project_detail", pk=query)
        return super().get(request, *args, **kwargs)


class ProjectExportView(RolePermissionMixin, CompanyQuerysetMixin, ListAPIView):
    model = Project
    required_permission = "read"
    queryset = (
        Project.objects.select_related("primary_division")
        .filtered_for_project()
        .with_strategic_value()
        .with_latest_health()
        .with_latest_percentage()
        .with_expenditures()
        .distinct()
    )
    serializer_class = ProjectExportSerializer
    pagination_class = None
    renderer_classes = [ProjectExportCSVRenderer]
    permission_classes = [IsAuthenticated]

    def filter_queryset(self, queryset: QuerySet) -> QuerySet:
        user = self.request.user
        queryset = queryset.with_starred_by_user(user)
        queryset = ProjectFilter(
            self.request.GET, queryset=queryset, user=self.request.user
        ).qs.distinct()
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)
        return queryset

    def finalize_response(self, *args, **kwargs) -> Response:
        response = super().finalize_response(*args, **kwargs)
        response["Content-Disposition"] = "attachment; filename=projects.csv"
        return response


class ProjectDetailView(
    LoginRequiredMixin,
    RolePermissionMixin,
    ProjectDeactivatedMixin,
    AttachmentJSONMixin,
    LinkJSONMixin,
    DetailView,
):
    model = Project
    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    queryset = Project.objects.all().filtered_for_project()
    template_name = "projects/project_detail.html"

    def get_template_names(self) -> List[str]:
        self.object: Project
        if not self.object.active:
            return ["projects/project_detail_deactivated.html"]
        return super().get_template_names()

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        queryset = queryset.with_starred_by_user(self.request.user)
        return queryset

    def get_context_data(self, **kwargs) -> dict:
        self.object: Project
        context = super().get_context_data(**kwargs)
        context["content_type_id"] = ContentType.objects.get_for_model(Project).pk
        context["presentation_file"] = (
            slugify(self.object.name, allow_unicode=True) + "-summary.ppt"
        )
        # if self.object.program:
        # TODO: Make this a property

        # context["can_view_program"] = self.object.program.user_has_view_access(
        #     self.request.user
        # )

        context["strategic_pillars"] = self.object.strategic_pillars.all()
        try:
            context["epmo_segment"] = Division.objects.get(name="ePMO").id
        except Division.DoesNotExist:
            pass
        try:
            project_rigor = (
                "swe"
                if self.object.project_rigor == "small work effort"
                else self.object.project_rigor
            )
            context["document_rigor"] = Rigor.objects.get(slug__iexact=project_rigor).id
        except Rigor.DoesNotExist:
            pass

        qs = RaidReport.objects.select_related(
            "risk", "issue", "assumption", "dependency"
        ).filter(
            Q(risk__project__id=self.object.id, status="Active")
            | Q(assumption__project__id=self.object.id, status="Unvalidated")
            | Q(issue__project__id=self.object.id, status="Active")
            | Q(dependency__project__id=self.object.id, status="Active")
        )

        raid_count = {
            "risk_low_count": qs.filter(risk__risk_rating="Low").count(),
            "risk_medium_count": qs.filter(risk__risk_rating="Medium").count(),
            "risk_high_count": qs.filter(risk__risk_rating="High").count(),
            "assumption_low_count": qs.filter(assumption__priority="Low").count(),
            "assumption_medium_count": qs.filter(assumption__priority="Medium").count(),
            "assumption_high_count": qs.filter(assumption__priority="High").count(),
            "issue_low_count": qs.filter(issue__priority="Low").count(),
            "issue_medium_count": qs.filter(issue__priority="Medium").count(),
            "issue_high_count": qs.filter(issue__priority="High").count(),
            "dependency_low_count": qs.filter(dependency__priority="Low").count(),
            "dependency_medium_count": qs.filter(dependency__priority="Medium").count(),
            "dependency_high_count": qs.filter(dependency__priority="High").count(),
        }

        context["raid_count"] = raid_count

        return context


class ProjectExpendituresView(
    LoginRequiredMixin, RolePermissionMixin, ProjectDeactivatedMixin, DetailView
):
    model = Project
    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    queryset = Project.objects.filtered_for_project()
    template_name = "projects/project_detail_expenditures.html"

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        queryset = queryset.with_starred_by_user(self.request.user)
        return queryset


class ProjectBenefitsTrackingView(
    LoginRequiredMixin, RolePermissionMixin, ProjectDeactivatedMixin, DetailView
):
    model = Project
    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    queryset = Project.objects.filtered_for_project().with_savings_calculations()

    template_name = "projects/project_detail_benefits_tracking.html"

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        queryset = queryset.with_starred_by_user(self.request.user)
        return queryset


class ProjectTollgateDetailView(
    LoginRequiredMixin, RolePermissionMixin, ProjectDeactivatedMixin, DetailView
):
    model = Project
    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    queryset = Project.objects.filtered_for_project()

    template_name = "projects/project_detail_tollgate.html"

    @cached_property
    def project(self) -> Project:
        return self.get_object()

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        queryset = queryset.with_starred_by_user(self.request.user)
        return queryset

    def get_context_data(self, **kwargs) -> dict:
        data = super().get_context_data(**kwargs)
        current_meeting = Meeting.objects.get_current()
        data["meeting"] = current_meeting
        try:
            meeting_item = self.project.meetingitem_set.get(
                Q(meeting=current_meeting) | Q(meeting__isnull=True)
            )
        except MeetingItem.DoesNotExist:
            meeting_item = None
        data["meeting_item"] = meeting_item
        data["meeting_history"] = (
            self.project.meetingitem_set.with_results()
            .filter(Q(meeting__isnull=False) | Q(vote_result__isnull=False))
            .order_by("-meeting__meeting_date")
        )
        data["in_queue"] = (
            meeting_item and meeting_item.state == MeetingItem.ItemState.QUEUED
        )
        data["on_agenda"] = (
            meeting_item and meeting_item.state == MeetingItem.ItemState.AGENDA
        )
        data["documents"] = meeting_item and [
            {
                "id": d.id,
                "url": d.document_url,
                "name": d.document_name,
                "application_type": d.application_type,
            }
            for d in meeting_item.documents.all()
        ]
        return data


class ProjectTollgateQueueFormView(
    LoginRequiredMixin, RolePermissionMixin, ProjectDeactivatedMixin, DetailView
):
    model = Project
    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    queryset = Project.objects.filtered_for_project()
    template_name = "projects/project_tollgate_form.html"

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        queryset = queryset.with_starred_by_user(self.request.user)
        return queryset

    def get_context_data(self, **kwargs) -> dict:
        data = super().get_context_data(**kwargs)
        data.update(
            {
                "page_title": "Add Project to Tollgate Queue",
                "submit_label": "Add To Queue",
                "success_message": "This project has been added to the Tollgate Queue. The ePMO manager has not yet been notified.",
                "form_url": reverse("meeting_add_queue"),
                "form_errors": getattr(self, "form_errors", []),
                "content_type_id": ContentType.objects.get_for_model(Project).pk,
                "attachments": [
                    {
                        "type": "attachment",
                        "id": a.id,
                        "file_name": a.file_name,
                        "url": a.file.url,
                        "application_type": a.application_type,
                        "created": a.created,
                        "tags": (
                            [t.name for t in a.tags.all()] if a.tags.count() else []
                        ),
                    }
                    for a in self.object.attachments.all()
                ],
                "links": [
                    {
                        "type": "link",
                        "id": link.id,
                        "file_name": link.name,
                        "url": link.url,
                        "created": link.created,
                        "application_type": "Link",
                        "tags": [t.name for t in link.tags.all()],
                    }
                    for link in self.object.links.all()
                ],
            }
        )
        return data


class ProjectTollgateDocumentFormView(LoginRequiredMixin, DetailView):
    queryset = MeetingItem.objects.all()
    template_name = "projects/project_tollgate_form.html"
    context_object_name = "meeting_item"

    def get_context_data(self, **kwargs) -> dict:
        data = super().get_context_data(**kwargs)
        meeting_item = self.get_object()
        data["project"] = Project.objects.filter(
            id=meeting_item.project.pk
        ).with_starred_by_user(self.request.user)[0]
        existing_doc_objects = [d.content_object for d in meeting_item.documents.all()]
        data.update(
            {
                "page_title": "Select Documents for Tollgate Review",
                "submit_label": "Save",
                "success_message": "The selected documents will appear in the Tollgate Meeting folder when the project is in the Expected Agenda.",
                "form_url": reverse(
                    "meeting_item_add_documents", args=[meeting_item.id]
                ),
                "form_errors": getattr(self, "form_errors", []),
                "content_type_id": ContentType.objects.get_for_model(Project).pk,
                "attachments": [
                    {
                        "type": "attachment",
                        "id": a.id,
                        "file_name": a.file_name,
                        "url": a.file.url,
                        "application_type": a.application_type,
                        "created": a.created,
                        "tags": (
                            [t.name for t in a.tags.all()] if a.tags.count() else []
                        ),
                        "selected": a in existing_doc_objects,
                    }
                    for a in meeting_item.project.attachments.all()
                ],
                "links": [
                    {
                        "type": "link",
                        "id": link.id,
                        "file_name": link.name,
                        "url": link.url,
                        "application_type": "Link",
                        "created": link.created,
                        "tags": [t.name for t in link.tags.all()],
                        "selected": link in existing_doc_objects,
                    }
                    for link in meeting_item.project.links.all()
                ],
            }
        )
        return data


class ProjectCreateView(LoginRequiredMixin, NamedFormsetsMixin, CreateWithInlinesView):
    model = Project
    form_class = ProjectForm
    inlines = [
        ProjectRecentAccomplishmentCreateInline,
        ProjectPlannedActivityCreateInline,
        ProjectExecutiveActionCreateInline,
        ProjectInScopeCreateInline,
        ProjectOutOfScopeCreateInline,
        ProjectMeasureOfSuccessCreateInline,
    ]
    inlines_names = [
        "project_recent_accomplishments_formset",
        "project_planned_activities_formset",
        "project_executive_actions_formset",
        "project_in_scope_formset",
        "project_out_of_scope_formset",
        "project_measure_of_success_formset",
    ]

    template_name = "projects/project_form_create.html"

    def get_context_data(self, **kwargs) -> dict:
        context = super().get_context_data(**kwargs)
        whitelist_tags = list(
            Project.tags.most_common(min_count=3).values_list("name", flat=True)[:50]
        )

        context.update(
            {
                "form_errors": getattr(self, "form_errors", []),
                "tags_whitelist_json": json.dumps(whitelist_tags),
                "content_type_id": ContentType.objects.get_for_model(Project).pk,
            }
        )
        if self.request.POST.get("attachments"):
            attachment_ids = self.request.POST.getlist("attachments")
            context["attachments"] = AttachmentSerializer(
                Attachment.objects.filter(id__in=attachment_ids), many=True
            ).data
        if self.request.POST.get("links"):
            link_ids = self.request.POST.getlist("links")
            context["links"] = LinkSerializer(
                Link.objects.filter(id__in=link_ids), many=True
            ).data
        return context

    def get_form_kwargs(self) -> dict:
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def create_action_records(self):
        self.object: Project
        record(
            project=self.object,
            action_type=ActionType.CREATED_PROJECT,
            editor=self.request.user,
        )
        for person in self.object.executive_owners.all():
            text = f"{person.full_name} was assigned to Executive Owner"
            record(
                project=self.object,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=self.request.user,
                description=text,
            )
        for person in self.object.finance_leads.all():
            text = f"{person.full_name} was assigned to Finance Lead"
            record(
                project=self.object,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=self.request.user,
                description=text,
            )
        for person in self.object.business_leads.all():
            text = f"{person.full_name} was assigned to Business Lead"
            record(
                project=self.object,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=self.request.user,
                description=text,
            )
        for person in self.object.business_analysts.all():
            text = f"{person.full_name} was assigned to Business Analyst"
            record(
                project=self.object,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=self.request.user,
                description=text,
            )
        for person in self.object.other_stakeholders.all():
            text = f"{person.full_name} was assigned to Additional Stakeholder"
            record(
                project=self.object,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=self.request.user,
                description=text,
            )

        for person in self.object.project_managers.all():
            text = f"{person.full_name} was assigned to Project Manager"
            record(
                project=self.object,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=self.request.user,
                description=text,
            )
        for executive_action in self.object.executive_actions.all():
            text = f"Executive Action Needed: {executive_action.action_display} - {executive_action.text}"
            record(
                project=self.object,
                action_type=ActionType.ADDED_EXECUTIVE_ACTION,
                editor=self.request.user,
                description=text,
            )
        for recent_accomplishment in self.object.recent_accomplishments.all():
            text = f"Recent Accomplishment: {recent_accomplishment.text}"
            record(
                project=self.object,
                action_type=ActionType.ADDED_RECENT_ACCOMPLISHMENT,
                editor=self.request.user,
                description=text,
            )
        if self.object.has_technology_components:
            record(
                project=self.object,
                action_type=ActionType.CHANGED_TECHNICAL_COMPONENT,
                editor=self.request.user,
            )
        if self.object.corporate_communication_needs:
            record(
                project=self.object,
                action_type=ActionType.NEEDS_CORPORATE_COMMUNICATION,
                editor=self.request.user,
            )

    def create_notifications(self):
        self.object: Project
        message = "{user} added <a href='{url}'>{name}</a>.".format(
            user=self.request.user.full_name,
            url=reverse("project_detail", args=[self.object.pk]),
            name=self.object.name,
        )
        notify(
            message_type=MessageTypes.PROJECT_NEW,
            message=message,
            creator=self.request.user,
            content_object=self.object,
        )

        if self.object.has_technology_components:
            message = "{user} marked <a href='{url}'>{name}</a> as a project with Technical Components.".format(
                user=self.request.user.full_name,
                url=reverse("project_detail", args=[self.object.pk]),
                name=self.object.name,
            )
            notify(
                message_type=MessageTypes.PROJECT_TECH_COMPONENTS,
                message=message,
                creator=self.request.user,
                content_object=self.object,
            )

        if (
            self.object.corporate_communication_involvement
            and self.object.corporate_communication_needs
            != Project.COMMUNICATION_NEED_LOW
        ):
            message = f"{self.request.user.full_name} indicated need on <a href='{reverse('project_detail', args=[self.object.pk])}'>{self.object.name}</a>."

            notify(
                message_type=MessageTypes.CORPORATE_COMMUNICATION_NEEDED,
                message=message,
                creator=self.request.user,
                content_object=self.object,
            )

    def forms_valid(self, form: ModelForm, inlines) -> HttpResponse:
        self.object: Project
        response = super().forms_valid(form, inlines)
        # self.object.set_health()
        # self.object.set_financial_tables()
        # self.object.set_savings_table()
        self.create_action_records()
        self.create_notifications()
        messages.success(self.request, "Project has been created.")
        return response

    def forms_invalid(self, form: ModelForm, inlines) -> HttpResponse:
        self.form_errors = collect_form_errors(form, inlines)
        return super().forms_invalid(form, inlines)

    def get_success_url(self) -> str:
        return reverse("project_detail", args=[self.object.pk])


class ProjectUpdateView(
    LoginRequiredMixin,
    RolePermissionMixin,
    ProjectDeactivatedMixin,
    NamedFormsetsMixin,
    AttachmentJSONMixin,
    LinkJSONMixin,
    UpdateWithInlinesView,
):
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = Project
    queryset = Project.objects.filtered_for_project()
    form_class = ProjectForm
    inlines = [
        ProjectRecentAccomplishmentUpdateInline,
        ProjectPlannedActivityUpdateInline,
        ProjectExecutiveActionUpdateInline,
        ProjectInScopeUpdateInline,
        ProjectOutOfScopeUpdateInline,
        ProjectMeasureOfSuccessUpdateInline,
    ]
    inlines_names = [
        "project_recent_accomplishments_formset",
        "project_planned_activities_formset",
        "project_executive_actions_formset",
        "project_in_scope_formset",
        "project_out_of_scope_formset",
        "project_measure_of_success_formset",
    ]
    template_name = "projects/project_form_update.html"

    def post(self, request, *args, **kwargs):
        self.previous_data = ProjectChangesSerializer(self.get_object()).data
        return super().post(request, *args, **kwargs)

    def get_context_data(self, **kwargs) -> dict:
        context = super().get_context_data(**kwargs)
        whitelist_tags = list(
            Project.tags.most_common(min_count=3).values_list("name", flat=True)[:50]
        )

        context.update(
            {
                "form_errors": getattr(self, "form_errors", []),
                "tags_whitelist_json": json.dumps(whitelist_tags),
                "content_type_id": ContentType.objects.get_for_model(Project).pk,
            }
        )
        return context

    def get_form_kwargs(self) -> dict:
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def create_action_records(self):
        self.object: Project
        current_data = ProjectChangesSerializer(self.object).data
        changed_fields = [
            k
            for k in current_data
            if k in self.previous_data and current_data[k] != self.previous_data[k]
        ]
        # breakpoint()
        for field in changed_fields:
            if field == "executive_owners":
                added_people = set(current_data["executive_owners"]) - set(
                    self.previous_data["executive_owners"]
                )
                for person in added_people:
                    text = f"{person.full_name} was assigned to Executive Owner"
                    record(
                        project=self.object,
                        action_type=ActionType.ASSIGNED_ROLE,
                        editor=self.request.user,
                        description=text,
                    )
            elif field == "finance_leads":
                added_people = set(current_data["finance_leads"]) - set(
                    self.previous_data["finance_leads"]
                )
                for person in added_people:
                    text = f"{person.full_name} was assigned to Finance Lead"
                    record(
                        project=self.object,
                        action_type=ActionType.ASSIGNED_ROLE,
                        editor=self.request.user,
                        description=text,
                    )
            elif field == "business_leads":
                added_people = set(current_data["business_leads"]) - set(
                    self.previous_data["business_leads"]
                )
                for person in added_people:
                    text = f"{person.full_name} was assigned to Business Lead"
                    record(
                        project=self.object,
                        action_type=ActionType.ASSIGNED_ROLE,
                        editor=self.request.user,
                        description=text,
                    )
            elif field == "business_analysts":
                added_people = set(current_data["business_analysts"]) - set(
                    self.previous_data["business_analysts"]
                )
                for person in added_people:
                    text = f"{person.full_name} was assigned to Business Analyst"
                    record(
                        project=self.object,
                        action_type=ActionType.ASSIGNED_ROLE,
                        editor=self.request.user,
                        description=text,
                    )
            elif field == "other_stakeholders":
                added_people = set(current_data["other_stakeholders"]) - set(
                    self.previous_data["other_stakeholders"]
                )
                for person in added_people:
                    text = f"{person.full_name} was assigned to Additional Stakeholder"
                    record(
                        project=self.object,
                        action_type=ActionType.ASSIGNED_ROLE,
                        editor=self.request.user,
                        description=text,
                    )

            elif field == "project_managers":
                added_people = set(current_data["project_managers"]) - set(
                    self.previous_data["project_managers"]
                )
                for person in added_people:
                    text = f"{person.full_name} was assigned to Project Manager"
                    record(
                        project=self.object,
                        action_type=ActionType.ASSIGNED_ROLE,
                        editor=self.request.user,
                        description=text,
                    )
            elif field == "has_technology_components" and (
                self.object.has_technology_components
                or "has_technology_components" in self.previous_data.keys()
            ):
                record(
                    project=self.object,
                    action_type=ActionType.CHANGED_TECHNICAL_COMPONENT,
                    editor=self.request.user,
                )
            elif field == "project_state":
                record(
                    project=self.object,
                    action_type=ActionType.CHANGED_STATE,
                    editor=self.request.user,
                )
            elif field == "phase":
                record(
                    project=self.object,
                    action_type=ActionType.CHANGED_PHASE,
                    editor=self.request.user,
                )
            elif field == "health":
                record(
                    project=self.object,
                    action_type=ActionType.CHANGED_HEALTH,
                    editor=self.request.user,
                )
            elif field == "corporate_communication_needs":
                record(
                    project=self.object,
                    action_type=ActionType.NEEDS_CORPORATE_COMMUNICATION,
                    editor=self.request.user,
                )
            elif field == "executive_actions":
                added_executive_actions = set(current_data["executive_actions"]) - set(
                    self.previous_data["executive_actions"]
                )
                for executive_action in added_executive_actions:
                    text = f"Executive Action Needed: {executive_action.action_display} - {executive_action.text}"
                    record(
                        project=self.object,
                        action_type=ActionType.ADDED_EXECUTIVE_ACTION,
                        editor=self.request.user,
                        description=text,
                    )
            elif field == "recent_accomplishments":
                added_recent_accomplishments = set(
                    current_data["recent_accomplishments"]
                ) - set(self.previous_data["recent_accomplishments"])
                for recent_accomplishment in added_recent_accomplishments:
                    text = f"Recent Accomplishment: {recent_accomplishment.text}"
                    record(
                        project=self.object,
                        action_type=ActionType.ADDED_RECENT_ACCOMPLISHMENT,
                        editor=self.request.user,
                        description=text,
                    )

    def create_notifications(self):
        self.object: Project
        current_data = ProjectChangesSerializer(self.object).data
        changed_fields = [
            k
            for k in current_data
            if k in self.previous_data and current_data[k] != self.previous_data[k]
        ]
        for field in changed_fields:
            if field == "executive_owners":
                added_people = set(current_data["executive_owners"]) - set(
                    self.previous_data["executive_owners"]
                )
                message = "{user} added You as an Executive Owner to <a href='{url}'>{name}</a>.".format(
                    user=self.request.user.full_name,
                    url=reverse("project_detail", args=[self.object.pk]),
                    name=self.object.name,
                )
                notify(
                    message_type=MessageTypes.ROLE_ASSIGNED,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                    recipients=added_people,
                )
            elif field == "finance_leads":
                added_people = set(current_data["finance_leads"]) - set(
                    self.previous_data["finance_leads"]
                )
                message = "{user} added You as a Finance Lead to <a href='{url}'>{name}</a>.".format(
                    user=self.request.user.full_name,
                    url=reverse("project_detail", args=[self.object.pk]),
                    name=self.object.name,
                )
                notify(
                    message_type=MessageTypes.ROLE_ASSIGNED,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                    recipients=added_people,
                )
            elif field == "business_leads":
                added_people = set(current_data["business_leads"]) - set(
                    self.previous_data["business_leads"]
                )
                message = "{user} added You as a Business Lead to <a href='{url}'>{name}</a>.".format(
                    user=self.request.user.full_name,
                    url=reverse("project_detail", args=[self.object.pk]),
                    name=self.object.name,
                )
                notify(
                    message_type=MessageTypes.ROLE_ASSIGNED,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                    recipients=added_people,
                )
            elif field == "business_analysts":
                added_people = set(current_data["business_analysts"]) - set(
                    self.previous_data["business_analysts"]
                )
                message = "{user} added You as a Business Analyst to <a href='{url}'>{name}</a>.".format(
                    user=self.request.user.full_name,
                    url=reverse("project_detail", args=[self.object.pk]),
                    name=self.object.name,
                )
                notify(
                    message_type=MessageTypes.ROLE_ASSIGNED,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                    recipients=added_people,
                )
            elif field == "other_stakeholders":
                added_people = set(current_data["other_stakeholders"]) - set(
                    self.previous_data["other_stakeholders"]
                )
                message = "{user} added You as a Stakeholder to <a href='{url}'>{name}</a>.".format(
                    user=self.request.user.full_name,
                    url=reverse("project_detail", args=[self.object.pk]),
                    name=self.object.name,
                )
                notify(
                    message_type=MessageTypes.ROLE_ASSIGNED,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                    recipients=added_people,
                )

            elif field == "project_managers":
                added_people = set(current_data["project_managers"]) - set(
                    self.previous_data["project_managers"]
                )
                message = "{user} added You as a Project Manager to <a href='{url}'>{name}</a>.".format(
                    user=self.request.user.full_name,
                    url=reverse("project_detail", args=[self.object.pk]),
                    name=self.object.name,
                )
                notify(
                    message_type=MessageTypes.ROLE_ASSIGNED,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                    recipients=added_people,
                )
            elif (
                field == "has_technology_components"
                and self.object.has_technology_components
            ):
                message = "{user} marked <a href='{url}'>{name}</a> as a project with Technical Components.".format(
                    user=self.request.user.full_name,
                    url=reverse("project_detail", args=[self.object.pk]),
                    name=self.object.name,
                )
                notify(
                    message_type=MessageTypes.PROJECT_TECH_COMPONENTS,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                )
            elif field == "project_state":
                message = "{user} changed the state of <a href='{url}'>{name}</a> to {state}.".format(
                    user=self.request.user.full_name,
                    url=reverse("project_detail", args=[self.object.pk]),
                    name=self.object.name,
                    state=self.object.project_state_display,
                )
                notify(
                    message_type=MessageTypes.PROJECT_STATE_CHANGE,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                )
            elif field == "phase":
                message = "{user} changed the phase of <a href='{url}'>{name}</a> to {phase}.".format(
                    user=self.request.user.full_name,
                    url=reverse("project_detail", args=[self.object.pk]),
                    name=self.object.name,
                    phase=self.object.phase_display,
                )
                notify(
                    message_type=MessageTypes.PROJECT_PHASE_CHANGE,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                )
            elif field == "health":
                message = "{user} changed the health of <a href='{url}'>{name}</a> to {health}.".format(
                    user=self.request.user.full_name,
                    url=reverse("project_detail", args=[self.object.pk]),
                    name=self.object.name,
                    health=self.object.current_health.health_display,
                )
                notify(
                    message_type=MessageTypes.HEALTH_CHANGE,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                )
            elif field == "status":
                message = f"{self.request.user.full_name} updated the status of <a href='{reverse('project_detail', args=[self.object.pk])}'>{self.object.name}</a>."

                notify(
                    message_type=MessageTypes.STATUS_UPDATE,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                )
            elif (
                field == "corporate_communication_needs"
                and self.object.corporate_communication_needs
                != Project.COMMUNICATION_NEED_LOW
            ):
                message = f"{self.request.user.full_name} indicated need on <a href='{reverse('project_detail', args=[self.object.pk])}'>{self.object.name}</a>."

                notify(
                    message_type=MessageTypes.CORPORATE_COMMUNICATION_NEEDED,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                )

    def forms_valid(self, form: ModelForm, inlines) -> HttpResponse:
        self.object: Project
        response = super().forms_valid(form, inlines)
        # self.object.set_health()
        # self.object.set_financial_tables()
        # self.object.set_savings_table()
        self.create_action_records()
        self.create_notifications()
        messages.success(self.request, "Project has been updated.")
        return response

    def forms_invalid(self, form: ModelForm, inlines) -> HttpResponse:
        self.form_errors = collect_form_errors(form, inlines)
        return super().forms_invalid(form, inlines)

    def get_success_url(self):
        return reverse("project_detail", args=[self.object.pk])


class ProjectUpdateFinancialsView(
    LoginRequiredMixin,
    RolePermissionMixin,
    ProjectDeactivatedMixin,
    NamedFormsetsMixin,
    UpdateWithInlinesView,
):
    model = Project
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = Project
    queryset = Project.objects.filtered_for_project()
    form_class = ProjectFinancialsSectionForm
    inlines = [ProjectCapitalExpenditureInline, ProjectOperationalExpenditureInline]
    inlines_names = [
        "project_capital_expenditure_formset",
        "project_operational_expenditure_formset",
    ]
    template_name = "projects/project_financials_form.html"

    def post(self, request, *args, **kwargs):
        self.previous_data = ProjectChangesSerializer(self.get_object()).data
        return super().post(request, *args, **kwargs)

    def get_context_data(self, **kwargs) -> dict:
        context = super().get_context_data(**kwargs)
        context["form_errors"] = getattr(self, "form_errors", [])
        return context

    def create_notifications(self):
        self.object: Project
        message = "{user} added spend data to <a href='{url}'>{name}</a>.".format(
            user=self.request.user.full_name,
            url=reverse("project_detail", args=[self.object.pk]),
            name=self.object.name,
        )
        notify(
            message_type=MessageTypes.NEW_SPEND_DATA,
            message=message,
            creator=self.request.user,
            content_object=self.object,
        )

    def forms_valid(self, form: ModelForm, inlines) -> HttpResponse:
        self.object: Project
        response = super().forms_valid(form, inlines)
        self.object.set_financial_tables()
        self.object.save()
        self.create_notifications()
        messages.success(self.request, "Project financials have been updated.")
        return response

    def forms_invalid(self, form: ModelForm, inlines) -> HttpResponse:
        self.form_errors = collect_form_errors(form, inlines)
        return super().forms_invalid(form, inlines)

    def get_success_url(self) -> str:
        return reverse("project_detail", args=[self.object.pk])


class ProjectUpdateSavingsView(
    LoginRequiredMixin,
    RolePermissionMixin,
    ProjectDeactivatedMixin,
    NamedFormsetsMixin,
    UpdateWithInlinesView,
):
    model = Project
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = Project
    queryset = Project.objects.filtered_for_project().filter(
        savings_start_date__isnull=False, savings_end_date__isnull=False
    )
    form_class = ProjectSavingsSectionForm
    inlines = [ProjectSavingsInline]
    inlines_names = ["project_savings_formset"]
    template_name = "projects/project_savings_form.html"

    def create_notifications(self):
        self.object: Project
        message = "{user} added benefits data to <a href='{url}'>{name}</a>.".format(
            user=self.request.user.full_name,
            url=reverse("project_detail", args=[self.object.pk]),
            name=self.object.name,
        )
        notify(
            message_type=MessageTypes.NEW_BENEFIT_TRACKING_DATA,
            message=message,
            creator=self.request.user,
            content_object=self.object,
        )

    def forms_valid(self, form: ModelForm, inlines) -> HttpResponse:
        self.object: Project
        response = super().forms_valid(form, inlines)
        self.object.set_savings_table()
        self.create_notifications()
        messages.success(self.request, "Project savings has been updated.")
        return response

    def get_success_url(self) -> str:
        return reverse("project_detail", args=[self.object.pk])


class ProjectUpdateStatusView(
    LoginRequiredMixin,
    RolePermissionMixin,
    ProjectDeactivatedMixin,
    NamedFormsetsMixin,
    UpdateWithInlinesView,
):
    model = Project
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = Project
    queryset = Project.objects.filtered_for_project()
    form_class = ProjectStatusForm
    inlines = [
        ProjectRecentAccomplishmentUpdateInline,
        ProjectPlannedActivityUpdateInline,
        ProjectExecutiveActionUpdateInline,
    ]
    inlines_names = [
        "project_recent_accomplishments_formset",
        "project_planned_activities_formset",
        "project_executive_actions_formset",
    ]
    template_name = "projects/project_status_form.html"

    def post(self, request, *args, **kwargs):
        self.previous_data = ProjectChangesSerializer(self.get_object()).data
        return super().post(request, *args, **kwargs)

    def get_context_data(self, **kwargs) -> dict:
        context = super().get_context_data(**kwargs)
        context["form_errors"] = getattr(self, "form_errors", [])
        return context

    def get_form_kwargs(self) -> dict:
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def create_action_records(self):
        self.object: Project
        current_data = ProjectChangesSerializer(self.object).data
        changed_fields = [
            k
            for k in current_data
            if k in self.previous_data and current_data[k] != self.previous_data[k]
        ]
        for field in changed_fields:
            if field == "project_state":
                record(
                    project=self.object,
                    action_type=ActionType.CHANGED_STATE,
                    editor=self.request.user,
                )
            elif field == "phase":
                record(
                    project=self.object,
                    action_type=ActionType.CHANGED_PHASE,
                    editor=self.request.user,
                )
            elif field == "health":
                record(
                    project=self.object,
                    action_type=ActionType.CHANGED_HEALTH,
                    editor=self.request.user,
                )
            elif field == "corporate_communication_needs":
                record(
                    project=self.object,
                    action_type=ActionType.NEEDS_CORPORATE_COMMUNICATION,
                    editor=self.request.user,
                )
            elif field == "executive_actions":
                added_executive_actions = set(current_data["executive_actions"]) - set(
                    self.previous_data["executive_actions"]
                )
                for executive_action in added_executive_actions:
                    text = f"Executive Action Needed: {executive_action.action_display} - {executive_action.text}"
                    record(
                        project=self.object,
                        action_type=ActionType.ADDED_EXECUTIVE_ACTION,
                        editor=self.request.user,
                        description=text,
                    )
            elif field == "recent_accomplishments":
                added_recent_accomplishments = set(
                    current_data["recent_accomplishments"]
                ) - set(self.previous_data["recent_accomplishments"])
                for recent_accomplishment in added_recent_accomplishments:
                    text = f"Recent Accomplishment: {recent_accomplishment.text}"
                    record(
                        project=self.object,
                        action_type=ActionType.ADDED_RECENT_ACCOMPLISHMENT,
                        editor=self.request.user,
                        description=text,
                    )

    def create_notifications(self):
        self.object: Project
        current_data = ProjectChangesSerializer(self.object).data
        changed_fields = [
            k
            for k in current_data
            if k in self.previous_data and current_data[k] != self.previous_data[k]
        ]
        for field in changed_fields:
            if field == "phase":
                message = "{user} changed the phase of <a href='{url}'>{name}</a> to {phase}.".format(
                    user=self.request.user.full_name,
                    url=reverse("project_detail", args=[self.object.pk]),
                    name=self.object.name,
                    phase=self.object.phase_display,
                )
                notify(
                    message_type=MessageTypes.PROJECT_PHASE_CHANGE,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                )
            elif field == "health":
                message = "{user} changed the health of <a href='{url}'>{name}</a> to {health}.".format(
                    user=self.request.user.full_name,
                    url=reverse("project_detail", args=[self.object.pk]),
                    name=self.object.name,
                    health=self.object.current_health.health_display,
                )
                notify(
                    message_type=MessageTypes.HEALTH_CHANGE,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                )
            elif field == "status":
                message = f"{self.request.user.full_name} updated the status of <a href='{reverse('project_detail', args=[self.object.pk])}'>{self.object.name}</a>."
                notify(
                    message_type=MessageTypes.STATUS_UPDATE,
                    message=message,
                    creator=self.request.user,
                    content_object=self.object,
                )

    def forms_valid(self, form: ModelForm, inlines) -> HttpResponse:
        self.object: Project
        response = super().forms_valid(form, inlines)
        # self.object.set_health()
        self.create_action_records()
        self.create_notifications()
        messages.success(self.request, "Project statuses have been updated.")
        return response

    def forms_invalid(self, form: ModelForm, inlines) -> HttpResponse:
        self.form_errors = collect_form_errors(form, inlines)
        return super().forms_invalid(form, inlines)

    def get_success_url(self) -> str:
        return reverse("project_detail", args=[self.object.pk])


class ProjectDeleteView(
    LoginRequiredMixin, RolePermissionMixin, ProjectDeactivatedMixin, DeleteView
):
    model = Project
    required_permission = "delete"
    permission_denied_template = "projects/project_no_access.html"
    queryset = Project.objects.filtered_for_project()
    permission_required = "project.delete_project"
    template_name = "projects/project_confirm_delete.html"

    def create_notifications(self):
        self.object: Project
        message = "{user} deleted {name}".format(
            user=self.request.user, name=self.object.name
        )
        notify(
            message_type=MessageTypes.PROJECT_DELETED,
            message=message,
            creator=self.request.user,
            content_object=self.object,
        )

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object: Project = self.get_object()
        self.object.active = False
        self.object.save()
        self.create_notifications()
        messages.success(request, "Project has been deactivated.")
        success_url = self.get_success_url()
        return HttpResponseRedirect(success_url)

    def get_success_url(self) -> str:
        url = reverse("project_list")
        return f"{url}?state=active&order_by=end_date"


class ProjectRestoreView(CompanyAdminRequiredMixin, DeleteView):
    queryset = Project.objects.filtered_for_project()
    permission_required = "project.delete_project"
    template_name = "projects/project_confirm_restore.html"
    success_url = reverse_lazy("project_list")

    def create_notifications(self):
        self.object: Project
        message = "{user} restored {name}".format(
            user=self.request.user, name=self.object.name
        )
        notify(
            message_type=MessageTypes.PROJECT_STATE_CHANGE,
            message=message,
            creator=self.request.user,
            content_object=self.object,
        )

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object: Project = self.get_object()
        self.object.active = True
        self.object.save()
        self.create_notifications()
        success_url = self.get_success_url()
        messages.success(request, "Project has been restored.")
        return HttpResponseRedirect(success_url)


class ProjectDuplicateView(
    LoginRequiredMixin, RolePermissionMixin, ProjectDeactivatedMixin, DeleteView
):
    model = Project
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    queryset = Project.objects.filtered_for_project(only_active=False)
    permission_required = "projects.add_project"
    template_name = "projects/project_confirm_duplicate.html"

    def create_action_records(self):
        self.object: Project
        record(
            project=self.object,
            action_type=ActionType.CREATED_PROJECT,
            editor=self.request.user,
        )

    def create_notifications(self):
        self.object: Project
        message = "{user} added <a href='{url}'>{name}</a> cloned from <a href='{original_url}'>{original_name}</a>.".format(
            user=self.request.user.full_name,
            url=reverse("project_detail", args=[self.object.pk]),
            name=self.object.name,
            original_url=reverse("project_detail", args=[self.original_object.pk]),
            original_name=self.original_object.name,
        )
        notify(
            message_type=MessageTypes.PROJECT_NEW,
            message=message,
            creator=self.request.user,
            content_object=self.object,
        )

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.original_object: Project = self.get_object()
        self.object = self.original_object.duplicate(duplicator=self.request.user)
        self.create_action_records()
        self.create_notifications()
        messages.success(request, "Project has been duplicated.")
        success_url = self.get_success_url()
        return HttpResponseRedirect(success_url)

    def get_success_url(self) -> str:
        return reverse("project_update", args=[self.object.pk])


class ProjectToggleStarAjaxView(LoginRequiredMixin, RolePermissionMixin, DeleteView):
    model = Project
    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    queryset = Project.objects.filtered_for_project()

    def get(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        return HttpResponseNotAllowed(["POST"])

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        with transaction.atomic():
            self.object = self.get_object()
            starred = StarredProject.objects.toggle_starred_project(
                project=self.object, user=self.request.user
            )
        message = {
            "message": f"{self.object} {'starred' if starred else 'unstarred'}",
            "value": starred,
        }
        return JsonResponse(message)


class ProjectRigorWizardMixin(
    LoginRequiredMixin,
    RolePermissionMixin,
    ProjectDeactivatedMixin,
    FormWizardMixin,
):
    model = Project
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    queryset = Project.objects.filtered_for_project()

    def get_completed_step_list(self) -> List[int]:
        self.object: Project
        return get_project_rigor_completed_step_list(self.object)

    def get_context_data(self, **kwargs) -> dict:
        self.object: Project
        context = super().get_context_data(**kwargs)
        context["project"] = RigorProject(
            id=self.object.id,
            name=self.object.name,
            last_update=self.object.modified.strftime("%m/%d/%Y"),
            rigor=self.object.get_rigor(),
            risk=self.object.RISK_DICT[self.object.get_risk()],
            complexity=self.object.COMPLEXITY_DICT[self.object.get_complexity()],
        )
        return context

    def get_success_url(self):
        return reverse("project_detail", args=[self.object.pk])


class ProjectRigorStep1UpdateView(
    SuccessUrlNextMixin, ProjectRigorWizardMixin, UpdateView
):
    fields = ["expected_duration"]
    steps = 10
    step = 1
    template_name = "projects/project_rigor/form_step_1.html"


class ProjectRigorStep2UpdateView(
    SuccessUrlNextMixin, ProjectRigorWizardMixin, UpdateView
):
    fields = ["funding_size"]
    steps = 10
    step = 2
    template_name = "projects/project_rigor/form_step_2.html"


class ProjectRigorStep3UpdateView(
    SuccessUrlNextMixin, ProjectRigorWizardMixin, UpdateView
):
    fields = ["departments_involved"]
    steps = 10
    step = 3
    template_name = "projects/project_rigor/form_step_3.html"


class ProjectRigorStep4UpdateView(
    SuccessUrlNextMixin, ProjectRigorWizardMixin, UpdateView
):
    fields = ["vendors_involved"]
    steps = 10
    step = 4
    template_name = "projects/project_rigor/form_step_4.html"


class ProjectRigorStep5UpdateView(
    SuccessUrlNextMixin, ProjectRigorWizardMixin, UpdateView
):
    fields = ["associated_projects"]
    steps = 10
    step = 5
    template_name = "projects/project_rigor/form_step_5.html"


class ProjectRigorStep6UpdateView(
    SuccessUrlNextMixin, ProjectRigorWizardMixin, UpdateView
):
    fields = ["schedule_motivation"]
    steps = 10
    step = 6
    template_name = "projects/project_rigor/form_step_6.html"


class ProjectRigorStep7UpdateView(
    SuccessUrlNextMixin, ProjectRigorWizardMixin, UpdateView
):
    fields = ["similar_experience"]
    steps = 10
    step = 7
    template_name = "projects/project_rigor/form_step_7.html"


class ProjectRigorStep8UpdateView(
    SuccessUrlNextMixin, ProjectRigorWizardMixin, UpdateView
):
    fields = ["infrastructure_readiness"]
    steps = 10
    step = 8
    template_name = "projects/project_rigor/form_step_8.html"


class ProjectRigorStep9UpdateView(
    SuccessUrlNextMixin, ProjectRigorWizardMixin, UpdateView
):
    fields = ["external_user_privileges"]
    steps = 10
    step = 9
    template_name = "projects/project_rigor/form_step_9.html"


class ProjectRigorStep10UpdateView(
    SuccessUrlNextMixin, ProjectRigorWizardMixin, UpdateView
):
    fields = ["post_project_support"]
    steps = 10
    step = 10
    template_name = "projects/project_rigor/form_step_10.html"


class ProjectRigorWizardReviewView(ProjectRigorWizardMixin, UpdateView):
    fields = ["project_rigor"]
    steps = 10
    step = 11
    template_name = "projects/project_rigor/form_review.html"

    def get_entries(self) -> List[ProjectRigorStepEntry]:
        project: Project = self.object
        entries: List[ProjectRigorStepEntry] = [
            ProjectRigorStepEntry(
                step=1,
                edit_url=reverse("project_rigor_wizard", args=[project.pk, 1]),
                label="Expected Duration",
                value=project.expected_duration_display,
            ),
            ProjectRigorStepEntry(
                step=2,
                edit_url=reverse("project_rigor_wizard", args=[project.pk, 2]),
                label="Expected Funding Size",
                value=project.funding_size_display,
            ),
            ProjectRigorStepEntry(
                step=3,
                edit_url=reverse("project_rigor_wizard", args=[project.pk, 3]),
                label="Department(s) Involved",
                value=project.departments_involved_display,
            ),
            ProjectRigorStepEntry(
                step=4,
                edit_url=reverse("project_rigor_wizard", args=[project.pk, 4]),
                label="Vendor Involvement",
                value=project.vendors_involved_display,
            ),
            ProjectRigorStepEntry(
                step=5,
                edit_url=reverse("project_rigor_wizard", args=[project.pk, 5]),
                label="Associated Projects",
                value=project.associated_projects_display,
            ),
            ProjectRigorStepEntry(
                step=6,
                edit_url=reverse("project_rigor_wizard", args=[project.pk, 6]),
                label="Project Schedule Motivation",
                value=project.schedule_motivation_display,
            ),
            ProjectRigorStepEntry(
                step=7,
                edit_url=reverse("project_rigor_wizard", args=[project.pk, 7]),
                label="Similar Project Experience",
                value=project.similar_experience_display,
            ),
            ProjectRigorStepEntry(
                step=8,
                edit_url=reverse("project_rigor_wizard", args=[project.pk, 8]),
                label="Infrastructure Readiness",
                value=project.infrastructure_readiness_display,
            ),
            ProjectRigorStepEntry(
                step=9,
                edit_url=reverse("project_rigor_wizard", args=[project.pk, 9]),
                label="External User Privileges",
                value=project.external_user_privileges_display,
            ),
            ProjectRigorStepEntry(
                step=10,
                edit_url=reverse("project_rigor_wizard", args=[project.pk, 10]),
                label="Post-Project Support",
                value=project.post_project_support_display,
            ),
        ]
        return entries

    def get_context_data(self, **kwargs) -> dict:
        context = super().get_context_data(**kwargs)
        context["entries"] = self.get_entries()
        return context


class ProjectRigorWizardView(View):
    def dispatch(
        self, request: HttpRequest, step: int = 1, *args: Any, **kwargs: Any
    ) -> HttpResponse:
        if not request.user.is_authenticated:
            return HttpResponse(status_code=404)
        if step == 1:
            return ProjectRigorStep1UpdateView.as_view()(request, *args, **kwargs)
        elif step == 2:
            return ProjectRigorStep2UpdateView.as_view()(request, *args, **kwargs)
        elif step == 3:
            return ProjectRigorStep3UpdateView.as_view()(request, *args, **kwargs)
        elif step == 4:
            return ProjectRigorStep4UpdateView.as_view()(request, *args, **kwargs)
        elif step == 5:
            return ProjectRigorStep5UpdateView.as_view()(request, *args, **kwargs)
        elif step == 6:
            return ProjectRigorStep6UpdateView.as_view()(request, *args, **kwargs)
        elif step == 7:
            return ProjectRigorStep7UpdateView.as_view()(request, *args, **kwargs)
        elif step == 8:
            return ProjectRigorStep8UpdateView.as_view()(request, *args, **kwargs)
        elif step == 9:
            return ProjectRigorStep9UpdateView.as_view()(request, *args, **kwargs)
        elif step == 10:
            return ProjectRigorStep10UpdateView.as_view()(request, *args, **kwargs)
        else:
            return ProjectRigorStep1UpdateView.as_view()(request, *args, **kwargs)


class PersonCreateAjaxView(LoginRequiredMixin, BaseFormView):
    form_class = PersonForm

    def get(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        return HttpResponseNotAllowed(["POST"])

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def form_valid(self, form: ModelForm) -> HttpResponse:
        person = form.save()
        message = {
            "data": {
                "id": person.id,
                "first_name": person.first_name,
                "last_name": person.last_name,
                "email": person.email,
            },
            "message": "Person added.",
        }
        return JsonResponse(message)

    def form_invalid(self, form: ModelForm):
        message = {"errors": form.errors}
        return JsonResponse(message, status=400)


class PersonListView(CompanyCompanyAdminRequiredMixin, CompanyQuerysetMixin, ListView):
    queryset = User.objects.all()
    paginate_by = 50
    template_name = "projects/person_list.html"

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data(*args, **kwargs)
        context["filter_form"] = PersonFilterForm(
            self.request.GET, user=self.request.user
        )
        context["person_list"] = self.get_queryset()
        return context

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        filterset = PersonFilter(self.request.GET, queryset=queryset)
        return filterset.qs


class ProjectTrackerPersonCreateView(
    CompanyCompanyAdminRequiredMixin, CompanyQuerysetMixin, CreateView
):
    queryset = User.objects.all()
    form_class = ProjectTrackerPersonForm
    template_name = "projects/person_form_project_tracker.html"
    success_url = reverse_lazy("person_list")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)

        division_choices = [
            {
                "id": div.id,
                "name": div.abbreviation,
                "checked": False,
            }
            for div in Division.objects.all().order_by("name")
        ]
        context["division_choices"] = division_choices
        return context


class ProjectTrackerPersonUpdateView(
    CompanyCompanyAdminRequiredMixin, CompanyQuerysetMixin, UpdateView
):
    queryset = User.objects.all()
    form_class = ProjectTrackerPersonForm
    template_name = "projects/person_form_project_tracker.html"
    success_url = reverse_lazy("person_list")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)

        division_choices = [
            {
                "id": div.id,
                "name": div.abbreviation,
            }
            for div in Division.objects.all().order_by("name")
        ]
        context["division_choices"] = division_choices

        return context


class FinancialPlanningPersonCreateView(
    CompanyCompanyAdminRequiredMixin, CompanyQuerysetMixin, CreateView
):
    queryset = User.objects.all()
    form_class = FinancialPlannerPersonForm
    template_name = "projects/person_form_financial_planning.html"
    success_url = reverse_lazy("person_list")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs


class FinancialPlanningPersonUpdateView(
    CompanyCompanyAdminRequiredMixin, CompanyQuerysetMixin, UpdateView
):
    queryset = User.objects.all()
    form_class = FinancialPlannerPersonForm
    template_name = "projects/person_form_financial_planning.html"
    success_url = reverse_lazy("person_list")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs


class IdeaTrackerPersonCreateView(ProjectTrackerPersonCreateView):
    form_class = IdeaTrackerPersonForm
    template_name = "projects/person_form_idea_tracker.html"


class IdeaTrackerPersonUpdateView(ProjectTrackerPersonUpdateView):
    form_class = IdeaTrackerPersonForm
    template_name = "projects/person_form_idea_tracker.html"


class PersonCreateView(View):
    def dispatch(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        site = get_site(request)
        if site == "project_tracker":
            return ProjectTrackerPersonCreateView.as_view()(request, *args, **kwargs)
        elif site == "financial_planning":
            return FinancialPlanningPersonCreateView.as_view()(request, *args, **kwargs)
        elif site == "idea_tracker":
            return IdeaTrackerPersonCreateView.as_view()(request, *args, **kwargs)


class PersonUpdateView(LoginRequiredMixin, View):
    def dispatch(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        site = get_site(request)
        if site == "project_tracker":
            return ProjectTrackerPersonUpdateView.as_view()(request, *args, **kwargs)
        elif site == "financial_planning":
            return FinancialPlanningPersonUpdateView.as_view()(request, *args, **kwargs)
        elif site == "idea_tracker":
            return IdeaTrackerPersonUpdateView.as_view()(request, *args, **kwargs)


class PersonDeactivateView(CompanyCompanyAdminRequiredMixin, DeleteView):
    model = User
    template_name = "projects/person_deactivate.html"
    success_url = reverse_lazy("person_list")

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object: User = cast(User, self.get_object())
        success_url = self.get_success_url()
        self.object.is_active = False
        self.object.save()
        return HttpResponseRedirect(success_url)


class CommentCreateView(LoginRequiredMixin, CreateView):
    model = Comment
    form_class = CommentForm
    project_instance = None

    def create_action_record(self):
        self.object: Comment
        text = '{user} added comment: "{comment}"'.format(
            user=self.object.creator.full_name, comment=self.object.text
        )
        record(
            project=self.project_instance,
            action_type=ActionType.ADDED_COMMENT,
            editor=self.request.user,
            description=text,
        )

    def create_notifications(self):
        message = (
            "{user} added a comment to project <a href='{url}'>{name}</a>.".format(
                user=self.request.user.full_name,
                url=reverse("project_detail", args=[self.project_instance.pk]),
                name=self.project_instance.name,
            )
        )
        notify(
            message_type=MessageTypes.COMMENT_ADDED,
            message=message,
            creator=self.request.user,
            content_object=self.project_instance,
        )

    def get_project_instance(self):
        if self.project_instance is None:
            self.project_instance = Project.objects.get(pk=self.kwargs.get("pk"))
        return self.project_instance

    def form_valid(self, form):
        response = super().form_valid(form)
        self.create_action_record()
        self.create_notifications()
        messages.success(self.request, "Comment added.")
        return response

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        kwargs["project"] = self.get_project_instance()
        return kwargs

    def get_success_url(self):
        return reverse("project_detail", args=[self.object.project.pk])


class RaidView(LoginRequiredMixin, RolePermissionMixin, DetailView):
    model = Project
    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    template_name = "projects/project_raid.html"
    queryset = Project.objects.filter(active=True)
    pk_url_kwarg = "project_pk"

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        project_pk = self.kwargs.get("project_pk")

        qs = RaidReport.objects.select_related(
            "risk", "issue", "assumption", "dependency"
        ).filter(
            Q(risk__project__id=project_pk)
            | Q(assumption__project__id=project_pk)
            | Q(issue__project__id=project_pk)
            | Q(dependency__project__id=project_pk)
        )

        raid_risk = qs.filter(type="risk").order_by("-risk__date_identified")
        raid_assumption = qs.filter(type="assumption").order_by(
            "-assumption__date_identified"
        )
        raid_issue = qs.filter(type="issue").order_by("-issue__date_identified")
        raid_dependency = qs.filter(type="dependency").order_by(
            "-dependency__date_identified"
        )

        risk_ids = raid_risk.values("risk_id")
        assumption_ids = raid_assumption.values("assumption_id")
        issue_ids = raid_issue.values("issue_id")
        dependency_ids = raid_dependency.values("dependency_id")
        raid_ids = json.dumps(
            {
                "risk_ids": list(risk_ids),
                "assumption_ids": list(assumption_ids),
                "issue_ids": list(issue_ids),
                "dependency_ids": list(dependency_ids),
            }
        )
        context["raid_ids"] = raid_ids
        context["risk_data"] = raid_risk
        context["assumption_data"] = raid_assumption
        context["issues_data"] = raid_issue
        context["dependency_data"] = raid_dependency
        return context


class RaidRisksCreateView(LoginRequiredMixin, RolePermissionMixin, CreateView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = RaidRisk
    form_class = RaidRisksForm
    template_name = "projects/partials/raid_risk.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user.email
        return kwargs

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        context["instance"] = "Add"
        return context

    def form_valid(self, form, *args, **kwargs):
        self.object = form.save(commit=False)
        self.object.project_id = self.project.id
        self.object.save()

        return super(ModelFormMixin, self).form_valid(form)

    def get_success_url(self):
        return reverse("project_raid_log", args=[self.object.project.pk])


class RaidAssumptionsCreateView(LoginRequiredMixin, RolePermissionMixin, CreateView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = RaidAssumption
    form_class = RaidAssumptionForm
    template_name = "projects/partials/raid_assumption.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user.email
        return kwargs

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        context["instance"] = "Add"

        return context

    def form_valid(self, form, *args, **kwargs):
        self.object = form.save(commit=False)
        self.object.project_id = self.project.id
        self.object.save()

        return super(ModelFormMixin, self).form_valid(form)

    def get_success_url(self):
        return reverse("project_raid_log", args=[self.object.project.pk])


class RaidIssuesCreateView(LoginRequiredMixin, RolePermissionMixin, CreateView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = RaidIssues
    form_class = RaidIssuesForm
    template_name = "projects/partials/raid_issue.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user.email
        return kwargs

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        context["instance"] = "Add"

        return context

    def form_valid(self, form, *args, **kwargs):
        self.object = form.save(commit=False)
        self.object.project_id = self.project.id
        self.object.save()

        return super(ModelFormMixin, self).form_valid(form)

    def get_success_url(self):
        return reverse("project_raid_log", args=[self.object.project.pk])


class RaidDependenciesCreateView(LoginRequiredMixin, RolePermissionMixin, CreateView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = RaidDependencies
    form_class = RaidDependenciesForm
    template_name = "projects/partials/raid_dependency.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user.email
        return kwargs

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        context["instance"] = "Add"

        return context

    def form_valid(self, form, *args, **kwargs):
        self.object = form.save(commit=False)
        self.object.project_id = self.project.id
        self.object.save()

        return super(ModelFormMixin, self).form_valid(form)

    def get_success_url(self):
        return reverse("project_raid_log", args=[self.object.project.pk])


class RaidRiskUpdateView(LoginRequiredMixin, RolePermissionMixin, UpdateView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = RaidRisk
    form_class = RaidRisksForm
    template_name = "projects/partials/raid_risk.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = None
        return kwargs

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        context["instance"] = "Edit"
        context["id"] = self.object.pk
        return context

    def form_valid(self, form, *args, **kwargs):
        self.object = form.save(commit=False)
        self.object.project_id = self.project.id
        if self.object.status == "Issue":
            RaidIssues.objects.create(
                project=self.project,
                section=self.object.section,
                date_identified=self.object.date_identified,
                identifier=self.object.identifier,
                issue_description=self.object.risk_description,
                issue_owner=self.object.risk_owner,
                priority=self.object.risk_rating,
            )
        self.object.save()

        return super(ModelFormMixin, self).form_valid(form)

    def get_success_url(self):
        return reverse("project_raid_log", args=[self.object.project.pk])


class RaidAssumptionUpdateView(LoginRequiredMixin, RolePermissionMixin, UpdateView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = RaidAssumption
    form_class = RaidAssumptionForm
    template_name = "projects/partials/raid_assumption.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = None
        return kwargs

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        context["instance"] = "Edit"
        context["id"] = self.object.pk

        return context

    def form_valid(self, form, *args, **kwargs):
        self.object = form.save(commit=False)
        self.object.project_id = self.project.id
        self.object.save()

        return super(ModelFormMixin, self).form_valid(form)

    def get_success_url(self):
        return reverse("project_raid_log", args=[self.object.project.pk])


class RaidIssueUpdateView(LoginRequiredMixin, RolePermissionMixin, UpdateView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = RaidIssues
    form_class = RaidIssuesForm
    template_name = "projects/partials/raid_issue.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = None
        return kwargs

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        context["instance"] = "Edit"
        context["id"] = self.object.pk

        return context

    def form_valid(self, form, *args, **kwargs):
        self.object = form.save(commit=False)
        self.object.project_id = self.project.id
        self.object.save()

        return super(ModelFormMixin, self).form_valid(form)

    def get_success_url(self):
        return reverse("project_raid_log", args=[self.object.project.pk])


class RaidDependencyUpdateView(LoginRequiredMixin, RolePermissionMixin, UpdateView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = RaidDependencies
    form_class = RaidDependenciesForm
    template_name = "projects/partials/raid_dependency.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = None
        return kwargs

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["instance"] = "Edit"
        context["project"] = self.project
        context["id"] = self.object.pk

        return context

    def form_valid(self, form, *args, **kwargs):
        self.object = form.save(commit=False)
        self.object.project_id = self.project.id
        self.object.save()
        return super(ModelFormMixin, self).form_valid(form)

    def get_success_url(self):
        return reverse("project_raid_log", args=[self.object.project.pk])


class DownloadRaidView(LoginRequiredMixin, RolePermissionMixin, APIView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    permission_classes = (IsAuthenticated,)
    authentication_classes = (SessionAuthentication,)

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def post(self, request, *args, **kwargs):
        workbook = Workbook()
        id_data = self.request.data
        show_hidden = id_data["showHidden"]
        queryset = RaidReport.objects.filter(
            Q(risk__id__in=id_data["risk_ids"])
            | Q(assumption__id__in=id_data["assumption_ids"])
            | Q(issue__id__in=id_data["issue_ids"])
            | Q(dependency__id__in=id_data["dependency_ids"])
        )

        workbook = generate_raid_excel_sheet(workbook, queryset, show_hidden)

        try:
            response = HttpResponse(
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
            response["Content-Disposition"] = "attachment; filename=Raid.xlsx"
            workbook.save(response)
            return response
        except Exception as e:
            return Response({"message": f"Error getting data {e}"})


class RaidRiskView(
    LoginRequiredMixin, CompanyQuerysetMixin, RolePermissionMixin, ListView
):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    model = RaidRisk
    template_name = "projects/partials/raid_risk_view.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        pk = self.kwargs.get("pk")
        risk = RaidRisk.objects.get(pk=pk)
        context["risk"] = risk
        context["project"] = self.project
        return context


class RaidAssumptionView(
    LoginRequiredMixin, CompanyQuerysetMixin, RolePermissionMixin, ListView
):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    model = RaidAssumption
    template_name = "projects/partials/raid_assumption_view.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        raid_assumption_pk = self.kwargs.get("pk")
        assumption = RaidAssumption.objects.get(pk=raid_assumption_pk)
        context["project"] = self.project
        context["assumption"] = assumption
        return context


class RaidIssueView(
    LoginRequiredMixin, CompanyQuerysetMixin, RolePermissionMixin, ListView
):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    model = RaidIssues
    template_name = "projects/partials/raid_issue_view.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        raid_issue_pk = self.kwargs.get("pk")
        issue = RaidIssues.objects.get(pk=raid_issue_pk)
        context["project"] = self.project
        context["issue"] = issue
        return context


class RaidDependencyView(
    LoginRequiredMixin, CompanyQuerysetMixin, RolePermissionMixin, ListView
):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    model = RaidDependencies
    template_name = "projects/partials/raid_dependency_view.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(
            Project,
            pk=self.kwargs["project_pk"],
        )

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        raid_dependency_pk = self.kwargs.get("pk")
        dependency = RaidDependencies.objects.get(pk=raid_dependency_pk)
        context["project"] = self.project
        context["dependency"] = dependency
        return context


class RaidRiskDeleteView(LoginRequiredMixin, RolePermissionMixin, DeleteView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    queryset = RaidRisk.objects.all()
    template_name = "projects/partials/delete_risk.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(Project.objects.all(), pk=self.kwargs["project_pk"])

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data()
        context["project_id"] = self.kwargs.get("project_pk")
        return context

    def post(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object = self.get_object()
        self.object.delete()
        success_url = reverse(
            "project_raid_log", kwargs={"project_pk": kwargs.get("project_pk")}
        )
        response = HttpResponseRedirect(success_url)
        return response


class RaidAssumptionDeleteView(LoginRequiredMixin, RolePermissionMixin, DeleteView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    queryset = RaidAssumption.objects.all()
    template_name = "projects/partials/delete_assumption.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(Project.objects.all(), pk=self.kwargs["project_pk"])

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data()
        context["project_id"] = self.kwargs.get("project_pk")
        return context

    def post(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object = self.get_object()
        self.object.delete()
        success_url = reverse(
            "project_raid_log", kwargs={"project_pk": kwargs.get("project_pk")}
        )
        response = HttpResponseRedirect(success_url)
        return response


class RaidIssueDeleteView(LoginRequiredMixin, RolePermissionMixin, DeleteView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    queryset = RaidIssues.objects.all()
    template_name = "projects/partials/delete_issue.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(Project.objects.all(), pk=self.kwargs["project_pk"])

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data()
        context["project_id"] = self.kwargs.get("project_pk")
        return context

    def post(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object = self.get_object()
        self.object.delete()
        success_url = reverse(
            "project_raid_log", kwargs={"project_pk": kwargs.get("project_pk")}
        )
        response = HttpResponseRedirect(success_url)
        return response


class RaidDependencyDeleteView(LoginRequiredMixin, RolePermissionMixin, DeleteView):
    def get_model(self):
        from apps.projects.models import Project

        return Project

    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    queryset = RaidDependencies.objects.all()
    template_name = "projects/partials/delete_dependency.html"

    @cached_property
    def project(self) -> Project:
        return get_object_or_404(Project.objects.all(), pk=self.kwargs["project_pk"])

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data()
        context["project_id"] = self.kwargs.get("project_pk")
        return context

    def post(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object = self.get_object()
        self.object.delete()
        success_url = reverse(
            "project_raid_log", kwargs={"project_pk": kwargs.get("project_pk")}
        )
        response = HttpResponseRedirect(success_url)
        return response
