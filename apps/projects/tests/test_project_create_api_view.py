from random import randint

from django.urls import reverse
from factory import fuzzy
from rest_framework.test import APIClient

from apps.projects.models import Division, ProjectDivisionImpact
from apps.users.models import User
from apps.projects.serializers import ProjectPersonSerializer
from apps.projects.tests.data import generate_project_data
from apps.users.serializers import UserSerializer
from apps.users.tests import UserFactory
from tests.base import BaseTestCase


def generate_impact_scores():
    first_column = randint(0, 1)
    if first_column:
        return [1, 0, 0, 0, 0, 0]
    else:
        return [0] + [randint(0, 1) for i in range(5)]


def add_impact_data(data):
    data["impacts"] = [
        {"title": div, "values": generate_impact_scores()}
        for div in ProjectDivisionImpact.DIVISIONS
    ]
    data["support_impacts"] = {"values": generate_impact_scores()}
    return data


def replace_people_and_users(data):
    creator = UserFactory()
    user_serializer = UserSerializer(creator)
    data["created_by"] = user_serializer.data
    data["executive_owners"] = ProjectPersonSerializer(
        Person.objects.filter(pk__in=data["executive_owners"]), many=True
    ).data
    data["business_leads"] = ProjectPersonSerializer(
        Person.objects.filter(pk__in=data["business_leads"]), many=True
    ).data
    data["business_analysts"] = ProjectPersonSerializer(
        Person.objects.filter(pk__in=data["business_analysts"]), many=True
    ).data

    data["other_stakeholders"] = ProjectPersonSerializer(
        Person.objects.filter(pk__in=data["other_stakeholders"]), many=True
    ).data
    data["project_managers"] = ProjectPersonSerializer(
        Person.objects.filter(pk__in=data["project_managers"]), many=True
    ).data
    return data


def generate_idea_to_convert_data():
    data = generate_project_data()
    data = add_impact_data(data)
    data = replace_people_and_users(data)
    return data


class ProjectConvertTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION="Bearer ")
        self.data = generate_idea_to_convert_data()
        self.data["idea_id"] = str(randint(0, 999)) + "#" + fuzzy.FuzzyText().fuzz()
        for div in ProjectDivisionImpact.DIVISIONS:
            Division.objects.get_or_create(name=div)

    def test_conversion_from_idea_tracker_query_count(self):
        with self.assertNumQueries(2):
            self.client.post(reverse("api_project_create"), self.data, format="json")

    def test_conversion_from_idea_tracker(self):
        request = self.client.post(
            reverse("api_project_create"), self.data, format="json"
        )
        self.assertEqual(request.status_code, 201)
