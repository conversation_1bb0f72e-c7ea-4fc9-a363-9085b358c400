from django.urls import reverse

from tests.base import BaseTestCase

from ..models import Person
from .factories import ProjectFactory


class ProjectListAPIViewTestCase(BaseTestCase):
    def setUp(self) -> None:
        super().setUp()
        self.regular_user_person = Person.objects.get(email=self.regular_user.email)
        self.public_project = ProjectFactory()
        self.private_project = ProjectFactory(private=True)

    def test_regular_user_should_not_see_private_projects(self):
        self.client.force_login(self.regular_user)
        url = reverse("api_project_list")
        response = self.client.get(url)
        self.assertEqual(response.data["count"], 1)

    def test_admin_user_should_see_all_projects(self):
        self.client.force_login(self.admin_user)
        url = reverse("api_project_list")
        response = self.client.get(url)
        self.assertEqual(response.data["count"], 2)

    def test_regular_user_should_see_private_project_if_named_as_executive_owner(self):
        self.private_project.executive_owners.add(self.regular_user_person)

        self.client.force_login(self.regular_user)
        url = reverse("api_project_list")
        response = self.client.get(url)
        self.assertEqual(response.data["count"], 2)

    def test_regular_user_should_see_private_project_if_named_as_finance_lead(self):
        self.private_project.finance_leads.add(self.regular_user_person)

        self.client.force_login(self.regular_user)
        url = reverse("api_project_list")
        response = self.client.get(url)
        self.assertEqual(response.data["count"], 2)

    def test_regular_user_should_see_private_project_if_named_as_business_analyst(self):
        self.private_project.business_analysts.add(self.regular_user_person)

        self.client.force_login(self.regular_user)
        url = reverse("api_project_list")
        response = self.client.get(url)
        self.assertEqual(response.data["count"], 2)

    def test_regular_user_should_see_private_project_if_named_as_other_stakeholder(
        self,
    ):
        self.private_project.other_stakeholders.add(self.regular_user_person)

        self.client.force_login(self.regular_user)
        url = reverse("api_project_list")
        response = self.client.get(url)
        self.assertEqual(response.data["count"], 2)

    def test_regular_user_should_see_private_project_if_named_as_project_manager(self):
        self.private_project.project_managers.add(self.regular_user_person)

        self.client.force_login(self.regular_user)
        url = reverse("api_project_list")
        response = self.client.get(url)
        self.assertEqual(response.data["count"], 2)
