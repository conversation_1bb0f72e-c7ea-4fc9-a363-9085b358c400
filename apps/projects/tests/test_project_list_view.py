from datetime import timed<PERSON><PERSON>
from unittest.case import skip

from django.db.models import OuterRef, Subquery
from django.urls import reverse
from django.utils import timezone

from tests.base import BaseTestCase

from ..forms import ProjectFilterForm
from ..models import Project, ProjectHealth, ProjectPercentComplete
from .factories import ProjectFactory
from tests.factories import UserFactory


class ProjectListViewTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        for i in range(10):
            ProjectFactory()

    def test_admin_view(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_authenticated_view(self):
        self.client.force_login(self.regular_user)
        url = reverse("project_list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_anonymous_view(self):
        url = reverse("project_list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_query_count(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        with self.assertNumQueries(36):
            self.client.get(url)

    def test_filter_health(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?health=green")
        self.assertEqual(response.status_code, 200)
        qs = response.context["object_list"]
        projects = (
            Project.objects.annotate(
                latest_health=Subquery(
                    ProjectHealth.objects.filter(project=OuterRef("pk"))
                    .order_by("-modified")
                    .values("health")[:1]
                )
            )
            .filter(latest_health="green")
            .values_list("pk", flat=True)
        )
        self.assertQuerySetEqual(
            qs, projects, transform=lambda o: getattr(o, "pk"), ordered=False
        )

    def test_filter_health_multiple(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?health=green&health=red")
        self.assertEqual(response.status_code, 200)
        qs = response.context["object_list"]
        projects = (
            Project.objects.annotate(
                latest_health=Subquery(
                    ProjectHealth.objects.filter(project=OuterRef("pk"))
                    .order_by("-modified")
                    .values("health")[:1]
                )
            )
            .filter(latest_health__in=["green", "red"])
            .values_list("pk", flat=True)
        )
        self.assertQuerySetEqual(
            qs, projects, transform=lambda o: getattr(o, "pk"), ordered=False
        )

    def test_filter_executive_owner(self):
        person: Person = PersonFactory()
        project: Project = ProjectFactory()
        project.executive_owners.add(person)

        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?executive_owner={person.pk}")
        self.assertEqual(response.status_code, 200)
        qs = response.context["object_list"]
        projects = Project.objects.filter(executive_owners=person.pk).values_list(
            "pk", flat=True
        )
        self.assertQuerySetEqual(
            qs, projects, transform=lambda o: getattr(o, "pk"), ordered=False
        )

    def test_filter_business_lead(self):
        person: Person = PersonFactory()
        project: Project = ProjectFactory()
        project.business_leads.add(person)

        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?business_lead={person.pk}")
        self.assertEqual(response.status_code, 200)
        qs = response.context["object_list"]
        projects = Project.objects.filter(business_leads=person.pk).values_list(
            "pk", flat=True
        )
        self.assertQuerySetEqual(
            qs, projects, transform=lambda o: getattr(o, "pk"), ordered=False
        )

    def test_filter_business_analyst(self):
        person: Person = PersonFactory()
        project: Project = ProjectFactory()
        project.business_analysts.add(person)

        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?business_analyst={person.pk}")
        self.assertEqual(response.status_code, 200)
        qs = response.context["object_list"]
        projects = Project.objects.filter(business_analysts=person.pk).values_list(
            "pk", flat=True
        )
        self.assertQuerySetEqual(
            qs, projects, transform=lambda o: getattr(o, "pk"), ordered=False
        )

    def test_filter_other_stakeholder(self):
        person: Person = PersonFactory()
        project: Project = ProjectFactory()
        project.other_stakeholders.add(person)

        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?other_stakeholder={person.pk}")
        self.assertEqual(response.status_code, 200)
        qs = response.context["object_list"]
        projects = Project.objects.filter(other_stakeholders=person.pk).values_list(
            "pk", flat=True
        )
        self.assertQuerySetEqual(
            qs, projects, transform=lambda o: getattr(o, "pk"), ordered=False
        )

    def test_filter_project_manager(self):
        person: Person = PersonFactory()
        project: Project = ProjectFactory()
        project.project_managers.add(person)

        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?project_manager={person.pk}")
        self.assertEqual(response.status_code, 200)
        qs = response.context["object_list"]
        projects = Project.objects.filter(project_managers=person.pk).values_list(
            "pk", flat=True
        )
        self.assertQuerySetEqual(
            qs, projects, transform=lambda o: getattr(o, "pk"), ordered=False
        )

    def test_filter_phase(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?phase=planning")
        self.assertEqual(response.status_code, 200)
        qs = response.context["object_list"]
        projects = Project.objects.filter(phase="planning").values_list("pk", flat=True)
        self.assertQuerySetEqual(
            qs, projects, transform=lambda o: getattr(o, "pk"), ordered=False
        )

    def test_state_phase(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?state=active")
        self.assertEqual(response.status_code, 200)
        qs = response.context["object_list"]
        projects = Project.objects.filter(project_state="active").values_list(
            "pk", flat=True
        )
        self.assertQuerySetEqual(
            qs, projects, transform=lambda o: getattr(o, "pk"), ordered=False
        )

    def test_filter_complete(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?complete_min=40&complete_max=60")
        self.assertEqual(response.status_code, 200)
        qs = response.context["object_list"]
        projects = (
            Project.objects.annotate(
                latest_percentage=Subquery(
                    ProjectPercentComplete.objects.filter(project=OuterRef("pk"))
                    .order_by("-modified")
                    .values("percentage")[:1]
                )
            )
            .filter(latest_percentage__range=(40, 60))
            .values_list("pk", flat=True)
        )
        self.assertQuerySetEqual(
            qs, projects, transform=lambda o: getattr(o, "pk"), ordered=False
        )

    def test_filter_created_within(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?created_within=30")
        self.assertEqual(response.status_code, 200)
        qs = response.context["object_list"]
        projects = Project.objects.filter(
            created__gte=timezone.now() - timedelta(days=30)
        ).values_list("pk", flat=True)
        self.assertQuerySetEqual(
            qs, projects, transform=lambda o: getattr(o, "pk"), ordered=False
        )

    def test_filter_search(self):
        project = ProjectFactory()

        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?search={project.name}")
        self.assertEqual(response.status_code, 200)
        qs = response.context["object_list"]
        projects = Project.objects.search(project.name).values_list("pk", flat=True)
        self.assertQuerySetEqual(
            qs, projects, transform=lambda o: getattr(o, "pk"), ordered=False
        )

    def test_filter_search_redirect(self):
        project = ProjectFactory()

        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?search={project.pk}")
        self.assertRedirects(
            response, f"{reverse('project_detail', args=[project.pk])}"
        )

    def test_order_id_asc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=id")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.with_latest_health_value()
            .order_by("id")
            .values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    def test_order_id_desc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=-id")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.with_latest_health_value()
            .order_by("-id")
            .values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    def test_order_health_asc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=latest_health_value")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.with_latest_health_value()
            .order_by("latest_health_value")
            .values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    def test_order_health_desc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=-latest_health_value")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.with_latest_health_value()
            .order_by("-latest_health_value")
            .values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    def test_order_name_asc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=name")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(Project.objects.order_by("name").values_list("pk", flat=True))
        self.assertEqual(qs, projects)

    def test_order_name_desc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=-name")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(Project.objects.order_by("-name").values_list("pk", flat=True))
        self.assertEqual(qs, projects)

    @skip("Getting duplicate projects in comparision query.")
    def test_order_business_lead_asc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=business_lead")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = (
            Project.objects.all().order_by("business_leads__first_name").distinct()
        )
        projects = [project.pk for project in projects]
        self.assertEqual(qs, projects)

    @skip("Getting duplicate projects in comparision query.")
    def test_order_business_lead_desc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=-business_lead")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.values_list("pk", flat=True)
            .order_by("-business_leads__first_name")
            .distinct()
        )
        self.assertEqual(qs, projects)

    def test_order_project_manager_asc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=project_manager")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.values_list("pk", flat=True)
            .order_by("project_managers__first_name")
            .distinct()
        )
        self.assertEqual(qs, projects)

    def test_order_project_manager_desc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=-project_manager")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.distinct()
            .order_by("-project_managers__first_name")
            .values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    @skip("Getting duplicate projects in comparision query.")
    def test_order_executive_sponsor_asc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=executive_sponsor")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.distinct()
            .order_by("executive_owners__first_name")
            .values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    @skip("Getting duplicate projects in comparision query.")
    def test_order_executive_sponsor_desc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=-executive_sponsor")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.distinct()
            .order_by("-executive_owners__first_name")
            .values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    def test_order_percentage_asc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=latest_percentage")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.with_latest_percentage()
            .order_by("latest_percentage")
            .values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    def test_order_percentage_desc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=-latest_percentage")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.with_latest_percentage()
            .order_by("-latest_percentage")
            .values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    def test_order_end_date_asc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=end_date")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.order_by("end_date").values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    def test_order_end_date_desc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=-end_date")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.order_by("-end_date").values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    def test_order_phase_asc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=phase")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(Project.objects.order_by("phase").values_list("pk", flat=True))
        self.assertEqual(qs, projects)

    def test_order_phase_desc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=-phase")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(Project.objects.order_by("-phase").values_list("pk", flat=True))
        self.assertEqual(qs, projects)

    def test_order_modified_asc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=modified")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.order_by("modified").values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    def test_order_modified_desc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=-modified")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.order_by("-modified").values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    def test_order_starred_asc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=starred")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.with_starred_by_user(self.admin_user)
            .order_by("starred")
            .values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)

    def test_order_starred_desc(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_list")
        response = self.client.get(f"{url}?order_by=-starred")
        self.assertEqual(response.status_code, 200)
        qs = [o.pk for o in response.context["object_list"]]
        projects = list(
            Project.objects.with_starred_by_user(self.admin_user)
            .order_by("-starred")
            .values_list("pk", flat=True)
        )
        self.assertEqual(qs, projects)


class ProjectFilterFormTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        from apps.programs.tests import ProgramFactory

        self.private_program = ProgramFactory(private=True)
        self.public_program = ProgramFactory(private=False)

    def test_regular_user_should_not_see_private_programs(self):
        form = ProjectFilterForm(user=self.regular_user)
        self.assertFalse(self.private_program in form.fields["program"].queryset)
