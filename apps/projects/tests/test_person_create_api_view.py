from random import randint

from django.conf import settings
from django.urls import reverse
from factory import fuzzy

from tests.base import BaseTestCase

from ..models import Person
from .factories import PersonFactory


class PersonListCreateAPIViewTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        for i in range(10):
            PersonFactory()

    def test_admin_view(self):
        self.client.force_login(self.admin_user)
        url = reverse("api_person_list_create")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_authenticated_view(self):
        self.client.force_login(self.regular_user)
        url = reverse("api_person_list_create")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_anonymous_view(self):
        url = reverse("api_person_list_create")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 401)

    def test_token_view(self):
        token = getattr(settings, "AUTH0_ACCESS_TOKEN", "")

        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")
        url = reverse("api_person_list_create")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_query_count(self):
        self.client.force_login(self.admin_user)
        url = reverse("api_person_list_create")
        with self.assertNumQueries(6):
            self.client.get(url)

    def test_email_available_view(self):
        person: Person = PersonFactory()

        self.client.force_login(self.admin_user)
        url = reverse("api_person_email_available", args=[person.email])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content, b"false")

    def test_post_view(self):
        self.client.force_login(self.admin_user)
        url = reverse("api_person_list_create")
        data = {
            "first_name": fuzzy.FuzzyText().fuzz(),
            "last_name": fuzzy.FuzzyText().fuzz(),
            "email": fuzzy.FuzzyText(suffix="@example.com").fuzz(),
        }
        response = self.client.post(url, data=data, format="json")
        self.assertEqual(response.status_code, 201)

    def test_post_view_with_token_authentication(self):
        token = getattr(settings, "AUTH0_ACCESS_TOKEN", "")

        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")
        url = reverse("api_person_list_create")
        data = {
            "first_name": fuzzy.FuzzyText().fuzz(),
            "last_name": fuzzy.FuzzyText().fuzz(),
            "email": fuzzy.FuzzyText(suffix="@example.com").fuzz(),
        }
        response = self.client.post(url, data=data, format="json")
        self.assertEqual(response.status_code, 201)

    def test_post_view_with_roles(self):
        self.client.force_login(self.admin_user)
        url = reverse("api_person_list_create")
        data = {
            "first_name": fuzzy.FuzzyText().fuzz(),
            "last_name": fuzzy.FuzzyText().fuzz(),
            "email": fuzzy.FuzzyText(suffix="@example.com").fuzz(),
            "is_compliance": bool(randint(0, 1)),
            "is_finance": bool(randint(0, 1)),
            "is_solutions_architecture": bool(randint(0, 1)),
        }
        response = self.client.post(url, data=data, format="json")
        self.assertEqual(response.status_code, 201)

    def test_duplicate_user_post_view(self):
        person: Person = PersonFactory()

        self.client.force_login(self.admin_user)
        url = reverse("api_person_list_create")
        data = {
            "first_name": fuzzy.FuzzyText().fuzz(),
            "last_name": fuzzy.FuzzyText().fuzz(),
            "email": person.email,
        }
        response = self.client.post(url, data=data, format="json")
        self.assertEqual(response.status_code, 400)
