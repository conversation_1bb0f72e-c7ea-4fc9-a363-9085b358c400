from datetime import date, datetime, timedelta
from random import randint

from factory import fuzzy

from apps.utils.calendar import month_year_iter

from ..forms import ProjectForm
from ..models import Project, ProjectExecutiveAction, ProjectHealth
from .factories import (
    BusinessSegmentFactory,
    DivisionFactory,
    PersonFactory,
    StrategicPillarFactory,
    choice_values,
)


def generate_project_data():
    primary_division = DivisionFactory()
    other_involved_divisions = [DivisionFactory() for i in range(2)]
    strategic_pillars = [StrategicPillarFactory() for i in range(2)]
    people = [PersonFactory() for i in range(3)]

    data = {
        "name": fuzzy.FuzzyText().fuzz(),
        "project_rigor": fuzzy.FuzzyChoice(
            choice_values(Project.PROJECT_RIGOR_CHOICES)
        ).fuzz(),
        "primary_division": primary_division.pk,
        "other_involved_divisions": [m.pk for m in other_involved_divisions],
        "strategic_pillars": [m.pk for m in strategic_pillars],
        "summary": fuzzy.FuzzyText().fuzz(),
        "business_case": fuzzy.FuzzyText().fuzz(),
        "executive_owners": [PersonFactory().pk],
        "business_leads": [m.pk for m in people],
        "business_analysts": [m.pk for m in people],
        "other_stakeholders": [m.pk for m in people],
        "project_managers": [PersonFactory().pk],
        "internal_savings_initiative": bool(randint(0, 1)),
        "committed_to_spend": bool(randint(0, 1)),
        "funding_size": fuzzy.FuzzyChoice(
            choice_values(Project.MONEY_AMOUNT_CHOICES)
        ).fuzz(),
        "annualized_savings": fuzzy.FuzzyChoice(
            choice_values(Project.MONEY_AMOUNT_CHOICES)
        ).fuzz(),
        "capital_budget": fuzzy.FuzzyChoice(
            choice_values(Project.MONEY_AMOUNT_CHOICES)
        ).fuzz(),
        "expense_budget": fuzzy.FuzzyChoice(
            choice_values(Project.MONEY_AMOUNT_CHOICES)
        ).fuzz(),
        "funding_source": fuzzy.FuzzyChoice(
            choice_values(Project.FUNDING_SOURCE_CHOICES)
        ).fuzz(),
        "payback_period": fuzzy.FuzzyChoice(
            choice_values(Project.PAYBACK_PERIOD_CHOICES)
        ).fuzz(),
        "capital_expenditure": bool(randint(0, 1)),
        "car_number": fuzzy.FuzzyText().fuzz(),
        "expense_io_number": fuzzy.FuzzyInteger(100_000, 999_999).fuzz(),
        "capital_io_number": fuzzy.FuzzyInteger(100_000, 999_999).fuzz(),
        "opex_expenditure": bool(randint(0, 1)),
        "company_code": fuzzy.FuzzyChoice(
            choice_values(Project.COMPANY_CODE_CHOICES)
        ).fuzz(),
        "cost_center": fuzzy.FuzzyText().fuzz(),
        "gl_account": fuzzy.FuzzyText().fuzz(),
        "expected_duration": fuzzy.FuzzyChoice(
            choice_values(Project.DURATION_CHOICES)
        ).fuzz(),
        "start_date": fuzzy.FuzzyDate(
            date.today() - timedelta(days=90), date.today()
        ).fuzz(),
        "end_date": fuzzy.FuzzyDate(
            date.today(), date.today() + timedelta(days=120)
        ).fuzz(),
        "savings_start_date": fuzzy.FuzzyDate(
            date.today() + timedelta(days=150), date.today() + timedelta(days=300)
        ).fuzz(),
        "savings_end_date": fuzzy.FuzzyDate(
            date.today() + timedelta(days=600), date.today() + timedelta(days=900)
        ).fuzz(),
        "has_technology_components": bool(randint(0, 1)),
        "technology_components": fuzzy.FuzzyText().fuzz(),
        "sap_impact": bool(randint(0, 1)),
        "priority": fuzzy.FuzzyChoice(choice_values(Project.PRIORITY_CHOICES)).fuzz(),
        "complexity": fuzzy.FuzzyChoice(
            choice_values(Project.COMPLEXITY_CHOICES)
        ).fuzz(),
        "project_state": fuzzy.FuzzyChoice(
            choice_values(ProjectForm.PROJECT_STATE_CHOICES)
        ).fuzz(),
        "phase": fuzzy.FuzzyChoice(choice_values(ProjectForm.PHASE_CHOICES)).fuzz(),
        "budget_health": fuzzy.FuzzyChoice(
            choice_values(ProjectHealth.HEALTH.choices)
        ).fuzz(),
        "schedule_health": fuzzy.FuzzyChoice(
            choice_values(ProjectHealth.HEALTH.choices)
        ).fuzz(),
        "scope_health": fuzzy.FuzzyChoice(
            choice_values(ProjectHealth.HEALTH.choices)
        ).fuzz(),
        "get_to_green": fuzzy.FuzzyText().fuzz(),
        "percent_complete": fuzzy.FuzzyInteger(0, 100).fuzz(),
    }

    num_recent_accomplishments_forms = 4
    data.update(
        {
            "projectrecentaccomplishment_set-TOTAL_FORMS": num_recent_accomplishments_forms,
            "projectrecentaccomplishment_set-INITIAL_FORMS": 0,
            "projectrecentaccomplishment_set-MIN_NUM_FORMS": 0,
            "projectrecentaccomplishment_set-MAX_NUM_FORMS": 1000,
        }
    )
    for i in range(num_recent_accomplishments_forms):
        data.update(
            {
                f"projectrecentaccomplishment_set-{i}-id": "",
                f"projectrecentaccomplishment_set-{i}-project": "",
                f"projectrecentaccomplishment_set-{i}-text": fuzzy.FuzzyText().fuzz(),
                f"projectrecentaccomplishment_set-{i}-weight": fuzzy.FuzzyInteger(
                    -10, 10
                ).fuzz(),
                f"projectrecentaccomplishment_set-{i}-DELETE": False,
            }
        )

    num_planned_activities_forms = 4
    data.update(
        {
            "projectplannedactivity_set-TOTAL_FORMS": num_planned_activities_forms,
            "projectplannedactivity_set-INITIAL_FORMS": 0,
            "projectplannedactivity_set-MIN_NUM_FORMS": 0,
            "projectplannedactivity_set-MAX_NUM_FORMS": 1000,
        }
    )
    for i in range(num_planned_activities_forms):
        data.update(
            {
                f"projectplannedactivity_set-{i}-id": "",
                f"projectplannedactivity_set-{i}-project": "",
                f"projectplannedactivity_set-{i}-text": fuzzy.FuzzyText().fuzz(),
                f"projectplannedactivity_set-{i}-weight": fuzzy.FuzzyInteger(
                    -10, 10
                ).fuzz(),
                f"projectplannedactivity_set-{i}-DELETE": False,
            }
        )

    num_executive_actions_forms = 4
    data.update(
        {
            "projectexecutiveaction_set-TOTAL_FORMS": num_executive_actions_forms,
            "projectexecutiveaction_set-INITIAL_FORMS": 0,
            "projectexecutiveaction_set-MIN_NUM_FORMS": 0,
            "projectexecutiveaction_set-MAX_NUM_FORMS": 1000,
        }
    )
    for i in range(num_executive_actions_forms):
        data.update(
            {
                f"projectexecutiveaction_set-{i}-id": "",
                f"projectexecutiveaction_set-{i}-project": "",
                f"projectexecutiveaction_set-{i}-text": fuzzy.FuzzyText().fuzz(),
                f"projectexecutiveaction_set-{i}-action": fuzzy.FuzzyChoice(
                    choice_values(ProjectExecutiveAction.ACTION.choices)
                ).fuzz(),
                f"projectexecutiveaction_set-{i}-weight": fuzzy.FuzzyInteger(
                    -10, 10
                ).fuzz(),
                f"projectexecutiveaction_set-{i}-DELETE": False,
            }
        )

    num_link_forms = 2
    data.update(
        {
            "projectlink_set-TOTAL_FORMS": num_link_forms,
            "projectlink_set-INITIAL_FORMS": 0,
            "projectlink_set-MIN_NUM_FORMS": 0,
            "projectlink_set-MAX_NUM_FORMS": 1000,
        }
    )
    for i in range(num_link_forms):
        data.update(
            {
                f"projectlink_set-{i}-id": "",
                f"projectlink_set-{i}-project": "",
                f"projectlink_set-{i}-name": fuzzy.FuzzyText().fuzz(),
                f"projectlink_set-{i}-url": fuzzy.FuzzyText(
                    prefix="http://", suffix=".com"
                ).fuzz(),
                f"projectlink_set-{i}-DELETE": False,
            }
        )

    data.update(
        {
            "projectattachment_set-TOTAL_FORMS": 0,
            "projectattachment_set-INITIAL_FORMS": 0,
            "projectattachment_set-MIN_NUM_FORMS": 0,
            "projectattachment_set-MAX_NUM_FORMS": 1000,
        }
    )

    return data


def generate_project_financial_data(project: Project):
    data = {}

    start_date = project.start_date
    end_date = project.end_date
    for i, (year, month) in enumerate(
        month_year_iter(
            start_date.month, start_date.year, end_date.month, end_date.year
        )
    ):
        data.update(
            {
                f"projectcapitalexpenditure_set-{i}-id": "",
                f"projectcapitalexpenditure_set-{i}-project": project.pk,
                f"projectcapitalexpenditure_set-{i}-forecast": fuzzy.FuzzyInteger(
                    0, 1_000_000
                ).fuzz(),
                f"projectcapitalexpenditure_set-{i}-actuals": fuzzy.FuzzyInteger(
                    0, 1_000_000
                ).fuzz(),
            }
        )
    data.update(
        {
            "projectcapitalexpenditure_set-TOTAL_FORMS": i,
            "projectcapitalexpenditure_set-INITIAL_FORMS": 0,
            "projectcapitalexpenditure_set-MIN_NUM_FORMS": 0,
            "projectcapitalexpenditure_set-MAX_NUM_FORMS": 1000,
        }
    )

    start_date = project.start_date
    end_date = project.end_date
    for i, (year, month) in enumerate(
        month_year_iter(
            start_date.month, start_date.year, end_date.month, end_date.year
        )
    ):
        data.update(
            {
                f"projectoperationalexpenditure_set-{i}-id": "",
                f"projectoperationalexpenditure_set-{i}-project": project.pk,
                f"projectoperationalexpenditure_set-{i}-forecast": fuzzy.FuzzyInteger(
                    0, 1_000_000
                ).fuzz(),
                f"projectoperationalexpenditure_set-{i}-actuals": fuzzy.FuzzyInteger(
                    0, 1_000_000
                ).fuzz(),
            }
        )
    data.update(
        {
            "projectoperationalexpenditure_set-TOTAL_FORMS": i,
            "projectoperationalexpenditure_set-INITIAL_FORMS": 0,
            "projectoperationalexpenditure_set-MIN_NUM_FORMS": 0,
            "projectoperationalexpenditure_set-MAX_NUM_FORMS": 1000,
        }
    )

    return data


def generate_project_savings_data(project: Project):
    data = {"annual_savings_target": fuzzy.FuzzyInteger(1000, 1_000_000).fuzz()}

    start_date = project.savings_start_date
    end_date = project.savings_end_date
    for i, (year, month) in enumerate(
        month_year_iter(
            start_date.month, start_date.year, end_date.month, end_date.year
        )
    ):
        data.update(
            {
                f"projectsavings_set-{i}-id": "",
                f"projectsavings_set-{i}-project": project.pk,
                f"projectsavings_set-{i}-savings": fuzzy.FuzzyInteger(
                    0, 1_000_000
                ).fuzz(),
            }
        )
    data.update(
        {
            "projectsavings_set-TOTAL_FORMS": i,
            "projectsavings_set-INITIAL_FORMS": 0,
            "projectsavings_set-MIN_NUM_FORMS": 0,
            "projectsavings_set-MAX_NUM_FORMS": 1000,
        }
    )

    return data


def generate_plan_data():
    return {
        "form-TOTAL_FORMS": 0,
        "form-INITIAL_FORMS": 0,
        "capital_budget": fuzzy.FuzzyInteger(low=0),
        "expense_budget": fuzzy.FuzzyInteger(low=0),
        "form-0-start_date": fuzzy.FuzzyDate(
            start_date=datetime(2020, 10, 8), end_date=datetime(2020, 10, 10)
        ),
        "form-0-end_date": fuzzy.FuzzyDate(
            start_date=datetime(2020, 11, 8), end_date=datetime(2020, 11, 9)
        ),
        "form-1-start_date": fuzzy.FuzzyDate(
            start_date=datetime(2020, 10, 8), end_date=datetime(2020, 10, 10)
        ),
        "form-1-end_date": fuzzy.FuzzyDate(
            start_date=datetime(2020, 11, 8), end_date=datetime(2020, 11, 9)
        ),
        "form-2-start_date": fuzzy.FuzzyDate(
            start_date=datetime(2020, 10, 8), end_date=datetime(2020, 10, 10)
        ),
        "form-2-end_date": fuzzy.FuzzyDate(
            start_date=datetime(2020, 11, 8), end_date=datetime(2020, 11, 9)
        ),
        "form-3-start_date": fuzzy.FuzzyDate(
            start_date=datetime(2020, 10, 8), end_date=datetime(2020, 10, 10)
        ),
        "form-3-end_date": fuzzy.FuzzyDate(
            start_date=datetime(2020, 11, 8), end_date=datetime(2020, 11, 9)
        ),
        "form-4-start_date": fuzzy.FuzzyDate(
            start_date=datetime(2020, 10, 8), end_date=datetime(2020, 10, 10)
        ),
        "form-4-end_date": fuzzy.FuzzyDate(
            start_date=datetime(2020, 11, 8), end_date=datetime(2020, 11, 9)
        ),
    }


def generate_plan_project_data():
    strategic_pillars = [StrategicPillarFactory() for i in range(2)]
    business_segment = BusinessSegmentFactory()
    return {
        "name": fuzzy.FuzzyText(),
        "business_segment": business_segment.pk,
        "location": 97,
        "strategic_pillars": [m.pk for m in strategic_pillars],
        "pods": 3,
        "is_infrastructure": False,
        "capital_budget_amount": fuzzy.FuzzyInteger(low=0),
        "expense_budget_amount": fuzzy.FuzzyInteger(low=0),
        "form-TOTAL_FORMS": 3,
        "form-INITIAL_FORMS": 0,
        "form-MIN_NUM_FORMS": 0,
        "form-MAX_NUM_FORMS": 3,
        "form-0-capital_amount": fuzzy.FuzzyInteger(low=0),
        "form-0-expense_amount": fuzzy.FuzzyInteger(low=0),
    }
