from datetime import date, timed<PERSON><PERSON>
from unittest.case import skip

import openpyxl
from django.utils import timezone
from freezegun import freeze_time

from apps.users.tests import UserFactory
from tests.base import BaseTestCase

from ..models import (
    Person,
    Project,
    ProjectCapitalExpenditure,
    ProjectHealth,
    ProjectOperationalExpenditure,
)
from .factories import ProjectFactory, StarredProjectFactory


def choice_dict(choice_list):
    choices = {}
    for elem in choice_list:
        choices[elem[1]] = elem[0]
    return choices


CURRENT_ENVIRONMENT_CHOICE_DICT = choice_dict(Project.CURRENT_ENVIRONMENT_CHOICES)
FAILURE_SEVERITY_CHOICE_DICT = choice_dict(Project.FAILURE_SEVERITY_CHOICES)
MONEY_AMOUNT_CHOICE_DICT = choice_dict(Project.MONEY_AMOUNT_CHOICES)
PAYBACK_PERIOD_CHOICE_DICT = choice_dict(Project.PAYBACK_PERIOD_CHOICES)
PRIORITY_CHOICE_DICT = choice_dict(Project.PRIORITY_CHOICES)
COMPLEXITY_CHOICE_DICT = choice_dict(Project.COMPLEXITY_CHOICES)
RESPONSE_TO_AUDIT_CHOICE_DICT = choice_dict(Project.RESPONSE_TO_AUDIT_CHOICES)


class ProjectModelTestCase(BaseTestCase):
    def test_model_str(self):
        project = ProjectFactory()
        self.assertTrue(str(project))

    def test_new_project_count(self):
        user = UserFactory()
        for i in range(5):
            ProjectFactory()
        with freeze_time(timezone.now() - timedelta(days=45)):
            for i in range(10):
                ProjectFactory()
        self.assertEqual(Project.objects.new_projects_count(user), 5)

    def test_new_project_change_positive(self):
        user = UserFactory()
        for i in range(10):
            ProjectFactory()
        with freeze_time(timezone.now() - timedelta(days=45)):
            for i in range(5):
                ProjectFactory()
        self.assertEqual(Project.objects.new_projects_change(user), 200)

    def test_new_project_change_negative(self):
        user = UserFactory()
        for i in range(5):
            ProjectFactory()
        with freeze_time(timezone.now() - timedelta(days=45)):
            for i in range(10):
                ProjectFactory()
        self.assertEqual(Project.objects.new_projects_change(user), -50)

    def test_overall_health_with_all_colors(self):
        project = ProjectFactory(project_state=Project.PROJECT_STATE_ACTIVE)
        project_health, created = ProjectHealth.objects.get_or_create(
            project=project,
            year=timezone.now().year,
            week=timezone.now().isocalendar()[1],
        )
        project_health.budget_health = ProjectHealth.HEALTH.GREEN
        project_health.schedule_health = ProjectHealth.HEALTH.YELLOW
        project_health.scope_health = ProjectHealth.HEALTH.RED
        self.assertEqual(project_health.overall_health, ProjectHealth.HEALTH.YELLOW)

    def test_overall_health_with_majority_green(self):
        project = ProjectFactory(project_state=Project.PROJECT_STATE_ACTIVE)
        project_health, created = ProjectHealth.objects.get_or_create(
            project=project,
            year=timezone.now().year,
            week=timezone.now().isocalendar()[1],
        )
        project_health.budget_health = ProjectHealth.HEALTH.GREEN
        project_health.schedule_health = ProjectHealth.HEALTH.YELLOW
        project_health.scope_health = ProjectHealth.HEALTH.GREEN
        self.assertEqual(project_health.overall_health, ProjectHealth.HEALTH.GREEN)

    def test_overall_health_with_all_red(self):
        project = ProjectFactory(project_state=Project.PROJECT_STATE_ACTIVE)
        project_health, created = ProjectHealth.objects.get_or_create(
            project=project,
            year=timezone.now().year,
            week=timezone.now().isocalendar()[1],
        )
        project_health.budget_health = ProjectHealth.HEALTH.RED
        project_health.schedule_health = ProjectHealth.HEALTH.RED
        project_health.scope_health = ProjectHealth.HEALTH.RED
        self.assertEqual(project_health.overall_health, ProjectHealth.HEALTH.RED)

    def test_health_default_values(self):
        project: Project = ProjectFactory(project_state=Project.PROJECT_STATE_ACTIVE)
        project_health, created = ProjectHealth.objects.get_or_create(
            project=project,
            year=timezone.now().year,
            week=timezone.now().isocalendar()[1],
        )
        project_health.budget_health = ProjectHealth.HEALTH.RED
        project_health.schedule_health = ""
        project_health.scope_health = ProjectHealth.HEALTH.RED
        project_health.save()
        project.set_health()
        project_health = ProjectHealth.objects.get(pk=project_health.pk)
        self.assertEqual(ProjectHealth.HEALTH.GREEN, project_health.schedule_health)

    def test_health_default_values_with_non_active_project(self):
        project: Project = ProjectFactory(project_state=Project.PROJECT_STATE_CANCELLED)
        project_health, created = ProjectHealth.objects.get_or_create(
            project=project,
            year=timezone.now().year,
            week=timezone.now().isocalendar()[1],
        )
        project_health.budget_health = ProjectHealth.HEALTH.RED
        project_health.schedule_health = ProjectHealth.HEALTH.GREEN
        project_health.scope_health = ProjectHealth.HEALTH.RED
        project_health.save()
        project.set_health()
        project_health = ProjectHealth.objects.get(pk=project_health.pk)
        self.assertEqual(ProjectHealth.HEALTH.NA, project_health.budget_health)
        self.assertEqual(ProjectHealth.HEALTH.NA, project_health.schedule_health)
        self.assertEqual(ProjectHealth.HEALTH.NA, project_health.scope_health)

    def test_green_project_count(self):
        user = UserFactory()
        for i in range(10):
            project = ProjectFactory(project_state=Project.PROJECT_STATE_ACTIVE)
            project_health, created = ProjectHealth.objects.get_or_create(
                project=project,
                year=timezone.now().year,
                week=timezone.now().isocalendar()[1],
            )
            project_health.budget_health = ProjectHealth.HEALTH.GREEN
            project_health.schedule_health = ProjectHealth.HEALTH.GREEN
            project_health.scope_health = ProjectHealth.HEALTH.GREEN
            project_health.save()
        self.assertEqual(
            Project.objects.health_projects_count(user, ProjectHealth.HEALTH.GREEN), 10
        )

    def test_yellow_project_count(self):
        user = UserFactory()
        for i in range(10):
            project = ProjectFactory(project_state=Project.PROJECT_STATE_ACTIVE)
            project_health, created = ProjectHealth.objects.get_or_create(
                project=project,
                year=timezone.now().year,
                week=timezone.now().isocalendar()[1],
            )
            health = (
                ProjectHealth.HEALTH.YELLOW if i % 2 else ProjectHealth.HEALTH.GREEN
            )
            project_health.budget_health = health
            project_health.schedule_health = health
            project_health.scope_health = health
            project_health.save()
        self.assertEqual(
            Project.objects.health_projects_count(user, ProjectHealth.HEALTH.YELLOW), 5
        )

    def test_red_project_count(self):
        user = UserFactory()
        for i in range(10):
            project = ProjectFactory(project_state=Project.PROJECT_STATE_ACTIVE)
            project_health, created = ProjectHealth.objects.get_or_create(
                project=project,
                year=timezone.now().year,
                week=timezone.now().isocalendar()[1],
            )
            health = ProjectHealth.HEALTH.RED if i % 5 else ProjectHealth.HEALTH.GREEN
            project_health.budget_health = health
            project_health.schedule_health = health
            project_health.scope_health = health
            project_health.save()
        self.assertEqual(
            Project.objects.health_projects_count(user, ProjectHealth.HEALTH.RED), 8
        )

    def test_green_project_percent(self):
        user = UserFactory()
        for i in range(10):
            project = ProjectFactory(project_state=Project.PROJECT_STATE_ACTIVE)
            project_health, created = ProjectHealth.objects.get_or_create(
                project=project,
                year=timezone.now().year,
                week=timezone.now().isocalendar()[1],
            )
            health = (
                ProjectHealth.HEALTH.GREEN
                if i in [0, 1, 2]
                else (
                    ProjectHealth.HEALTH.RED
                    if i in [3, 4, 5, 6]
                    else ProjectHealth.HEALTH.YELLOW
                )
            )
            project_health.budget_health = health
            project_health.schedule_health = health
            project_health.scope_health = health
            project_health.save()
        self.assertEqual(
            Project.objects.health_projects_percent(user, ProjectHealth.HEALTH.GREEN),
            30,
        )

    def test_red_project_percent(self):
        user = UserFactory()
        for i in range(10):
            project = ProjectFactory(project_state=Project.PROJECT_STATE_ACTIVE)
            project_health, created = ProjectHealth.objects.get_or_create(
                project=project,
                year=timezone.now().year,
                week=timezone.now().isocalendar()[1],
            )
            project_health.health = ProjectHealth.HEALTH.YELLOW
            project_health.save()
        self.assertEqual(
            Project.objects.health_projects_percent(user, ProjectHealth.HEALTH.RED), 0
        )

    def test_not_overdue(self):
        project = ProjectFactory(
            project_state=Project.PROJECT_STATE_ACTIVE,
            end_date=date.today() + timedelta(days=100),
        )
        self.assertFalse(project.overdue)

    def test_not_overdue_project_state(self):
        project = ProjectFactory(
            project_state=Project.PROJECT_STATE_COMPLETE,
            end_date=date.today() - timedelta(days=100),
        )
        self.assertFalse(project.overdue)

    def test_overdue_modified(self):
        project = ProjectFactory(
            project_state=Project.PROJECT_STATE_ACTIVE,
            end_date=date.today() - timedelta(days=2),
        )
        self.assertTrue(project.overdue)

    def test_starred_project(self):
        project = ProjectFactory()
        user = UserFactory()
        StarredProjectFactory(project=project, starred_by=user)
        self.assertTrue(project.is_starred_by_user(user))

    def test_not_starred_project(self):
        project = ProjectFactory()
        user = UserFactory()
        StarredProjectFactory(project=project)
        self.assertFalse(project.is_starred_by_user(user))

    def test_with_starred_by_user(self):
        user = UserFactory()
        projects = [ProjectFactory() for i in range(10)]
        StarredProjectFactory(project=projects[1], starred_by=user)
        StarredProjectFactory(project=projects[5], starred_by=user)
        StarredProjectFactory(project=projects[8], starred_by=user)
        starred_projects = Project.objects.with_starred_by_user(user).filter(
            starred=True
        )
        self.assertEqual(starred_projects.count(), 3)

    def test_clear_technology_components_field(self):
        project = ProjectFactory(
            has_technology_components=True, technology_components="Lorem ipsum"
        )
        project.has_technology_components = False
        project.save()
        project = Project.objects.get(pk=project.pk)
        self.assertFalse(project.technology_components)

    def test_clear_capital_related_fields(self):
        project = ProjectFactory(
            capital_expenditure=True,
            car_number="123456",
            expense_io_number="123456",
            capital_io_number="123456",
        )
        project.capital_expenditure = False
        project.save()
        project = Project.objects.get(pk=project.pk)
        self.assertFalse(project.car_number)
        self.assertFalse(project.expense_io_number)
        self.assertFalse(project.capital_io_number)

    def test_clear_opex_related_fields(self):
        project = ProjectFactory(
            opex_expenditure=True,
            company_code="123456",
            cost_center="Packaging",
            gl_account="123456",
        )
        project.opex_expenditure = False
        project.save()
        project = Project.objects.get(pk=project.pk)
        self.assertFalse(project.company_code)
        self.assertFalse(project.cost_center)
        self.assertFalse(project.gl_account)

    @skip("Changed the requirement, do not automatically set savings start date.")
    def test_set_savings_start_date(self):
        project = ProjectFactory(end_date=date(2015, 3, 15))
        self.assertEqual(project.savings_start_date, date(2015, 4, 1))

    @skip("Changed the requirement, do not automatically set savings end date.")
    def test_set_savings_end_date(self):
        project = ProjectFactory(savings_start_date=date(2015, 3, 15))
        self.assertEqual(project.savings_end_date, date(2016, 3, 1))

    def test_set_tollgate_dates(self):
        project = ProjectFactory(project_rigor=Project.PROJECT_RIGOR_SWE)
        self.assertFalse(project.has_tollgate_dates())

    def test_with_expenditures(self):
        project = ProjectFactory(
            project_state=Project.PROJECT_STATE_ACTIVE,
            start_date=date(2018, 1, 1),
            end_date=date(2018, 4, 1),
        )
        ProjectCapitalExpenditure.objects.create(
            project=project, year=2018, month=1, forecast=1000, actuals=800
        )
        ProjectCapitalExpenditure.objects.create(
            project=project, year=2018, month=2, forecast=1200, actuals=900
        )
        ProjectCapitalExpenditure.objects.create(
            project=project, year=2018, month=3, forecast=1100, actuals=1100
        )
        ProjectCapitalExpenditure.objects.create(
            project=project, year=2018, month=4, forecast=1100, actuals=1300
        )
        ProjectOperationalExpenditure.objects.create(
            project=project, year=2018, month=1, forecast=800, actuals=800
        )
        ProjectOperationalExpenditure.objects.create(
            project=project, year=2018, month=2, forecast=600, actuals=800
        )
        ProjectOperationalExpenditure.objects.create(
            project=project, year=2018, month=3, forecast=800, actuals=500
        )
        ProjectOperationalExpenditure.objects.create(
            project=project, year=2018, month=4, forecast=700, actuals=600
        )
        project = Project.objects.with_expenditures().get(pk=project.pk)
        # project.set_capex_expenditure()
        # project.set_opex_expenditure()
        self.assertEqual(4400, project.capital_forecast_total)
        self.assertEqual(4100, project.capital_actuals_total)
        self.assertEqual(300, project.capital_forecast_remaining)
        self.assertEqual(2900, project.operational_forecast_total)
        self.assertEqual(2700, project.operational_actuals_total)
        self.assertEqual(200, project.operational_forecast_remaining)
        self.assertTrue(project.opex_expenditure)
        self.assertTrue(project.capital_expenditure)

    def test_strategic_value_query_calculation(self):
        wb = openpyxl.load_workbook(
            filename="apps/projects/tests/files/strategic_value_test_data.xlsx"
        )
        ws = wb["data"]
        for row in ws.iter_rows(min_row=2):
            funding_size = row[4].value
            annualized_savings = row[5].value
            payback_period = row[6].value
            priority = row[7].value
            complexity = row[8].value
            current_environment = row[9].value
            failure_severity = row[10].value
            response_to_audit = row[11].value
            project = ProjectFactory(
                funding_size=MONEY_AMOUNT_CHOICE_DICT.get(funding_size, None),
                annualized_savings=MONEY_AMOUNT_CHOICE_DICT.get(
                    annualized_savings, None
                ),
                payback_period=PAYBACK_PERIOD_CHOICE_DICT.get(payback_period, None),
                priority=PRIORITY_CHOICE_DICT.get(priority, None),
                complexity=COMPLEXITY_CHOICE_DICT.get(complexity, None),
                current_environment=CURRENT_ENVIRONMENT_CHOICE_DICT.get(
                    current_environment, None
                ),
                failure_severity=FAILURE_SEVERITY_CHOICE_DICT.get(
                    failure_severity, None
                ),
                response_to_audit=RESPONSE_TO_AUDIT_CHOICE_DICT.get(
                    response_to_audit, None
                ),
            )
            project = Project.objects.with_strategic_value().get(pk=project.pk)
            strategic_value = project.strategic_value
            self.assertEqual(strategic_value, row[24].value)

    def test_duplicate(self):
        project = ProjectFactory()
        duplicate_project = project.duplicate()
        self.assertEqual(duplicate_project.name, f"COPY - {project.name}")
