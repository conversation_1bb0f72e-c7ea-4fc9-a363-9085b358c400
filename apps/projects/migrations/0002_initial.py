# Generated by Django 5.2.4 on 2025-10-28 00:03

import auto_prefetch
import django.db.models.deletion
import taggit.managers
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("locations", "0003_initial"),
        ("organizations", "0002_initial"),
        ("programs", "0002_initial"),
        ("projects", "0001_initial"),
        (
            "taggit",
            "0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="businesssegmentperson",
            name="user",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="division",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="divisions",
                to="organizations.company",
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="business_analysts",
            field=models.ManyToManyField(
                blank=True,
                related_name="business_analysts",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="business_leads",
            field=models.ManyToManyField(
                blank=True, related_name="business_leads", to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="business_segment",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="projects.businesssegment",
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="company",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="organizations.company",
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="coos",
            field=models.ManyToManyField(
                blank=True, related_name="coos", to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="created_by",
            field=models.ForeignKey(
                editable=False,
                on_delete=django.db.models.deletion.PROTECT,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="engineer_directors",
            field=models.ManyToManyField(
                blank=True,
                related_name="engineer_directors",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="executive_owners",
            field=models.ManyToManyField(
                blank=True, related_name="executive_owners", to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="finance_leads",
            field=models.ManyToManyField(
                blank=True, related_name="finance_leads", to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="location",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="locations.location",
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="location_engineers",
            field=models.ManyToManyField(
                blank=True,
                related_name="location_engineers",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="location_managers",
            field=models.ManyToManyField(
                blank=True,
                related_name="location_managers",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="operations_directors",
            field=models.ManyToManyField(
                blank=True,
                related_name="operations_directors",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="other_involved_divisions",
            field=models.ManyToManyField(
                blank=True, related_name="other_divisions", to="projects.division"
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="other_stakeholders",
            field=models.ManyToManyField(
                blank=True,
                related_name="other_stakeholders",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="pods",
            field=models.ManyToManyField(blank=True, to="projects.pod"),
        ),
        migrations.AddField(
            model_name="project",
            name="primary_division",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="projects.division",
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="program",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="programs.program",
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="project_managers",
            field=models.ManyToManyField(
                blank=True, related_name="project_managers", to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="svps",
            field=models.ManyToManyField(
                blank=True, related_name="svps", to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="tags",
            field=taggit.managers.TaggableManager(
                blank=True,
                help_text="A comma-separated list of tags.",
                through="taggit.TaggedItem",
                to="taggit.Tag",
                verbose_name="Tags",
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="vps",
            field=models.ManyToManyField(
                blank=True, related_name="vps", to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="outofscope",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="measureofsuccess",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="inscope",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectattachment",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectcapitalexpenditure",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectdivisionimpact",
            name="division",
            field=auto_prefetch.ForeignKey(
                limit_choices_to={
                    "name__in": [
                        "Emerging Business",
                        "Engineering",
                        "Fresh",
                        "Growth & Research",
                        "Hog Production",
                        "Innovation",
                        "International",
                        "Information Technology & Transformation",
                        "Marketing",
                        "Operations",
                        "Packaged",
                        "Supply Chain",
                    ]
                },
                on_delete=django.db.models.deletion.CASCADE,
                to="projects.division",
            ),
        ),
        migrations.AddField(
            model_name="projectdivisionimpact",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectexecutiveaction",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projecthealth",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectimportdata",
            name="project",
            field=auto_prefetch.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="projects.project",
            ),
        ),
        migrations.AddField(
            model_name="projectlink",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectoperationalexpenditure",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectoperationsneed",
            name="project",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectpercentcomplete",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectplannedactivity",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectrecentaccomplishment",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectrisk",
            name="project",
            field=auto_prefetch.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectsavings",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="projectsupportimpact",
            name="project",
            field=auto_prefetch.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="project_types",
            field=models.ManyToManyField(blank=True, to="projects.projecttype"),
        ),
        migrations.AddField(
            model_name="raidassumption",
            name="identifier",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="assumption_identifier",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="raidassumption",
            name="owner",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="assumption_owner",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="raidassumption",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="raidassumption",
            name="validated_by",
            field=auto_prefetch.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="validated_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="raiddependencies",
            name="dependency_owner",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="dependency_owner",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="raiddependencies",
            name="identifier",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="dependency_identifier",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="raiddependencies",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="raidissues",
            name="identifier",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="issue_identifier",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="raidissues",
            name="issue_owner",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="issue_owner",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="raidissues",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="raidreport",
            name="assumption",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="projects.raidassumption",
            ),
        ),
        migrations.AddField(
            model_name="raidreport",
            name="dependency",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="projects.raiddependencies",
            ),
        ),
        migrations.AddField(
            model_name="raidreport",
            name="issue",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="projects.raidissues",
            ),
        ),
        migrations.AddField(
            model_name="raidreport",
            name="owner",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="owner",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="raidreport",
            name="project_name",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="project_name",
                to="projects.project",
            ),
        ),
        migrations.AddField(
            model_name="raidreport",
            name="project_number",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="project_number",
                to="projects.project",
            ),
        ),
        migrations.AddField(
            model_name="raidrisk",
            name="identifier",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="risk_identifier",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="raidrisk",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="raidrisk",
            name="risk_owner",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="risk_owner",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="raidreport",
            name="risk",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="projects.raidrisk",
            ),
        ),
        migrations.AddField(
            model_name="starredproject",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="starredproject",
            name="starred_by",
            field=auto_prefetch.ForeignKey(
                editable=False,
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="strategic_pillars",
            field=models.ManyToManyField(to="projects.strategicpillar"),
        ),
        migrations.AddField(
            model_name="subpillar",
            name="pillar",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="projects.strategicpillar",
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="strategic_subpillars",
            field=models.ManyToManyField(blank=True, to="projects.subpillar"),
        ),
        migrations.AlterUniqueTogether(
            name="businesssegment",
            unique_together={("company", "name")},
        ),
        migrations.AlterUniqueTogether(
            name="projectcapitalexpenditure",
            unique_together={("project", "year", "month")},
        ),
        migrations.AlterUniqueTogether(
            name="projectdivisionimpact",
            unique_together={("division", "project")},
        ),
        migrations.AlterUniqueTogether(
            name="projecthealth",
            unique_together={("project", "year", "week")},
        ),
        migrations.AlterUniqueTogether(
            name="projectoperationalexpenditure",
            unique_together={("project", "year", "month")},
        ),
        migrations.AlterUniqueTogether(
            name="projectpercentcomplete",
            unique_together={("project", "year", "week")},
        ),
        migrations.AlterUniqueTogether(
            name="projectsavings",
            unique_together={("project", "year", "month")},
        ),
        migrations.AlterUniqueTogether(
            name="starredproject",
            unique_together={("project", "starred_by")},
        ),
    ]
