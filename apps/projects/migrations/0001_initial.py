# Generated by Django 5.2.4 on 2025-10-28 00:03

import auto_prefetch
import django.core.validators
import django.db.models.deletion
import django.db.models.manager
import django.utils.timezone
import django_lifecycle.mixins
import jsonfield.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("organizations", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Division",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("abbr", models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="InScope",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("text", models.TextField(max_length=5000)),
                ("add_to_slide", models.BooleanField(default=True)),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
                ("active", models.BooleanField(db_index=True, default=True)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="MeasureOfSuccess",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("text", models.TextField(max_length=5000)),
                ("add_to_slide", models.BooleanField(default=True)),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
                ("active", models.BooleanField(db_index=True, default=True)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="OutOfScope",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("text", models.TextField(max_length=5000)),
                ("add_to_slide", models.BooleanField(default=True)),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
                ("active", models.BooleanField(db_index=True, default=True)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="Pod",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Project",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("private", models.BooleanField(blank=True, default=False)),
                ("ready_to_begin", models.BooleanField(blank=True, default=False)),
                ("get_to_green", models.TextField(blank=True, null=True)),
                ("idea_id", models.CharField(blank=True, max_length=50, null=True)),
                ("name", models.CharField(max_length=255)),
                (
                    "project_rigor",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("small work effort", "Small Work Effort"),
                            ("standard", "Standard Rigor"),
                            ("high", "High Rigor"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "summary",
                    models.TextField(
                        help_text="What will be delivered by the project?",
                        max_length=5000,
                    ),
                ),
                (
                    "business_case",
                    models.TextField(
                        blank=True,
                        help_text="Describe the justification and expected benefit for this initiative.",
                        max_length=5000,
                        null=True,
                    ),
                ),
                (
                    "internal_savings_initiative",
                    models.BooleanField(
                        blank=True, choices=[(False, "No"), (True, "Yes")], null=True
                    ),
                ),
                (
                    "committed_to_spend",
                    models.BooleanField(
                        blank=True, choices=[(False, "No"), (True, "Yes")], null=True
                    ),
                ),
                (
                    "funding_size",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("none", "None"),
                            ("less than 25k", "Less than 25k"),
                            ("25k to 250k", "25k to 250k"),
                            ("250k to 1m", "250k to 1M"),
                            ("1m to 5m", "1M to 5M"),
                            ("greater than 5m", "Greater than 5M"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "annualized_savings",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("none", "None"),
                            ("less than 25k", "Less than 25k"),
                            ("25k to 250k", "25k to 250k"),
                            ("250k to 1m", "250k to 1M"),
                            ("1m to 5m", "1M to 5M"),
                            ("greater than 5m", "Greater than 5M"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                ("profitability_index", models.IntegerField(blank=True, null=True)),
                (
                    "capital_budget",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("none", "None"),
                            ("less than 25k", "Less than 25k"),
                            ("25k to 250k", "25k to 250k"),
                            ("250k to 1m", "250k to 1M"),
                            ("1m to 5m", "1M to 5M"),
                            ("greater than 5m", "Greater than 5M"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "expense_budget",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("none", "None"),
                            ("less than 25k", "Less than 25k"),
                            ("25k to 250k", "25k to 250k"),
                            ("250k to 1m", "250k to 1M"),
                            ("1m to 5m", "1M to 5M"),
                            ("greater than 5m", "Greater than 5M"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "funding_source",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("it&t", "IT&T"),
                            ("business", "Business"),
                            ("to be determined", "To Be Determined"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "payback_period",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("None", "None"),
                            ("<1 year", "<1 Year"),
                            ("1-2 years", "1-2 Years"),
                            ("2-3 years", "2-3 Years"),
                            ("3+ years", "3+ Years"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "capital_expenditure",
                    models.BooleanField(
                        blank=True, choices=[(False, "No"), (True, "Yes")], null=True
                    ),
                ),
                (
                    "capital_budget_amount",
                    models.BigIntegerField(blank=True, default=0, null=True),
                ),
                (
                    "expense_budget_amount",
                    models.BigIntegerField(blank=True, default=0, null=True),
                ),
                (
                    "annual_savings_target",
                    models.BigIntegerField(blank=True, null=True),
                ),
                (
                    "capital_forecast",
                    models.BigIntegerField(blank=True, default=0, null=True),
                ),
                (
                    "capital_actuals",
                    models.BigIntegerField(blank=True, default=0, null=True),
                ),
                ("car_number", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "expense_io_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "capital_io_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "opex_expenditure",
                    models.BooleanField(
                        blank=True, choices=[(False, "No"), (True, "Yes")], null=True
                    ),
                ),
                (
                    "opex_forecast",
                    models.BigIntegerField(blank=True, default=0, null=True),
                ),
                (
                    "opex_actuals",
                    models.BigIntegerField(blank=True, default=0, null=True),
                ),
                (
                    "company_code",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("2000", "2000 - Pork Group"),
                            ("2100", "2100 - Smithfield Direct, LLC"),
                            ("2200", "2200 - American Skin"),
                            ("3000", "3000 - Smithfield Foods"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "cost_center",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("gl_account", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "expected_duration",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("small work effort", "<1 Month"),
                            ("1-6 months", "1-6 Months"),
                            ("6-12 months", "6-12 Months"),
                            ("12+ months", "12+ Months"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                ("savings_start_date", models.DateField(blank=True, null=True)),
                ("savings_end_date", models.DateField(blank=True, null=True)),
                ("start_date", models.DateField(blank=True, null=True)),
                ("end_date", models.DateField(blank=True, null=True)),
                (
                    "is_infrastructure",
                    models.BooleanField(
                        blank=True, choices=[(False, "No"), (True, "Yes")], null=True
                    ),
                ),
                (
                    "has_technology_components",
                    models.BooleanField(
                        blank=True, choices=[(False, "No"), (True, "Yes")], null=True
                    ),
                ),
                (
                    "technology_components",
                    models.TextField(
                        blank=True,
                        help_text="What are the technical components?",
                        max_length=5000,
                        null=True,
                    ),
                ),
                (
                    "corporate_communication_involvement",
                    models.BooleanField(
                        blank=True, choices=[(False, "No"), (True, "Yes")], null=True
                    ),
                ),
                (
                    "corporate_communication_needs",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("high", "The majority of the company"),
                            (
                                "medium",
                                "The majority of employees in one or more of the following businesses: Hog Production, Fresh, Packaged, or International",
                            ),
                            ("low", "Specific departments or project teams"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "corporate_communication_needs_description",
                    models.TextField(blank=True, max_length=5000),
                ),
                (
                    "sap_impact",
                    models.BooleanField(
                        blank=True, choices=[(False, "No"), (True, "Yes")], null=True
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("regulatory", "Regulatory"),
                            ("high", "High"),
                            ("medium", "Medium"),
                            ("low", "Low"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "complexity",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("high", "High"),
                            ("medium", "Medium"),
                            ("low", "Low"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "project_state",
                    models.CharField(
                        choices=[
                            ("deferred", "Deferred"),
                            ("draft", "Draft"),
                            ("submitted", "Submitted"),
                            ("returned", "Returned"),
                            ("active", "Active"),
                            ("complete", "Complete"),
                            ("cancelled", "Cancelled"),
                            ("on hold", "On Hold"),
                            ("archived", "Archived"),
                        ],
                        default="active",
                        max_length=50,
                    ),
                ),
                (
                    "phase",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("idea", "Idea"),
                            ("propose", "Propose"),
                            ("initiation", "Initiation"),
                            ("planning", "Planning"),
                            ("execution", "Execution"),
                            ("control", "Close"),
                        ],
                        default="initiation",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "departments_involved",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("1", "1"),
                            ("2-3", "2-3"),
                            ("4-5", "4-5"),
                            ("6+", "6+"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "vendors_involved",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("0", "0"),
                            ("1-3", "1-3"),
                            ("4-5", "4-5"),
                            ("6+", "6+"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "associated_projects",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("none", "None"),
                            ("related_to", "Related Projects"),
                            ("dependent_on", "Dependent on other Projects"),
                            ("has_dependencies", "Dependencies to this Project"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "schedule_motivation",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("none", "None"),
                            ("resource_constraint", "Resource Constraint"),
                            (
                                "business_process_accommodation",
                                "Business Process Accommodation",
                            ),
                            ("regulatory_mandate", "Regulatory Mandate"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "similar_experience",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("high", "High"),
                            ("medium", "Medium"),
                            ("low", "Low"),
                            ("none", "None"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "infrastructure_readiness",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("high", "High"),
                            ("medium", "Medium"),
                            ("low", "Low"),
                            ("none", "None"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "external_user_privileges",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("none", "None"),
                            ("view", "View Only"),
                            ("download", "View and Access/Download Files"),
                            ("edit", "Create, Edit, & View Data"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "post_project_support",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("none", "None"),
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
                ("active", models.BooleanField(default=True)),
                (
                    "current_environment",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (-1, "None"),
                            (0, "Low"),
                            (1, "Moderate"),
                            (2, "High"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "failure_severity",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (-1, "None"),
                            (0, "Low"),
                            (1, "Moderate"),
                            (2, "High"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "response_to_audit",
                    models.IntegerField(
                        blank=True,
                        choices=[(0, "No"), (1, "Yes"), (2, "Violation")],
                        null=True,
                    ),
                ),
                (
                    "estimation_confidence",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (4, "Very High"),
                            (3, "High"),
                            (2, "Medium"),
                            (1, "Low"),
                            (0, "Very Low"),
                        ],
                        null=True,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
        ),
        migrations.CreateModel(
            name="ProjectAttachment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("file", models.FileField(max_length=500, upload_to="projects")),
                ("name", models.CharField(max_length=255)),
                (
                    "extension",
                    models.CharField(
                        choices=[
                            ("aac", "aac"),
                            ("ai", "ai"),
                            ("avi", "avi"),
                            ("bmp", "bmp"),
                            ("bpg", "bpg"),
                            ("csv", "csv"),
                            ("doc", "doc"),
                            ("docx", "docx"),
                            ("exif", "exif"),
                            ("flac", "flac"),
                            ("flv", "flv"),
                            ("gif", "gif"),
                            ("jpeg", "jpeg"),
                            ("jpg", "jpg"),
                            ("m4a", "m4a"),
                            ("m4v", "m4v"),
                            ("mkv", "mkv"),
                            ("mov", "mov"),
                            ("mp3", "mp3"),
                            ("mp4", "mp4"),
                            ("mpeg4", "mpeg4"),
                            ("pdf", "pdf"),
                            ("png", "png"),
                            ("potx", "potx"),
                            ("ppt", "ppt"),
                            ("pptm", "pptm"),
                            ("pptx", "pptx"),
                            ("psd", "psd"),
                            ("svg", "svg"),
                            ("tar", "tar"),
                            ("tif", "tif"),
                            ("tiff", "tiff"),
                            ("txt", "txt"),
                            ("vsd", "vsd"),
                            ("vsdx", "vsdx"),
                            ("wav", "wav"),
                            ("webm", "webm"),
                            ("webp", "webp"),
                            ("wma", "wma"),
                            ("wmv", "wmv"),
                            ("xls", "xls"),
                            ("xlsb", "xlsb"),
                            ("xlsm", "xlsm"),
                            ("xlsx", "xlsx"),
                            ("zip", "zip"),
                        ],
                        db_index=True,
                        editable=False,
                        max_length=10,
                    ),
                ),
                (
                    "media_type",
                    models.CharField(
                        choices=[
                            ("archive", "archive"),
                            ("audio", "audio"),
                            ("document", "document"),
                            ("image", "image"),
                            ("miscellaneous", "miscellaneous"),
                            ("presentation", "presentation"),
                            ("spreadsheet", "spreadsheet"),
                            ("video", "video"),
                        ],
                        db_index=True,
                        editable=False,
                        max_length=100,
                    ),
                ),
                ("size", models.PositiveIntegerField(editable=False)),
                (
                    "height",
                    models.PositiveIntegerField(blank=True, editable=False, null=True),
                ),
                (
                    "width",
                    models.PositiveIntegerField(blank=True, editable=False, null=True),
                ),
                (
                    "dpi",
                    models.PositiveIntegerField(blank=True, editable=False, null=True),
                ),
                ("checksum", models.CharField(max_length=32)),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["weight", "created"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectCapitalExpenditure",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("year", models.PositiveSmallIntegerField()),
                (
                    "month",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "January"),
                            (2, "February"),
                            (3, "March"),
                            (4, "April"),
                            (5, "May"),
                            (6, "June"),
                            (7, "July"),
                            (8, "August"),
                            (9, "September"),
                            (10, "October"),
                            (11, "November"),
                            (12, "December"),
                        ],
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                ("forecast", models.PositiveIntegerField(default=0)),
                ("actuals", models.PositiveIntegerField(default=0)),
            ],
            options={
                "ordering": ["year", "month"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectDivisionImpact",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("impact_none", models.BooleanField(default=False)),
                ("impact_increase_revenue", models.BooleanField(default=False)),
                (
                    "impact_decrease_operational_cost",
                    models.BooleanField(default=False),
                ),
                ("impact_increase_productivity", models.BooleanField(default=False)),
                ("impact_improve_quality", models.BooleanField(default=False)),
                ("impact_mitigate_risk", models.BooleanField(default=False)),
            ],
            options={
                "ordering": ["division__name"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectExecutiveAction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("text", models.TextField(max_length=5000)),
                ("add_to_slide", models.BooleanField(default=True)),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("decision needed", "Decision Needed"),
                            ("feedback requested", "Escalation/Blocker"),
                            ("informational only", "Informational Only"),
                        ],
                        max_length=100,
                    ),
                ),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
                ("active", models.BooleanField(db_index=True, default=True)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["weight", "created"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectHealth",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "week",
                    models.PositiveSmallIntegerField(
                        editable=False,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                ("year", models.PositiveSmallIntegerField(editable=False)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
                (
                    "budget_health",
                    models.CharField(
                        choices=[
                            ("green", "Green"),
                            ("yellow", "Yellow"),
                            ("red", "Red"),
                            ("not applicable", "N/A"),
                        ],
                        default="green",
                        max_length=50,
                    ),
                ),
                (
                    "schedule_health",
                    models.CharField(
                        choices=[
                            ("green", "Green"),
                            ("yellow", "Yellow"),
                            ("red", "Red"),
                            ("not applicable", "N/A"),
                        ],
                        default="green",
                        max_length=50,
                    ),
                ),
                (
                    "scope_health",
                    models.CharField(
                        choices=[
                            ("green", "Green"),
                            ("yellow", "Yellow"),
                            ("red", "Red"),
                            ("not applicable", "N/A"),
                        ],
                        default="green",
                        max_length=50,
                    ),
                ),
                ("calculate_overall_health", models.BooleanField(default=True)),
                (
                    "health",
                    models.CharField(
                        choices=[
                            ("green", "Green"),
                            ("yellow", "Yellow"),
                            ("red", "Red"),
                            ("not applicable", "N/A"),
                        ],
                        default="green",
                        max_length=50,
                    ),
                ),
            ],
            options={
                "ordering": ["-year", "-week"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectImportData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("data", jsonfield.fields.JSONField()),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectLink",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("url", models.URLField(max_length=1000)),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
                ("created", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "ordering": ["weight", "created"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectOperationalExpenditure",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("year", models.PositiveSmallIntegerField()),
                (
                    "month",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "January"),
                            (2, "February"),
                            (3, "March"),
                            (4, "April"),
                            (5, "May"),
                            (6, "June"),
                            (7, "July"),
                            (8, "August"),
                            (9, "September"),
                            (10, "October"),
                            (11, "November"),
                            (12, "December"),
                        ],
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                ("forecast", models.PositiveIntegerField(default=0)),
                ("actuals", models.PositiveIntegerField(default=0)),
            ],
            options={
                "ordering": ["year", "month"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectOperationsNeed",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "need_rating",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Low", "Low"),
                            ("Medium", "Medium"),
                            ("High", "High"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectPercentComplete",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "week",
                    models.PositiveSmallIntegerField(
                        editable=False,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                ("year", models.PositiveSmallIntegerField(editable=False)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
                (
                    "percentage",
                    models.PositiveSmallIntegerField(
                        default=0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
            ],
            options={
                "ordering": ["-year", "-week"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectPlannedActivity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("text", models.TextField(max_length=5000)),
                ("add_to_slide", models.BooleanField(default=True)),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
                ("active", models.BooleanField(db_index=True, default=True)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["weight", "created"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectRecentAccomplishment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("text", models.TextField(max_length=5000)),
                ("add_to_slide", models.BooleanField(default=True)),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
                ("active", models.BooleanField(db_index=True, default=True)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["weight", "created"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectRisk",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "technology_risk_rating",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Low", "Low"),
                            ("Medium", "Medium"),
                            ("High", "High"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "site_capacity_risk_rating",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Low", "Low"),
                            ("Medium", "Medium"),
                            ("High", "High"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "product_risk_rating",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Low", "Low"),
                            ("Medium", "Medium"),
                            ("High", "High"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "savings_risk_rating",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Low", "Low"),
                            ("Medium", "Medium"),
                            ("High", "High"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "project_complexity_risk_rating",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Low", "Low"),
                            ("Medium", "Medium"),
                            ("High", "High"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectSavings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("year", models.PositiveSmallIntegerField()),
                (
                    "month",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "January"),
                            (2, "February"),
                            (3, "March"),
                            (4, "April"),
                            (5, "May"),
                            (6, "June"),
                            (7, "July"),
                            (8, "August"),
                            (9, "September"),
                            (10, "October"),
                            (11, "November"),
                            (12, "December"),
                        ],
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                ("savings", models.IntegerField(default=0)),
            ],
            options={
                "ordering": ["year", "month"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="ProjectSupportImpact",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "impact",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("no impact", "No Impact"),
                            ("single team", "Single Team in a Dept"),
                            ("multiple teams", "Multiple Teams in a Dept"),
                            ("2 to 3 departments", "2-3 Depts"),
                            ("4 to 5 departments", "4-5 Depts"),
                            ("6+ departments", "6+ Depts"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ProjectType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="RaidAssumption",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "section",
                    models.CharField(
                        choices=[
                            ("schedule", "Schedule"),
                            ("scope", "Scope"),
                            ("budget", "Budget"),
                            ("staffing", "Staffing"),
                        ],
                        default="schedule",
                        max_length=200,
                    ),
                ),
                (
                    "date_identified",
                    models.DateField(default=django.utils.timezone.now),
                ),
                ("last_updated", models.DateField(auto_now=True)),
                ("assumption", models.TextField()),
                ("reason", models.TextField()),
                ("validation_action", models.TextField(blank=True, null=True)),
                ("impact_if_incorrect", models.TextField()),
                ("close_by", models.DateField(blank=True, null=True)),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                        ],
                        default="medium",
                        max_length=200,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("validated", "Validated"),
                            ("unvalidated", "Unvalidated"),
                        ],
                        default="unvalidated",
                        max_length=200,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="RaidDependencies",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "section",
                    models.CharField(
                        choices=[
                            ("schedule", "Schedule"),
                            ("scope", "Scope"),
                            ("budget", "Budget"),
                            ("staffing", "Staffing"),
                        ],
                        default="schedule",
                        max_length=200,
                    ),
                ),
                (
                    "date_identified",
                    models.DateField(default=django.utils.timezone.now),
                ),
                ("last_updated", models.DateField(auto_now=True)),
                (
                    "dependency_type",
                    models.CharField(
                        choices=[
                            ("related", "Related"),
                            ("upstream", "Upstream"),
                            ("downstream", "Downstream"),
                        ],
                        default="related",
                        max_length=200,
                    ),
                ),
                ("dependency", models.TextField()),
                ("impact", models.TextField(blank=True, null=True)),
                ("close_by", models.DateField(blank=True, null=True)),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                        ],
                        default="medium",
                        max_length=200,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("active", "Active"), ("closed", "Closed")],
                        default="active",
                        max_length=200,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="RaidIssues",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "section",
                    models.CharField(
                        choices=[
                            ("schedule", "Schedule"),
                            ("scope", "Scope"),
                            ("budget", "Budget"),
                            ("staffing", "Staffing"),
                        ],
                        default="schedule",
                        max_length=200,
                    ),
                ),
                (
                    "date_identified",
                    models.DateField(default=django.utils.timezone.now),
                ),
                ("last_updated", models.DateField(auto_now=True)),
                ("issue_description", models.TextField(null=True)),
                ("corrective_action", models.TextField(blank=True, null=True)),
                ("close_by", models.DateField(blank=True, null=True)),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                        ],
                        default="medium",
                        max_length=200,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("active", "Active"), ("closed", "Closed")],
                        default="active",
                        max_length=200,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="RaidReport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("type", models.CharField(max_length=120)),
                (
                    "section",
                    models.CharField(
                        choices=[
                            ("schedule", "Schedule"),
                            ("scope", "Scope"),
                            ("budget", "Budget"),
                            ("staffing", "Staffing"),
                        ],
                        default="schedule",
                        max_length=200,
                    ),
                ),
                ("description", models.TextField()),
                (
                    "rating_priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                        ],
                        default="low",
                        max_length=200,
                    ),
                ),
                ("impact_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("resolved", "Resolved"),
                            ("issue", "Issue"),
                            ("closed", "Closed"),
                            ("validated", "Validated"),
                            ("unvalidated", "Unvalidated"),
                        ],
                        default="active",
                        max_length=200,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="RaidRisk",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "section",
                    models.CharField(
                        choices=[
                            ("schedule", "Schedule"),
                            ("scope", "Scope"),
                            ("budget", "Budget"),
                            ("staffing", "Staffing"),
                        ],
                        default="schedule",
                        max_length=200,
                    ),
                ),
                (
                    "date_identified",
                    models.DateField(default=django.utils.timezone.now),
                ),
                ("last_updated", models.DateField(auto_now=True)),
                ("risk_description", models.TextField()),
                (
                    "likelihood",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                        ],
                        default="medium",
                        max_length=200,
                    ),
                ),
                (
                    "impact",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                        ],
                        default="medium",
                        max_length=200,
                    ),
                ),
                (
                    "risk_rating",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                        ],
                        default="low",
                        max_length=200,
                    ),
                ),
                ("impact_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("issue", "Issue"),
                            ("resolved", "Resolved"),
                        ],
                        default="active",
                        max_length=200,
                    ),
                ),
                (
                    "mitigation",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("accepted", "Accepted"),
                            ("deferred", "Deferred"),
                            ("replanned", "Replanned"),
                            ("transferred", "Transferred"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="StarredProject",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="StrategicPillar",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="SubPillar",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
            ],
            options={
                "ordering": ["name"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="BusinessSegment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=120)),
                (
                    "company",
                    auto_prefetch.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="business_segments",
                        to="organizations.company",
                    ),
                ),
            ],
            options={
                "verbose_name": "Business Segment",
                "verbose_name_plural": "Business Segments",
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="BusinessSegmentPerson",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "business_segment",
                    auto_prefetch.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="projects.businesssegment",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
    ]
