[{"model": "projects.division", "pk": 1, "fields": {"company": 1, "name": "Corporate Strategy", "abbr": "STRAT"}}, {"model": "projects.division", "pk": 2, "fields": {"company": 2, "name": "Product Design", "abbr": "PDES"}}, {"model": "projects.division", "pk": 3, "fields": {"company": 3, "name": "Research & Development", "abbr": "RND"}}, {"model": "projects.division", "pk": 4, "fields": {"company": 1, "name": "Operations Management", "abbr": "OPS"}}, {"model": "projects.division", "pk": 5, "fields": {"company": 2, "name": "Financial Planning", "abbr": "FIN"}}, {"model": "projects.division", "pk": 6, "fields": {"company": 3, "name": "Human Resources", "abbr": "HR"}}, {"model": "projects.division", "pk": 7, "fields": {"company": 1, "name": "Engineering", "abbr": "ENG"}}, {"model": "projects.division", "pk": 8, "fields": {"company": 2, "name": "Customer Success", "abbr": "CS"}}, {"model": "projects.division", "pk": 9, "fields": {"company": 3, "name": "Data Science", "abbr": "DATA"}}, {"model": "projects.division", "pk": 10, "fields": {"company": 1, "name": "Quality Assurance", "abbr": "QA"}}, {"model": "projects.division", "pk": 11, "fields": {"company": 2, "name": "Business Intelligence", "abbr": "BI"}}, {"model": "projects.division", "pk": 12, "fields": {"company": 3, "name": "Infrastructure", "abbr": "INFRA"}}, {"model": "projects.division", "pk": 13, "fields": {"company": 1, "name": "Procurement", "abbr": "PROC"}}, {"model": "projects.division", "pk": 14, "fields": {"company": 2, "name": "Marketing & Communications", "abbr": "MKT"}}, {"model": "projects.division", "pk": 15, "fields": {"company": 3, "name": "Legal & Compliance", "abbr": "LEGAL"}}, {"model": "projects.division", "pk": 16, "fields": {"company": 1, "name": "Corporate Partnerships", "abbr": "CPART"}}, {"model": "projects.division", "pk": 17, "fields": {"company": 2, "name": "Community Engagement", "abbr": "COMM"}}, {"model": "projects.division", "pk": 18, "fields": {"company": 3, "name": "Training & Development", "abbr": "TRAIN"}}, {"model": "projects.division", "pk": 19, "fields": {"company": 1, "name": "Sustainability", "abbr": "SUS"}}, {"model": "projects.division", "pk": 20, "fields": {"company": 2, "name": "Security & Compliance", "abbr": "SEC"}}, {"model": "projects.division", "pk": 21, "fields": {"company": 3, "name": "Project Management Office", "abbr": "PMO"}}, {"model": "projects.division", "pk": 22, "fields": {"company": 1, "name": "Architecture", "abbr": "ARCH"}}, {"model": "projects.division", "pk": 23, "fields": {"company": 2, "name": "Cloud Engineering", "abbr": "CLOUD"}}, {"model": "projects.division", "pk": 24, "fields": {"company": 3, "name": "Innovation Lab", "abbr": "INNOV"}}, {"model": "projects.division", "pk": 25, "fields": {"company": 1, "name": "UX Research", "abbr": "UXR"}}, {"model": "projects.division", "pk": 26, "fields": {"company": 2, "name": "Mobile Development", "abbr": "MOB"}}, {"model": "projects.division", "pk": 27, "fields": {"company": 3, "name": "Backend Systems", "abbr": "BACK"}}, {"model": "projects.division", "pk": 28, "fields": {"company": 1, "name": "Frontend Development", "abbr": "FRONT"}}, {"model": "projects.division", "pk": 29, "fields": {"company": 2, "name": "DevOps", "abbr": "DEVOPS"}}, {"model": "projects.division", "pk": 30, "fields": {"company": 3, "name": "Analytics", "abbr": "ANLYT"}}, {"model": "projects.division", "pk": 31, "fields": {"company": 1, "name": "E-Commerce", "abbr": "ECOM"}}, {"model": "projects.division", "pk": 32, "fields": {"company": 2, "name": "Public Relations", "abbr": "PR"}}, {"model": "projects.division", "pk": 33, "fields": {"company": 3, "name": "Logistics & Supply Chain", "abbr": "LOG"}}, {"model": "projects.division", "pk": 34, "fields": {"company": 1, "name": "Innovation Strategy", "abbr": "INVS"}}, {"model": "projects.division", "pk": 35, "fields": {"company": 2, "name": "AI Research", "abbr": "AI"}}, {"model": "projects.division", "pk": 36, "fields": {"company": 3, "name": "Customer Insights", "abbr": "CINS"}}, {"model": "projects.division", "pk": 37, "fields": {"company": 1, "name": "Facilities Management", "abbr": "FAC"}}, {"model": "projects.division", "pk": 38, "fields": {"company": 2, "name": "Data Governance", "abbr": "DGOV"}}, {"model": "projects.division", "pk": 39, "fields": {"company": 3, "name": "Risk Management", "abbr": "RISK"}}, {"model": "projects.division", "pk": 40, "fields": {"company": 1, "name": "Corporate Communications", "abbr": "CORCOM"}}, {"model": "projects.division", "pk": 41, "fields": {"company": 2, "name": "Talent Acquisition", "abbr": "TALENT"}}, {"model": "projects.division", "pk": 42, "fields": {"company": 3, "name": "Global Expansion", "abbr": "GLOBAL"}}, {"model": "projects.division", "pk": 43, "fields": {"company": 1, "name": "Accounting", "abbr": "ACC"}}, {"model": "projects.division", "pk": 44, "fields": {"company": 2, "name": "Content Marketing", "abbr": "CONTENT"}}, {"model": "projects.division", "pk": 45, "fields": {"company": 3, "name": "Internal Audit", "abbr": "AUDIT"}}, {"model": "projects.division", "pk": 46, "fields": {"company": 1, "name": "SaaS Operations", "abbr": "SAAS"}}, {"model": "projects.division", "pk": 47, "fields": {"company": 2, "name": "Customer Support", "abbr": "SUP"}}, {"model": "projects.division", "pk": 48, "fields": {"company": 3, "name": "Growth Strategy", "abbr": "GROW"}}, {"model": "projects.division", "pk": 49, "fields": {"company": 1, "name": "Regional Operations", "abbr": "REGOPS"}}, {"model": "projects.division", "pk": 50, "fields": {"company": 2, "name": "International Partnerships", "abbr": "INTL"}}]