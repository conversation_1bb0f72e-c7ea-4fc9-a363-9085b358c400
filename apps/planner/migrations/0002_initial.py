# Generated by Django 5.2.4 on 2025-10-28 00:03

import auto_prefetch
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("planner", "0001_initial"),
        ("projects", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="planproject",
            name="project",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="plan",
            name="projects",
            field=models.ManyToManyField(
                through="planner.PlanProject", to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="plantimeline",
            name="plan",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="planner.plan"
            ),
        ),
        migrations.AddIndex(
            model_name="planproject",
            index=models.Index(fields=["status"], name="planner_pla_status_e3f537_idx"),
        ),
        migrations.AddIndex(
            model_name="planproject",
            index=models.Index(
                fields=["location_rank"], name="planner_pla_locatio_9b3282_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="planproject",
            index=models.Index(
                fields=["vp_rank"], name="planner_pla_vp_rank_1af2b7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="planproject",
            index=models.Index(
                fields=["svp_rank"], name="planner_pla_svp_ran_d1db6f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="planproject",
            index=models.Index(
                fields=["executive_rank"], name="planner_pla_executi_290f88_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="planproject",
            unique_together={("plan", "project")},
        ),
        migrations.AddIndex(
            model_name="plan",
            index=models.Index(fields=["year"], name="planner_pla_year_aa207a_idx"),
        ),
        migrations.AddIndex(
            model_name="plan",
            index=models.Index(fields=["phase"], name="planner_pla_phase_d83668_idx"),
        ),
        migrations.AddIndex(
            model_name="plan",
            index=models.Index(fields=["status"], name="planner_pla_status_bae3f9_idx"),
        ),
        migrations.AddConstraint(
            model_name="plan",
            constraint=models.UniqueConstraint(
                fields=("company", "year"), name="unique_company_year_plan"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="plantimeline",
            unique_together={("plan", "phase")},
        ),
    ]
