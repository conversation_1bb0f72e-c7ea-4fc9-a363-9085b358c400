# Generated by Django 5.2.4 on 2025-10-28 00:03

import auto_prefetch
import django.db.models.deletion
import django.db.models.manager
import django_lifecycle.mixins
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("organizations", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="PlanTimeline",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "phase",
                    models.CharField(
                        choices=[
                            ("locations_rank", "Locations Rank"),
                            (
                                "operations_need_and_risk_factor",
                                "Operations Need & Risk Factor",
                            ),
                            ("vp_rank", "VP Rank"),
                            ("svp_rank", "SVP Rank"),
                            ("executive_adjustment", "Executive Adjustment"),
                        ],
                        max_length=50,
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="Plan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "year",
                    models.PositiveSmallIntegerField(
                        help_text="The fiscal year for which this plan applies."
                    ),
                ),
                (
                    "phase",
                    models.CharField(
                        choices=[
                            ("not_started", "Not Started"),
                            ("locations_rank", "Locations Rank"),
                            (
                                "operations_need_and_risk_factor",
                                "Operations Need & Risk Factor",
                            ),
                            ("vp_rank", "VP Rank"),
                            ("svp_rank", "SVP Rank"),
                            ("executive_adjustment", "Executive Adjustment"),
                            ("allocated", "Allocated"),
                        ],
                        default="not_started",
                        help_text="The current phase of this plan.",
                        max_length=50,
                    ),
                ),
                ("capital_budget", models.BigIntegerField(default=0)),
                ("expense_budget", models.BigIntegerField(default=0)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("past", "Past"),
                            ("current", "Current"),
                            ("next", "Next"),
                            ("future", "Future"),
                        ],
                        max_length=50,
                    ),
                ),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
                (
                    "company",
                    auto_prefetch.ForeignKey(
                        help_text="The company this capital plan belongs to.",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="plans",
                        to="organizations.company",
                    ),
                ),
            ],
            options={
                "ordering": ["company", "year"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="PlanProject",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("capital_amount", models.BigIntegerField(blank=True, null=True)),
                ("expense_amount", models.BigIntegerField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("incomplete", "Draft"),
                            ("submitted", "Submitted"),
                            ("allocated", "Allocated"),
                            ("withheld", "Returned"),
                            ("rejected", "Rejected"),
                        ],
                        max_length=50,
                    ),
                ),
                ("global_rank", models.PositiveIntegerField(blank=True, null=True)),
                ("location_rank", models.PositiveIntegerField(blank=True, null=True)),
                ("vp_rank", models.PositiveIntegerField(blank=True, null=True)),
                ("svp_rank", models.PositiveIntegerField(blank=True, null=True)),
                ("executive_rank", models.PositiveIntegerField(blank=True, null=True)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
                (
                    "plan",
                    auto_prefetch.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="planner.plan"
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
    ]
