from datetime import date
from typing import Any, Dict, Optional

from django.contrib import messages
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q, QuerySet
from django.db.models.aggregates import Max
from django.db.models.expressions import F
from django.forms.formsets import BaseFormSet
from django.forms.models import ModelForm
from django.http.request import HttpRequest
from django.http.response import (
    HttpResponse,
    HttpResponseNotAllowed,
    HttpResponseRedirect,
    JsonResponse,
)
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.utils.functional import cached_property
from django.views.generic.base import RedirectView
from django.views.generic.detail import DetailView
from django.views.generic.edit import CreateView, DeleteView, UpdateView
from django.views.generic.list import ListView
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated

from apps.locations.models import Location
from apps.notifications.models import MessageTypes, notify
from apps.planner.filters import ProjectProposalFilter, ProjectRankFilter
from apps.planner.forms import (
    PlanChooserForm,
    PlanForm,
    PlanProjectFinancialFormSet,
    PlanTimelineFormSet,
    ProjectFinancialForm,
    ProjectOperationsNeedForm,
    ProjectProposalFilterForm,
    ProjectProposalForm,
    ProjectRiskFactorForm,
)
from apps.planner.models import (
    Plan,
    PlanProject,
    PlanProjectQuerySet,
    PlanTimeline,
)
from apps.planner.serializers import PlannerExportCSVRenderer, PlannerSerializer
from apps.projects.models import Project, ProjectOperationsNeed, ProjectRisk
from apps.utils.mixins import (
    CompanyAdminRequiredMixin,
    ProjectDeactivatedMixin,
)
from apps.organizations.mixins import RolePermissionMixin
from apps.organizations.mixins import CompanyQuerysetMixin

from apps.organizations.mixins import RolePermissionMixin


class PlannerListView(RolePermissionMixin, ListView):
    model = Plan
    required_permission = "read"

    queryset = (
        PlanProject.objects.select_related(
            "plan",
            "project",
            "project__location",
            "project__projectoperationsneed",
            "project__projectrisk",
        )
        .with_current_rank()
        .with_scores()
        .with_length_in_years()
        .filtered_viewable()
        .order_by(F("current_rank").asc(nulls_last=True), "created")
    )
    template_name = "planner/home.html"

    @cached_property
    def plan(self):
        return (
            Plan.objects.with_status_preference().order_by("status_preference").first()
        )

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        queryset = queryset.filter(
            plan=self.plan, project__company=self.request.user.company
        )

        if self.plan and self.plan.can_user_rank_or_evaluate_projects(
            self.request.user
        ):
            if self.plan.phase_choice.is_single_location_ranking_phase():
                queryset = ProjectRankFilter(
                    self.request.user, self.request.GET, queryset
                ).qs

            rank_field = PlanProject.get_rank_field(self.plan.get_phase())
            if rank_field:
                queryset.order_by().defragment_ranks(rank_field=rank_field)

        return queryset

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        self.object_list: PlanProjectQuerySet
        context = super().get_context_data(**kwargs)
        context["plan"] = self.plan
        context["can_edit"] = (
            self.plan
            and self.plan.can_user_rank_or_evaluate_projects(self.request.user)
        )
        if (
            self.plan
            and self.plan.phase_choice.is_single_location_ranking_phase()
            and self.plan.can_user_rank_or_evaluate_projects(self.request.user)
        ):
            context["filter_form"] = ProjectRankFilter(
                self.request.user, self.request.GET, self.object_list
            ).form
        context["timelines"] = (
            PlanTimeline.objects.filter(plan=self.plan)
            .with_phase_order()
            .order_by("phase_order")
        )
        if self.plan:
            context["allocation_date"] = self.plan.get_allocation_date()
        context["project_count"] = self.object_list.count()
        context["total_capital_amount"] = (
            self.object_list.total_capital_amount()["capital_amount__sum"] or 0
        )
        context["total_capital_carry_in"] = (
            self.object_list.total_capital_carry_in()["capital_amount__sum"] or 0
        )
        context["total_capital_allocated"] = (
            self.object_list.total_capital_allocated()["capital_amount__sum"] or 0
        )
        context["total_capital_roi_amount"] = (
            self.object_list.total_capital_roi_amount()["capital_amount__sum"] or 0
        )
        context["total_capital_roi_allocated"] = (
            self.object_list.total_capital_roi_allocated()["capital_amount__sum"] or 0
        )
        context["total_capital_roi_carry_in"] = (
            self.object_list.total_capital_roi_carry_in()["capital_amount__sum"] or 0
        )
        context["total_capital_rrns_amount"] = (
            self.object_list.total_capital_rrns_amount()["capital_amount__sum"] or 0
        )
        context["total_capital_rrns_allocated"] = (
            self.object_list.total_capital_rrns_allocated()["capital_amount__sum"] or 0
        )
        context["total_capital_rrns_carry_in"] = (
            self.object_list.total_capital_rrns_carry_in()["capital_amount__sum"] or 0
        )
        context["total_expense_amount"] = (
            self.object_list.total_expense_amount()["expense_amount__sum"] or 0
        )
        context["total_expense_allocated"] = (
            self.object_list.total_expense_allocated()["expense_amount__sum"] or 0
        )
        context["total_expense_only_amount"] = (
            self.object_list.total_expense_only_amount()["expense_amount__sum"] or 0
        )
        context["total_expense_with_capital_amount"] = (
            self.object_list.total_expense_with_capital_amount()["expense_amount__sum"]
            or 0
        )
        return context


class PlanProjectRankSetAjaxView(RolePermissionMixin, DeleteView):
    def get_model(self):
        from apps.planner.models import Plan

        return Plan

    required_permission = "update"
    queryset = PlanProject.objects.all()

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        queryset = queryset.filtered_user_rankable(self.request.user)
        return queryset

    def get(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        return HttpResponseNotAllowed(["POST"])

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        rank = kwargs["rank"]
        self.object: PlanProject = self.get_object()
        queryset = self.get_queryset().filter(plan=self.object.plan)
        if rank.lower() == "r":
            self.object.reject()
        elif rank.isdigit():
            rank = int(rank)
            if self.object.plan.phase_choice.is_single_location_ranking_phase():
                queryset = queryset.filter(
                    project__location=self.object.project.location
                )
            PlanProject.set_rank(queryset=queryset, plan_project=self.object, rank=rank)
        message = {"message": f"{self.object} set at {rank}."}
        return JsonResponse(message)


class PlanProjectRankUpAjaxView(RolePermissionMixin, DeleteView):
    def get_model(self):
        from apps.planner.models import Plan

        return Plan

    required_permission = "update"
    queryset = PlanProject.objects.all()

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        queryset = queryset.filtered_user_rankable(self.request.user)
        return queryset

    def get(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        return HttpResponseNotAllowed(["POST"])

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object: PlanProject = self.get_object()
        queryset = self.get_queryset().filter(plan=self.object.plan)
        if self.object.plan.phase_choice.is_single_location_ranking_phase():
            queryset = queryset.filter(project__location=self.object.project.location)
        PlanProject.move_rank_up(queryset=queryset, plan_project=self.object)
        message = {"message": f"{self.object} moved up."}
        return JsonResponse(message)


class PlanProjectRankDownAjaxView(RolePermissionMixin, DeleteView):
    def get_model(self):
        from apps.planner.models import Plan

        return Plan

    required_permission = "update"
    queryset = PlanProject.objects.all()

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        queryset = queryset.filtered_user_rankable(self.request.user)
        return queryset

    def get(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        return HttpResponseNotAllowed(["POST"])

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object: PlanProject = self.get_object()
        queryset = self.get_queryset().filter(plan=self.object.plan)
        if self.object.plan.phase_choice.is_single_location_ranking_phase():
            queryset = queryset.filter(project__location=self.object.project.location)
        PlanProject.move_rank_down(queryset=queryset, plan_project=self.object)
        message = {"message": f"{self.object} moved up."}
        return JsonResponse(message)


class ProjectProposalCreateView(RolePermissionMixin, CreateView):
    def get_model(self):
        from apps.planner.models import Plan

        return Plan

    required_permission = "create"
    model = Project
    form_class = ProjectProposalForm
    template_name = "planner/project_form.html"

    def get_form_kwargs(self) -> Dict[str, Any]:
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def get_formset(self):
        self.object: Optional[Project]
        formset_kwargs = {
            "project": self.object,
            "data": self.request.POST if self.request.method == "POST" else None,
        }
        return PlanProjectFinancialFormSet(**formset_kwargs)

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        self.object: Optional[Project]
        context = super().get_context_data(**kwargs)
        if "formset" not in kwargs:
            context["formset"] = self.get_formset()
        return context

    def post(self, request: HttpRequest, *args, **kwargs):
        self.object = None
        form = self.get_form()
        form_is_valid = form.is_valid()
        if form_is_valid:
            self.object = form.save(commit=False)
        formset = self.get_formset()
        formset_is_valid = formset.is_valid()
        if form_is_valid and formset_is_valid:
            return self.form_valid(form, formset)
        else:
            return self.form_invalid(form, formset)

    def form_valid(self, form, formset):
        self.object = form.save()
        formset.save()
        return HttpResponseRedirect(self.get_success_url())

    def form_invalid(self, form, formset):
        context = self.get_context_data(form=form, formset=formset)
        return self.render_to_response(context)

    def get_success_url(self) -> str:
        if "add_another" in self.request.POST:
            return reverse("planner_create_project")
        elif "next" in self.request.GET:
            return self.request.GET["next"]
        else:
            return reverse("planner_detail_project", args=[self.object.pk])


class ProjectProposalUpdateView(
    RolePermissionMixin,
    ProjectDeactivatedMixin,
    UpdateView,
):
    def get_model(self):
        from apps.planner.models import Plan

        return Plan

    required_permission = "update"
    queryset = Project.objects.filter(active=True)
    form_class = ProjectProposalForm
    template_name = "planner/project_form.html"

    def get_queryset(self):
        user = self.request.user
        queryset = super().get_queryset()
        queryset = queryset.filtered_user_modify_access(user=user)
        return queryset

    def get_form_kwargs(self) -> Dict[str, Any]:
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def get_formset(self):
        self.object: Project
        formset_kwargs = {
            "project": self.object,
            "data": self.request.POST if self.request.method == "POST" else None,
        }
        return PlanProjectFinancialFormSet(**formset_kwargs)

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        self.object: Project
        context = super().get_context_data(**kwargs)
        if "formset" not in kwargs:
            context["formset"] = self.get_formset()
        return context

    def post(self, request: HttpRequest, *args, **kwargs):
        self.object = self.get_object()
        form = self.get_form()
        formset = self.get_formset()
        form_is_valid = form.is_valid()
        formset_is_valid = formset.is_valid()
        if form_is_valid and formset_is_valid:
            return self.form_valid(form, formset)
        else:
            return self.form_invalid(form, formset)

    def form_valid(self, form, formset):
        self.object = form.save()
        formset.save()
        return HttpResponseRedirect(self.get_success_url())

    def form_invalid(self, form, formset):
        context = self.get_context_data(form=form, formset=formset)
        return self.render_to_response(context)

    def get_success_url(self) -> str:
        if "next" in self.request.GET:
            return self.request.GET["next"]
        else:
            return reverse("planner_detail_project", args=[self.object.pk])


class ProjectOperationsNeedIframeView(RolePermissionMixin, UpdateView):
    def get_model(self):
        from apps.planner.models import Plan

        return Plan

    required_permission = "update"
    queryset = Project.objects.filter(active=True)
    form_class = ProjectOperationsNeedForm
    template_name = "planner/operations_need_frame.html"

    def get_queryset(self):
        user = self.request.user
        queryset = super().get_queryset()
        queryset = queryset.filtered_user_modify_access(user=user)
        return queryset

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["instance"] = ProjectOperationsNeed.objects.filter(
            project=self.object
        ).first()
        kwargs["project"] = self.object
        return kwargs

    def form_valid(self, form: ModelForm) -> HttpResponse:
        form.save()
        form.instance.project.save()
        content = "<script>window.parent.postMessage('reloadWindow', location.origin);</script>"
        return HttpResponse(content)


class ProjectRiskFactorIframeView(RolePermissionMixin, UpdateView):
    def get_model(self):
        from apps.planner.models import Plan

        return Plan

    required_permission = "update"
    queryset = Project.objects.filter(active=True)
    form_class = ProjectRiskFactorForm
    template_name = "planner/risk_factor_frame.html"

    def get_queryset(self):
        user = self.request.user
        queryset = super().get_queryset()
        queryset = queryset.filtered_user_modify_access(user=user)
        return queryset

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["instance"] = ProjectRisk.objects.filter(project=self.object).first()
        kwargs["project"] = self.object
        return kwargs

    def form_valid(self, form: ModelForm) -> HttpResponse:
        form.save()
        form.instance.project.save()
        content = "<script>window.parent.postMessage('reloadWindow', location.origin);</script>"
        return HttpResponse(content)


class ProjectFinancialsIframeView(RolePermissionMixin, UpdateView):
    def get_model(self):
        from apps.planner.models import Plan

        return Plan

    required_permission = "update"
    queryset = Project.objects.filter(active=True)
    form_class = ProjectFinancialForm
    template_name = "planner/financials_frame.html"

    def get_queryset(self):
        user = self.request.user
        queryset = super().get_queryset()
        queryset = queryset.filtered_user_modify_access(user=user)
        return queryset

    def get_formset(self):
        self.object: Project
        formset_kwargs = {
            "project": self.object,
            "data": self.request.POST if self.request.method == "POST" else None,
        }
        return PlanProjectFinancialFormSet(**formset_kwargs)

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        self.object: Project
        context = super().get_context_data(**kwargs)
        if "formset" not in kwargs:
            context["formset"] = self.get_formset()
        return context

    def post(self, request: HttpRequest, *args, **kwargs):
        self.object = self.get_object()
        form = self.get_form()
        formset = self.get_formset()
        form_is_valid = form.is_valid()
        formset_is_valid = formset.is_valid()
        if form_is_valid and formset_is_valid:
            return self.form_valid(form, formset)
        else:
            return self.form_invalid(form, formset)

    def form_valid(self, form, formset):
        self.object = form.save()
        formset.save()
        content = "<script>window.parent.postMessage('reloadWindow', location.origin);</script>"
        return HttpResponse(content)

    def form_invalid(self, form, formset):
        context = self.get_context_data(form=form, formset=formset)
        return self.render_to_response(context)


class ProjectProposalDeleteView(
    RolePermissionMixin,
    ProjectDeactivatedMixin,
    DeleteView,
):
    def get_model(self):
        from apps.planner.models import Plan

        return Plan

    required_permission = "delete"
    queryset = Project.objects.filter(active=True)
    permission_required = "project.delete_project"
    template_name = "planner/project_confirm_delete.html"

    def get_queryset(self):
        user = self.request.user
        queryset = super().get_queryset()
        queryset = queryset.filtered_user_modify_access(user=user)
        return queryset

    def create_notifications(self):
        self.object: Project
        message = "{user} deleted {name}".format(
            user=self.request.user, name=self.object.name
        )
        notify(
            message_type=MessageTypes.PROJECT_DELETED,
            message=message,
            creator=self.request.user,
            content_object=self.object,
        )

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object: Project = self.get_object()
        self.object.active = False
        self.object.save()
        self.create_notifications()
        messages.success(request, "Project has been deactivated.")
        success_url = self.get_success_url()
        return HttpResponseRedirect(success_url)

    def get_success_url(self) -> str:
        if "next" in self.request.GET:
            return self.request.GET["next"]
        else:
            return reverse("planner_home")


class ProjectProposalRestoreView(
    CompanyAdminRequiredMixin, CompanyQuerysetMixin, DeleteView
):
    queryset = Project.objects.filter(active=False)
    permission_required = "project.delete_project"
    template_name = "planner/project_confirm_restore.html"

    def get_queryset(self):
        user = self.request.user
        queryset = super().get_queryset()
        queryset = queryset.filtered_user_modify_access(user=user)
        return queryset

    def create_notifications(self):
        self.object: Project
        message = "{user} restored {name}".format(
            user=self.request.user, name=self.object.name
        )
        notify(
            message_type=MessageTypes.PROJECT_STATE_CHANGE,
            message=message,
            creator=self.request.user,
            content_object=self.object,
        )

    def delete(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        self.object: Project = self.get_object()
        self.object.active = True
        self.object.save()
        self.create_notifications()
        success_url = self.get_success_url()
        messages.success(request, "Project has been restored.")
        return HttpResponseRedirect(success_url)

    def get_success_url(self) -> str:
        return reverse("planner_detail_project", args=[self.object.pk])


class ProjectProposalDetailView(RolePermissionMixin, DetailView):
    def get_model(self):
        from apps.planner.models import Plan

        return Plan

    required_permission = "read"
    """
    Displays detailed information about a project proposal within a planning context.
    """

    queryset = Project.objects.select_related(
        "location",
        "primary_division",
        "created_by",
        "projectoperationsneed",
        "projectrisk",
    )
    template_name = "planner/project_detail.html"

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        project: Project = self.object

        context.update(
            {
                "content_type_id": ContentType.objects.get_for_model(PlanProject).pk,
                "next_plan_project": project.next_plan_project,
                "plan_projects": project.relevant_plan_projects,
                "capital_finance_json": project.capital_finance_json,
                "expense_finance_json": project.expense_finance_json,
                "can_edit": not self.request.user.is_anonymous,
            }
        )

        if project.next_plan_project:
            context["attachments"] = project.next_plan_project.serialized_attachments
            context["links"] = project.next_plan_project.serialized_links
        else:
            context["attachments"] = []
            context["links"] = []

        return context


class PlanRedirectView(RedirectView):
    def get_redirect_url(self, *args, **kwargs) -> Optional[str]:
        plan = (
            Plan.objects.with_status_preference().order_by("status_preference").first()
        )
        if plan is None:
            return reverse("planner_home")
        return reverse("planner_detail_plan", args=[plan.year])


class ProjectProposalListView(RolePermissionMixin, ListView):
    def get_model(self):
        from apps.planner.models import Plan

        return Plan

    required_permission = "read"
    queryset = (
        PlanProject.objects.select_related(
            "project",
            "plan",
            "project__location",
            "project__projectoperationsneed",
            "project__projectrisk",
        )
        .with_current_rank()
        .with_scores()
        .with_length_in_years()
        .filtered_included()
        .distinct()
        .order_by(F("current_rank").asc(nulls_last=True), "created")
    )

    paginate_by = 50
    template_name = "planner/project_list.html"

    @cached_property
    def plan(self):
        return get_object_or_404(
            Plan.objects.all(),
            year=self.kwargs["year"],
            company=self.request.user.company,
        )

    def get_unfiltered_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.filter(plan=self.plan)
        return queryset

    def get_queryset(self) -> QuerySet:
        queryset = self.get_unfiltered_queryset()
        queryset = ProjectProposalFilter(self.request.GET, queryset=queryset).qs
        return queryset

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        self.object_list: PlanProjectQuerySet
        context = super().get_context_data(**kwargs)
        context["plan"] = self.plan
        context["can_edit"] = (
            self.plan
            and self.plan.can_user_rank_or_evaluate_projects(self.request.user)
        )
        context["plan_chooser_form"] = PlanChooserForm(initial={"year": self.plan.year})
        context["timelines"] = (
            PlanTimeline.objects.filter(plan=self.plan)
            .with_phase_order()
            .order_by("phase_order")
        )
        context["allocation_date"] = self.plan.get_allocation_date()
        unfiltered_list = self.get_unfiltered_queryset()
        context["unfiltered_project_count"] = unfiltered_list.count()
        context["unfiltered_total_capital_amount"] = (
            unfiltered_list.total_capital_amount()["capital_amount__sum"] or 0
        )
        context["unfiltered_total_expense_amount"] = (
            unfiltered_list.total_expense_amount()["expense_amount__sum"] or 0
        )
        context["total_capital_amount"] = (
            self.object_list.total_capital_amount()["capital_amount__sum"] or 0
        )
        context["total_capital_carry_in"] = (
            self.object_list.total_capital_carry_in()["capital_amount__sum"] or 0
        )
        context["total_capital_allocated"] = (
            self.object_list.total_capital_allocated()["capital_amount__sum"] or 0
        )
        context["total_capital_roi_amount"] = (
            self.object_list.total_capital_roi_amount()["capital_amount__sum"] or 0
        )
        context["total_capital_roi_allocated"] = (
            self.object_list.total_capital_roi_allocated()["capital_amount__sum"] or 0
        )
        context["total_capital_roi_carry_in"] = (
            self.object_list.total_capital_roi_carry_in()["capital_amount__sum"] or 0
        )
        context["total_capital_rrns_amount"] = (
            self.object_list.total_capital_rrns_amount()["capital_amount__sum"] or 0
        )
        context["total_capital_rrns_allocated"] = (
            self.object_list.total_capital_rrns_allocated()["capital_amount__sum"] or 0
        )
        context["total_capital_rrns_carry_in"] = (
            self.object_list.total_capital_rrns_carry_in()["capital_amount__sum"] or 0
        )
        context["total_expense_amount"] = (
            self.object_list.total_expense_amount()["expense_amount__sum"] or 0
        )
        context["total_expense_allocated"] = (
            self.object_list.total_expense_allocated()["expense_amount__sum"] or 0
        )
        context["total_expense_only_amount"] = (
            self.object_list.total_expense_only_amount()["expense_amount__sum"] or 0
        )
        context["total_expense_with_capital_amount"] = (
            self.object_list.total_expense_with_capital_amount()["expense_amount__sum"]
            or 0
        )
        context["filter_form"] = ProjectProposalFilterForm(self.request.GET)
        return context


class PlanListView(CompanyAdminRequiredMixin, CompanyQuerysetMixin, ListView):
    queryset = Plan.objects.with_start_and_end_dates().order_by("year")
    template_name = "planner/plan_list.html"


class PlanCreateView(CompanyAdminRequiredMixin, CompanyQuerysetMixin, RedirectView):
    plan: Plan
    template_name = "planner/plan_form.html"

    def get_next_year(self) -> int:
        year = (
            Plan.objects.all().aggregate(Max("year")).get("year__max")
            or date.today().year
        )
        return year + 1

    def get(self, *args, **kwargs):
        year = self.get_next_year()
        self.plan = Plan.objects.create(year=year)
        return super().get(*args, **kwargs)

    def get_redirect_url(self, *args: Any, **kwargs: Any) -> Optional[str]:
        return reverse("planner_update_plan", args=[self.plan.year])


class PlanUpdateView(CompanyAdminRequiredMixin, CompanyQuerysetMixin, UpdateView):
    model = Plan
    form_class = PlanForm
    slug_field = "year"
    slug_url_kwarg = "year"
    template_name = "planner/plan_form.html"

    def create_workflow_dates_notification(self):
        self.object: Plan
        message = f"{self.request.user} set the workflow dates for {self.object.year}"
        notify(
            message_type=MessageTypes.PLANNER_WORKFLOW_DATES_SET,
            message=message,
            creator=self.request.user,
            content_object=self.object,
        )

    def get_formset(self) -> BaseFormSet:
        self.object: Plan
        formset_kwargs = {
            "plan": self.object,
            "data": self.request.POST if self.request.method == "POST" else None,
        }
        return PlanTimelineFormSet(**formset_kwargs)

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        deliver_notification = False
        year = self.kwargs.get("year")
        plan_timelines = PlanTimeline.objects.filter(plan__year=year)
        if "formset" not in kwargs:
            if not plan_timelines:
                # fetching the formset will generate timelines
                deliver_notification = True
            context["formset"] = self.get_formset()
        if deliver_notification:
            plan_timelines = PlanTimeline.objects.filter(plan__year=year)
            self.create_workflow_dates_notification()
        context["year"] = year
        return context

    def post(self, request: HttpRequest, *args, **kwargs):
        self.object = self.get_object()
        form = self.get_form()
        formset = self.get_formset()
        form_is_valid = form.is_valid()
        formset_is_valid = formset.is_valid()
        if form_is_valid and formset_is_valid:
            return self.form_valid(form, formset)
        else:
            return self.form_invalid(form, formset)

    def form_valid(self, form, formset):
        self.object = form.save()
        formset.save()
        return HttpResponseRedirect(self.get_success_url())

    def form_invalid(self, form, formset):
        context = self.get_context_data(form=form, formset=formset)
        return self.render_to_response(context)

    def get_success_url(self) -> str:
        if "next" in self.request.GET:
            return self.request.GET["next"]
        else:
            return reverse("planner_detail_plan", args=[self.object.year])


class PlanDetailView(CompanyAdminRequiredMixin, CompanyQuerysetMixin, DetailView):
    queryset = Plan.objects.all()
    slug_field = "year"
    slug_url_kwarg = "year"
    template_name = "planner/plan_detail.html"


class PlanExportView(RolePermissionMixin, ListAPIView):
    def get_model(self):
        from apps.planner.models import Plan

        return Plan

    required_permission = "read"
    queryset = (
        PlanProject.objects.select_related(
            "plan",
            "project",
            "project__location",
            "project__business_segment",
            "project__projectoperationsneed",
            "project__projectrisk",
            "project__created_by",
        )
        .with_previous_year_capital_request()
        .with_future_year_capital_request()
        .with_remaining_capital_budget_amount()
        .with_previous_year_expense_request()
        .with_future_year_expense_request()
        .with_remaining_expense_budget_amount()
        .with_has_carry_in()
        .with_scores()
        .filtered_included()
        .distinct()
    )
    serializer_class = PlannerSerializer
    renderer_classes = [PlannerExportCSVRenderer]
    permission_classes = [IsAuthenticated]
    pagination_class = None

    def filter_queryset(self, queryset: QuerySet) -> QuerySet:
        queryset = ProjectProposalFilter(self.request.GET, queryset=queryset).qs
        queryset = queryset.filter(plan__year=self.kwargs["year"])
        return queryset

    def finalize_response(self, *args, **kwargs):
        response = super().finalize_response(*args, **kwargs)
        response["Content-Disposition"] = (
            f"attachment; filename=planner {self.kwargs['year']}.csv"
        )
        return response
