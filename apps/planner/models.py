import auto_prefetch
from datetime import date, timedelta
from typing import Dict, List, Optional, Any, Tuple, Set

from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import GenericRelation
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.db.models import QuerySet
from django.db.models.aggregates import Count, Max, Min, Sum
from django.db.models.expressions import (
    Case,
    Exists,
    ExpressionWrapper,
    F,
    OuterRef,
    Subquery,
    Value,
    When,
)
from django.db.models.query_utils import Q
from django.urls import reverse

from django_lifecycle import LifecycleModel, hook, BEFORE_SAVE, AFTER_SAVE

from apps.attachments.models import Attachment
from apps.comments.models import Comment
from apps.links.models import Link
from apps.locations.models import SizeChoices
from apps.planner.data import DivisionCodeDict
from apps.projects.models import (
    Project,
    ProjectOperationsNeed,
    ProjectRisk,
)
from apps.utils.dates import format_date_range


from calendar import SUNDAY, HTMLCalendar
from datetime import timedelta
from typing import List


Day = int

CARRY_IN_RANK = -2
EXPENSE_ONLY_RANK = -1
REJECTED_RANK = 999_999
LOWEST_RANK = 999_999


User = get_user_model()


class PlanQuerySet(QuerySet):
    def with_status_preference(self) -> QuerySet:
        status_whens = [
            When(status=Plan.Status.PAST, then=Value(3)),
            When(status=Plan.Status.CURRENT, then=Value(2)),
            When(status=Plan.Status.NEXT, then=Value(1)),
            When(status=Plan.Status.FUTURE, then=Value(4)),
        ]
        return self.annotate(
            status_preference=Case(
                *status_whens, default=Value(10), output_field=models.IntegerField()
            )
        )

    def with_start_and_end_dates(self) -> QuerySet:
        return self.annotate(
            start_date=Subquery(
                PlanTimeline.objects.filter(plan=OuterRef("pk"))
                .order_by("start_date")
                .values("start_date")[:1]
            ),
            end_date=Subquery(
                PlanTimeline.objects.filter(plan=OuterRef("pk"))
                .order_by("-end_date")
                .values("end_date")[:1]
            ),
        )

    def with_total_budget(self) -> QuerySet:
        return self.annotate(capital_budget=F("capital_budget") + F("expense_budget"))


class PlanManager(auto_prefetch.Manager):
    def get_current(self) -> Optional["Plan"]:
        return self.filter(status=Plan.Status.CURRENT).first()

    def get_next(self) -> Optional["Plan"]:
        return self.filter(status=Plan.Status.NEXT).first()

    def set_as_current(self, plan: "Plan"):
        if plan.get_status() != Plan.Status.CURRENT:
            plan.status = Plan.Status.CURRENT
            plan.save()
        # Ensure only a single current plan
        old_plans = self.exclude(pk=plan.pk).filter(status=Plan.Status.CURRENT)
        for old_plan in old_plans:
            old_plan.status = Plan.Status.PAST
            old_plan.save()

    def set_as_next(self, plan: "Plan"):
        if plan.get_status() != Plan.Status.NEXT:
            plan.status = Plan.Status.NEXT
            plan.save()
        # Ensure only a single next plan
        old_plans = (
            self.exclude(pk=plan.pk).filter(status=Plan.Status.NEXT).order_by("-year")
        )
        old_next_plan = old_plans.first()
        if old_next_plan:
            self.set_as_current(old_next_plan)
        for old_plan in old_plans[1:]:
            old_plan.status = Plan.Status.PAST
            old_plan.save()


class Plan(LifecycleModel, auto_prefetch.Model):
    """
    Represents a capital planning cycle for a given company and year.

    Each Plan is associated with a specific company and a year, and contains metadata
    about budgeting, project phases, and overall status. The combination of (company, year)
    must be unique, ensuring each company can have only one plan per year.

    This model also provides logic for advancing plan phases, setting statuses,
    and calculating total capital and expense amounts from associated PlanProjects.
    """

    class Phase(models.TextChoices):
        NOT_STARTED = "not_started", "Not Started"
        LOCATIONS_RANK = "locations_rank", "Locations Rank"
        OPERATIONS_NEED_AND_RISK_FACTOR = (
            "operations_need_and_risk_factor",
            "Operations Need & Risk Factor",
        )
        VP_RANK = "vp_rank", "VP Rank"
        SVP_RANK = "svp_rank", "SVP Rank"
        EXECUTIVE_ADJUSTMENT = "executive_adjustment", "Executive Adjustment"
        ALLOCATED = "allocated", "Allocated"

        def is_single_location_ranking_phase(self) -> bool:
            """
            Returns:
                bool: True if the phase is a single location ranking phase, False otherwise.
            """
            return self in [
                Plan.Phase.NOT_STARTED,
                Plan.Phase.LOCATIONS_RANK,
                Plan.Phase.OPERATIONS_NEED_AND_RISK_FACTOR,
            ]

    class Status(models.TextChoices):
        PAST = "past", "Past"
        CURRENT = "current", "Current"
        NEXT = "next", "Next"
        FUTURE = "future", "Future"

    RANKING_PHASES = [
        Phase.NOT_STARTED,
        Phase.LOCATIONS_RANK,
        Phase.VP_RANK,
        Phase.SVP_RANK,
        Phase.EXECUTIVE_ADJUSTMENT,
    ]

    company = auto_prefetch.ForeignKey(
        "organizations.Company",
        on_delete=models.PROTECT,
        related_name="plans",
        help_text="The company this capital plan belongs to.",
    )
    year: int = models.PositiveSmallIntegerField(
        help_text="The fiscal year for which this plan applies."
    )
    projects = models.ManyToManyField(Project, through="PlanProject")
    phase: str = models.CharField(
        max_length=50,
        choices=Phase.choices,
        default=Phase.NOT_STARTED,
        help_text="The current phase of this plan.",
    )
    capital_budget: int = models.BigIntegerField(default=0)
    expense_budget: int = models.BigIntegerField(default=0)
    status: str = models.CharField(max_length=50, choices=Status.choices)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = PlanManager.from_queryset(PlanQuerySet)()

    class Meta(auto_prefetch.Model.Meta):
        constraints = [
            models.UniqueConstraint(
                fields=["company", "year"],
                name="unique_company_year_plan",
            ),
        ]
        indexes = [
            models.Index(fields=["year"]),
            models.Index(fields=["phase"]),
            models.Index(fields=["status"]),
        ]
        ordering = ["company", "year"]

    def __str__(self) -> str:
        """Return a human-readable representation of the plan."""
        return f"{self.company} - {self.year} Capital Plan"

    @property
    def calendars(self) -> List[str]:
        """
        Return a list of HTML calendars representing all timeline phases for this plan.

        Each calendar corresponds to one month spanned by any of the plan's timelines.
        Days are color-coded according to their phase (e.g., Locations Rank, VP Rank, etc.).

        Returns:
            List[str]: A list of HTML strings for monthly calendars.
        """
        plan_timelines = self.plantimeline_set.all()
        if not plan_timelines.exists():
            return []
        return PlanTimeline.restructure_dates(list(plan_timelines))

    @property
    def phase_choice(self):
        """Returb the enum value of the phase"""
        return self.get_phase()

    def get_phase(self) -> Phase:
        return self.Phase(self.phase)

    def get_status(self) -> Status:
        return self.Status(self.status)

    def advance_current_phase(self) -> Phase:
        next_phases = {
            self.Phase.NOT_STARTED: self.Phase.LOCATIONS_RANK,
            self.Phase.LOCATIONS_RANK: self.Phase.OPERATIONS_NEED_AND_RISK_FACTOR,
            self.Phase.OPERATIONS_NEED_AND_RISK_FACTOR: self.Phase.VP_RANK,
            self.Phase.VP_RANK: self.Phase.SVP_RANK,
            self.Phase.SVP_RANK: self.Phase.EXECUTIVE_ADJUSTMENT,
            self.Phase.EXECUTIVE_ADJUSTMENT: self.Phase.ALLOCATED,
            self.Phase.ALLOCATED: self.Phase.ALLOCATED,
        }
        phase = self.get_phase()
        next_phase = next_phases[phase]
        if self.phase != next_phase:
            self.phase = next_phase
            self.save()
        return self.get_phase()

    def advance_current_phase_to_today(self):
        phase = self.get_phase()
        while True:
            end_date = self.get_current_phase_end_date()
            if (
                phase == self.Phase.ALLOCATED
                or not end_date
                or date.today() <= end_date
            ):
                break
            else:
                phase = self.advance_current_phase()

    def generate_timelines(
        self, seed_date: Optional[date] = None, period: Day = 13, gap: Day = 3
    ):
        def get_date_generator(seed_date: date, period: Day, gap: Day):
            next_date = seed_date
            yield next_date
            while True:
                next_date += timedelta(days=period)
                yield next_date
                next_date += timedelta(days=gap)
                yield next_date

        if not self.pk:
            return
        if not seed_date:
            seed_date = date(self.year - 1, 3, 1)
        date_generator = get_date_generator(seed_date=seed_date, period=period, gap=gap)
        for phase in PlanTimeline.Phase:
            start_date = next(date_generator)
            end_date = next(date_generator)
            if not PlanTimeline.objects.filter(plan=self, phase=phase).exists():
                PlanTimeline.objects.create(
                    plan=self, phase=phase, start_date=start_date, end_date=end_date
                )

    def get_start_date(self) -> Optional[date]:
        return (
            self.plantimeline_set.all()
            .aggregate(Min("start_date"))
            .get("start_date__min")
        )

    def get_end_date(self) -> Optional[date]:
        return (
            self.plantimeline_set.all().aggregate(Max("end_date")).get("end_date__max")
        )

    def get_current_phase_end_date(self) -> Optional[date]:
        timeline = self.plantimeline_set.filter(phase=self.phase).first()
        return timeline.end_date if timeline else None

    def get_current_phase_time_left(self) -> timedelta:
        today = date.today()
        end_date = self.get_current_phase_end_date()
        if not end_date:
            return timedelta(days=0)
        if today >= end_date:
            return timedelta(days=0)
        return max(end_date - today, timedelta(days=0))

    def get_allocation_date(self) -> Optional[date]:
        plan_timeline = self.plantimeline_set.filter(
            phase=PlanTimeline.Phase.EXECUTIVE_ADJUSTMENT
        ).first()
        return plan_timeline.end_date if plan_timeline else None

    def total_capital_amount(self) -> int:
        return (
            self.planproject_set.filter(project__active=True).total_capital_amount()[
                "capital_amount__sum"
            ]
            or 0
        )

    def total_expense_amount(self) -> int:
        return (
            self.planproject_set.filter(project__active=True).total_expense_amount()[
                "expense_amount__sum"
            ]
            or 0
        )

    def total_capital_allocated(self) -> int:
        return (
            self.planproject_set.filter(
                project__active=True, status=PlanProject.Status.ALLOCATED
            ).total_capital_amount()["capital_amount__sum"]
            or 0
        )

    def total_expense_allocated(self) -> int:
        return (
            self.planproject_set.filter(
                project__active=True, status=PlanProject.Status.ALLOCATED
            ).total_expense_amount()["expense_amount__sum"]
            or 0
        )

    def assign_project_car_numbers(self) -> None:
        capital_number = 0
        expense_number = 699
        plan_projects = self.planproject_set.select_related(
            "project", "project__location", "project__primary_division"
        ).filter(
            status__in=[PlanProject.Status.SUBMITTED, PlanProject.Status.ALLOCATED]
        )
        for plan_project in plan_projects:
            plan_project: PlanProject
            if (
                self.year
                and not plan_project.project.car_number
                and plan_project.project.primary_division_id
                and plan_project.project.location_id
            ):
                division_code = DivisionCodeDict[
                    plan_project.project.primary_division.name
                ]
                year_code = str(self.year)[:2]
                location_code = plan_project.project.location.code or "ZZZ"
                with transaction.atomic():
                    while True:
                        if plan_project.capital_amount:
                            capital_number += 1
                            number_code = f"{capital_number:03}"
                        elif plan_project.expense_amount:
                            expense_number += 1
                            number_code = f"{expense_number:03}"
                        car_regex = (
                            rf"{division_code}-{year_code}-\w{{3}}-{number_code}"
                        )
                        if not Project.objects.filter(
                            car_number__iregex=car_regex
                        ).exists():
                            car_number = f"{division_code}-{year_code}-{location_code}-{number_code}"
                            plan_project.project.car_number = car_number
                            plan_project.project.save()
                            break

    def can_user_rank_or_evaluate_projects(self, user: User):
        return self.phase in user.get_modifiable_phases_for_user()

    @hook(BEFORE_SAVE)
    def set_status(self):
        if self.status:
            return
        year = date.today().year
        if self.year < year:
            self.status = self.Status.PAST
        elif self.year == year:
            self.status = self.Status.CURRENT
        elif self.year == year + 1:
            self.status = self.Status.NEXT
        else:
            self.status = self.Status.FUTURE

    def set_phase(self):
        if self.status in [self.Status.PAST, self.Status.CURRENT]:
            self.phase = self.Phase.ALLOCATED
        elif (
            self.status == self.Status.NEXT
            and self.phase == self.Phase.NOT_STARTED
            and self.get_start_date()
            and self.get_start_date() <= date.today()
        ):
            self.phase = self.Phase.LOCATIONS_RANK
        else:
            self.phase = self.Phase.NOT_STARTED

    @hook(AFTER_SAVE)
    def apply_status_constraints(self):
        if self.status == self.Status.CURRENT:
            self.__class__.objects.set_as_current(plan=self)
        elif self.status == self.Status.NEXT:
            self.__class__.objects.set_as_next(plan=self)

    @hook(AFTER_SAVE, when="phase", has_changed=True)
    def set_ranks_for_phase_change(self):
        rank_field = PlanProject.get_rank_field(self.phase)
        plan_projects = (
            self.planproject_set.with_scores()
            .filtered_eligible_to_allocate()
            .order_by(F("global_score").asc(nulls_last=True), "created")
        )
        plan_projects.generate_ranks(rank_field)

    @hook(AFTER_SAVE)
    def set_allocations(self):
        if (
            not self.capital_budget
            or self.status != self.Status.NEXT
            or self.phase not in [Plan.Phase.SVP_RANK, Plan.Phase.EXECUTIVE_ADJUSTMENT]
        ):
            self.planproject_set.with_has_carry_in().filtered_included().filter(
                has_carry_in=False
            ).filter(
                status__in=[PlanProject.Status.ALLOCATED, PlanProject.Status.WITHHELD]
            ).update(status=PlanProject.Status.SUBMITTED)
            return

        plan_projects = list(
            self.planproject_set.with_has_carry_in()
            .with_scores()
            .filtered_eligible_to_allocate()
            .order_by(F("global_score").asc(nulls_last=True), "created")
        )
        amount_left = self.capital_budget
        has_budget = True
        rank = 0
        for index, plan_project in enumerate(plan_projects):
            amount_requested = plan_project.capital_amount or 0
            has_budget = has_budget and (amount_left - amount_requested) >= 0
            if has_budget:
                plan_projects[index].status = PlanProject.Status.ALLOCATED
                amount_left = amount_left - amount_requested
            else:
                plan_projects[index].status = PlanProject.Status.WITHHELD
            if not plan_project.has_carry_in:
                rank = rank + 1
            plan_projects[index].global_rank = (
                rank if not plan_project.has_carry_in else 0
            )
        PlanProject.objects.bulk_update(plan_projects, ["status", "global_rank"])


class PlanTimelineQuerySet(QuerySet):
    def with_phase_order(self) -> QuerySet:
        phase_whens = [
            When(phase=PlanTimeline.Phase.LOCATIONS_RANK, then=Value(1)),
            When(
                phase=PlanTimeline.Phase.OPERATIONS_NEED_AND_RISK_FACTOR, then=Value(2)
            ),
            When(phase=PlanTimeline.Phase.VP_RANK, then=Value(3)),
            When(phase=PlanTimeline.Phase.SVP_RANK, then=Value(4)),
            When(phase=PlanTimeline.Phase.EXECUTIVE_ADJUSTMENT, then=Value(5)),
        ]
        return self.annotate(
            phase_order=Case(
                *phase_whens, default=Value(10), output_field=models.IntegerField()
            )
        )


class PlanTimeline(auto_prefetch.Model):
    class Phase(models.TextChoices):
        LOCATIONS_RANK = "locations_rank", "Locations Rank"
        OPERATIONS_NEED_AND_RISK_FACTOR = (
            "operations_need_and_risk_factor",
            "Operations Need & Risk Factor",
        )
        VP_RANK = "vp_rank", "VP Rank"
        SVP_RANK = "svp_rank", "SVP Rank"
        EXECUTIVE_ADJUSTMENT = "executive_adjustment", "Executive Adjustment"

    plan = auto_prefetch.ForeignKey(Plan, on_delete=models.CASCADE)
    phase = models.CharField(max_length=50, choices=Phase.choices)
    start_date = models.DateField()
    end_date = models.DateField()

    class Meta(auto_prefetch.Model.Meta):
        unique_together = ["plan", "phase"]

    objects = PlanTimelineQuerySet.as_manager()

    def __str__(self):
        return f"{self.get_phase_display()}: {self.start_date:%m/%d/%Y } - {self.end_date:%m/%d/%Y }"

    def has_passed(self) -> bool:
        return date.today() > self.end_date

    def is_current(self) -> bool:
        return self.start_date <= date.today() <= self.end_date

    def is_future(self) -> bool:
        return date.today() < self.start_date

    @property
    def date_range(self) -> str:
        return format_date_range(
            self.start_date,
            self.end_date,
            day_code="%-d",
            month_code="%-m",
            year_code="%Y",
            show_year=False,
        )

    @property
    def pretty_date_range(self) -> str:
        return format_date_range(
            self.start_date,
            self.end_date,
            day_code="%-d",
            month_code="%B",
            year_code="%Y",
            show_year=False,
        )

    def clean(self) -> None:
        if self.start_date and self.end_date and self.start_date >= self.end_date:
            raise ValidationError(
                {"end_date": "Cannot set end date before start date."}
            )

    @classmethod
    def restructure_dates(cls, timelines: List["PlanTimeline"]) -> List[str]:
        """
        Generate HTML calendars for all months covered by the given timelines.

        Args:
            timelines: A collection of `PlanTimeline` objects.

        Returns:
            A list of HTML strings representing monthly calendars
            with color-coded days based on phase.
        """
        months_set: Set[Tuple[int, int]] = set()
        for timeline in timelines:
            months_set.add((timeline.start_date.year, timeline.start_date.month))
            months_set.add((timeline.end_date.year, timeline.end_date.month))

        sorted_months: List[Tuple[int, int]] = sorted(months_set)
        calendars: List[str] = []

        for year, month in sorted_months:
            html = HTMLCalendar()
            html.setfirstweekday(SUNDAY)
            html.cssclass_month_head = "calendar-month Form-label"
            html.cssclass_noday = "u-white-background"
            html.cssclasses_weekday_head = ["calendar-weekdays u-white-background"] * 7
            html.cssclasses = ["dummy-class u-white-background "] * 7

            html_month = html.formatmonth(year, month, withyear=False)

            # Color days based on timeline phase
            for timeline in timelines:
                for day in cls.get_date_data(timeline, month):
                    if day.month == month:
                        html_date = html.formatday(day.day, day.isoweekday() - 1)
                        colored = cls.color_day(html_date, timeline.phase)
                        html_month = html_month.replace(html_date, colored)

            html_month = cls.rename_days(html_month)
            calendars.append(html_month)

        return calendars

    @staticmethod
    def get_date_data(plan_timeline: "PlanTimeline", month_index: int) -> List[date]:
        """
        Return a list of date objects for the given timeline and month.

        Args:
            plan_timeline: A single `PlanTimeline` instance.
            month_index: The month to extract days for (1–12).

        Returns:
            A list of dates included in this phase for the given month.
        """
        days: List[date] = []
        if (
            plan_timeline.end_date.month == month_index
            or plan_timeline.start_date.month == month_index
        ):
            delta = plan_timeline.end_date - plan_timeline.start_date
            for i in range(delta.days + 1):
                day = plan_timeline.start_date + timedelta(days=i)
                days.append(day)
        return days

    @staticmethod
    def color_day(html_date: str, phase: str) -> str:
        """
        Apply a CSS class to a date cell based on the plan phase.
        """
        css_map = {
            PlanTimeline.Phase.LOCATIONS_RANK: "location-rank",
            PlanTimeline.Phase.OPERATIONS_NEED_AND_RISK_FACTOR: "operations-need",
            PlanTimeline.Phase.VP_RANK: "vp-rank",
            PlanTimeline.Phase.SVP_RANK: "svp-rank",
            PlanTimeline.Phase.EXECUTIVE_ADJUSTMENT: "executive-adjustment",
        }
        css_class = css_map.get(phase, "dummy-class")
        return html_date.replace("dummy-class", css_class)

    @staticmethod
    def rename_days(html_month: str) -> str:
        """Shorten weekday names to single-letter labels."""
        return (
            html_month.replace("Mon", "M")
            .replace("Tue", "T")
            .replace("Wed", "W")
            .replace("Thu", "T")
            .replace("Fri", "F")
            .replace("Sat", "S")
            .replace("Sun", "S")
        )


class PlanProjectQuerySet(QuerySet):
    def filtered_included(self) -> QuerySet:
        return self.filter(project__active=True).exclude(
            capital_amount__isnull=True, expense_amount__isnull=True
        )

    def filtered_viewable(self) -> QuerySet:
        return self.filtered_included().filter(
            status__in=[
                PlanProject.Status.SUBMITTED,
                PlanProject.Status.ALLOCATED,
                PlanProject.Status.WITHHELD,
                PlanProject.Status.REJECTED,
            ]
        )

    def filtered_eligible_to_allocate(self) -> QuerySet:
        return self.filtered_included().filter(
            status__in=[
                PlanProject.Status.SUBMITTED,
                PlanProject.Status.ALLOCATED,
                PlanProject.Status.WITHHELD,
            ]
        )

    def filtered_rankable(self) -> QuerySet:
        return (
            self.with_has_carry_in()
            .with_is_expense_only()
            .filtered_eligible_to_allocate()
            .filter(has_carry_in=False)
            .filter(is_expense_only=False)
        )

    def filtered_user_rankable(self, user: User) -> QuerySet:
        return self.filtered_viewable().filtered_user_modify_access(user)

    def with_length_in_years(self) -> QuerySet:
        return self.annotate(
            length_in_years=Subquery(
                PlanProject.objects.filter(
                    project_id=OuterRef("project_id"),
                    status__in=[
                        PlanProject.Status.SUBMITTED,
                        PlanProject.Status.ALLOCATED,
                    ],
                )
                .exclude(capital_amount__isnull=True, expense_amount__isnull=True)
                .values("project_id")
                .annotate(cnt=Count("*"))
                .values("cnt")[:1],
                output_field=models.IntegerField(),
            )
        )

    def with_has_carry_in(self) -> QuerySet:
        return self.annotate(
            has_carry_in=Exists(
                PlanProject.objects.exclude(id=OuterRef("id")).filter(
                    project_id=OuterRef("project_id"),
                    status=PlanProject.Status.ALLOCATED,
                    plan__status=Plan.Status.CURRENT,
                )
            )
        )

    def with_is_expense_only(self) -> QuerySet:
        return self.annotate(
            is_expense_only=Case(
                When(
                    capital_amount__isnull=True, expense_amount__gt=0, then=Value(True)
                ),
                When(capital_amount=0, expense_amount__gt=0, then=Value(True)),
                default=Value(False),
                output_field=models.BooleanField(),
            )
        )

    def with_current_rank(self) -> QuerySet:
        whens = [
            When(has_carry_in=True, then=Value(CARRY_IN_RANK)),
            When(status=PlanProject.Status.REJECTED, then=Value(REJECTED_RANK)),
            When(is_expense_only=True, then=Value(EXPENSE_ONLY_RANK)),
            When(executive_rank__isnull=False, then="executive_rank"),
            When(svp_rank__isnull=False, then="svp_rank"),
            When(vp_rank__isnull=False, then="vp_rank"),
            When(location_rank__isnull=False, then="location_rank"),
        ]
        return (
            self.with_has_carry_in()
            .with_is_expense_only()
            .annotate(
                current_rank=Case(
                    *whens, default=Value(0), output_field=models.PositiveIntegerField()
                )
            )
        )

    def with_scores(self) -> QuerySet:
        location_whens = [
            When(project__location__location_size=SizeChoices.XLARGE, then=Value(1)),
            When(project__location__location_size=SizeChoices.LARGE, then=Value(2)),
            When(project__location__location_size=SizeChoices.MEDIUM, then=Value(3)),
            When(project__location__location_size=SizeChoices.SMALL, then=Value(4)),
        ]

        category_whens = [
            When(
                project__strategic_pillars__name__in=[
                    "Continuous Improvement",
                    "Organic Growth",
                    "New Plant Capacity",
                    "Strategy Pivot",
                ],
                then=Value(1.25),
            ),
            When(project__strategic_pillars__name="Regulatory", then=Value(1.5)),
            When(project__strategic_pillars__name="Sustainability", then=Value(1.75)),
            When(project__strategic_pillars__name="Replacement", then=Value(2)),
        ]

        cost_whens = [
            When(project__capital_budget_amount__lt=200_000, then=Value(1)),
            When(
                project__capital_budget_amount__range=(200_000, 499_999), then=Value(2)
            ),
            When(
                project__capital_budget_amount__range=(500_000, 999_999), then=Value(3)
            ),
            When(
                project__capital_budget_amount__range=(1_000_000, 4_999_999),
                then=Value(4),
            ),
            When(project__capital_budget_amount__gte=5_000_000, then=Value(5)),
        ]

        need_whens = [
            When(
                project__projectoperationsneed__need_rating=ProjectOperationsNeed.Need.LOW,
                then=Value(1),
            ),
            When(
                project__projectoperationsneed__need_rating=ProjectOperationsNeed.Need.MEDIUM,
                then=Value(2),
            ),
            When(
                project__projectoperationsneed__need_rating=ProjectOperationsNeed.Need.HIGH,
                then=Value(3),
            ),
        ]

        technology_risk_whens = [
            When(
                project__projectrisk__technology_risk_rating=ProjectRisk.Risk.LOW,
                then=Value(1),
            ),
            When(
                project__projectrisk__technology_risk_rating=ProjectRisk.Risk.MEDIUM,
                then=Value(2),
            ),
            When(
                project__projectrisk__technology_risk_rating=ProjectRisk.Risk.HIGH,
                then=Value(3),
            ),
        ]
        site_capacity_risk_whens = [
            When(
                project__projectrisk__site_capacity_risk_rating=ProjectRisk.Risk.LOW,
                then=Value(1),
            ),
            When(
                project__projectrisk__site_capacity_risk_rating=ProjectRisk.Risk.MEDIUM,
                then=Value(2),
            ),
            When(
                project__projectrisk__site_capacity_risk_rating=ProjectRisk.Risk.HIGH,
                then=Value(3),
            ),
        ]
        product_risk_whens = [
            When(
                project__projectrisk__product_risk_rating=ProjectRisk.Risk.LOW,
                then=Value(1),
            ),
            When(
                project__projectrisk__product_risk_rating=ProjectRisk.Risk.MEDIUM,
                then=Value(2),
            ),
            When(
                project__projectrisk__product_risk_rating=ProjectRisk.Risk.HIGH,
                then=Value(3),
            ),
        ]
        savings_risk_whens = [
            When(
                project__projectrisk__savings_risk_rating=ProjectRisk.Risk.LOW,
                then=Value(1),
            ),
            When(
                project__projectrisk__savings_risk_rating=ProjectRisk.Risk.MEDIUM,
                then=Value(2),
            ),
            When(
                project__projectrisk__savings_risk_rating=ProjectRisk.Risk.HIGH,
                then=Value(3),
            ),
        ]
        project_complexity_risk_whens = [
            When(
                project__projectrisk__project_complexity_risk_rating=ProjectRisk.Risk.LOW,
                then=Value(1),
            ),
            When(
                project__projectrisk__project_complexity_risk_rating=ProjectRisk.Risk.MEDIUM,
                then=Value(2),
            ),
            When(
                project__projectrisk__project_complexity_risk_rating=ProjectRisk.Risk.HIGH,
                then=Value(3),
            ),
        ]

        risk_whens = [
            When(
                risk_average_score__lt=1.5,
                then=Value(1),
            ),
            When(
                risk_average_score__gte=1.5,
                risk_average_score__lte=2.5,
                then=Value(2),
            ),
            When(
                risk_average_score__gt=2.5,
                then=Value(3),
            ),
        ]

        return self.with_current_rank().annotate(
            location_score=Case(
                *location_whens,
                default=Value(4),
                output_field=models.PositiveIntegerField(),
            ),
            category_score=Case(
                *category_whens, default=Value(1), output_field=models.FloatField()
            ),
            cost_score=Case(
                *cost_whens,
                default=Value(5),
                output_field=models.PositiveIntegerField(),
            ),
            need_score=Case(
                *need_whens,
                default=Value(3),
                output_field=models.PositiveIntegerField(),
            ),
            technology_risk_score=Case(
                *technology_risk_whens,
                default=Value(3),
                output_field=models.PositiveIntegerField(),
            ),
            site_capacity_risk_score=Case(
                *site_capacity_risk_whens,
                default=Value(3),
                output_field=models.PositiveIntegerField(),
            ),
            product_risk_score=Case(
                *product_risk_whens,
                default=Value(3),
                output_field=models.PositiveIntegerField(),
            ),
            savings_risk_score=Case(
                *savings_risk_whens,
                default=Value(3),
                output_field=models.PositiveIntegerField(),
            ),
            project_complexity_risk_score=Case(
                *project_complexity_risk_whens,
                default=Value(3),
                output_field=models.PositiveIntegerField(),
            ),
            risk_average_score=ExpressionWrapper(
                (
                    F("technology_risk_score")
                    + F("site_capacity_risk_score")
                    + F("product_risk_score")
                    + F("savings_risk_score")
                    + F("project_complexity_risk_score")
                )
                / 5,
                output_field=models.FloatField(),
            ),
            risk_score=Case(
                *risk_whens,
                default=Value(3),
                output_field=models.PositiveIntegerField(),
            ),
            project_score=ExpressionWrapper(
                F("location_score") * F("category_score") * 1.75
                + F("cost_score")
                + (F("need_score") + F("risk_score")) * 2.75,
                output_field=models.FloatField(),
            ),
            global_score=ExpressionWrapper(
                F("project_score") + F("current_rank") * 1.75,
                output_field=models.FloatField(),
            ),
        )

    def generate_ranks(self, rank_field: str) -> QuerySet:
        queryset = self.filtered_rankable()
        for rank, plan_project in enumerate(queryset, start=1):
            setattr(plan_project, rank_field, rank)
            plan_project.save()
        return self

    def defragment_ranks(self, rank_field: str) -> QuerySet:
        queryset = self.filtered_rankable().order_by(rank_field)
        plan_projects = []
        for rank, plan_project in enumerate(queryset, start=1):
            setattr(plan_project, rank_field, rank)
            plan_projects.append(plan_project)
        self.bulk_update(plan_projects, [rank_field])
        return self

    def total_capital_amount(self) -> Dict[str, int]:
        return self.filter(capital_amount__isnull=False).aggregate(
            Sum("capital_amount")
        )

    def with_previous_year_capital_request(self):
        return self.annotate(
            previous_year_capital_request=Subquery(
                PlanProject.objects.filter(
                    project_id=OuterRef("project_id"),
                    plan__year=OuterRef("plan__year") - 1,
                    status=PlanProject.Status.ALLOCATED,
                ).values("capital_amount")[:1]
            )
        )

    def with_future_year_capital_request(self):
        return self.annotate(
            future_year_capital_request=Subquery(
                PlanProject.objects.filter(
                    project_id=OuterRef("project_id"),
                    plan__year=OuterRef("plan__year") + 1,
                    status=PlanProject.Status.SUBMITTED,
                ).values("capital_amount")[:1]
            )
        )

    def with_remaining_capital_budget_amount(self):
        return self.annotate(
            remaining_capital_budget_amount=F("project__capital_budget_amount")
            - F("project__capital_actuals")
        )

    def with_previous_year_expense_request(self):
        return self.annotate(
            previous_year_expense_request=Subquery(
                PlanProject.objects.filter(
                    project_id=OuterRef("project_id"),
                    plan__year=OuterRef("plan__year") - 1,
                    status=PlanProject.Status.ALLOCATED,
                ).values("expense_amount")[:1]
            )
        )

    def with_future_year_expense_request(self):
        return self.annotate(
            future_year_expense_request=Subquery(
                PlanProject.objects.filter(
                    project_id=OuterRef("project_id"),
                    plan__year=OuterRef("plan__year") + 1,
                    status=PlanProject.Status.SUBMITTED,
                ).values("expense_amount")[:1]
            )
        )

    def with_remaining_expense_budget_amount(self):
        return self.annotate(
            remaining_expense_budget_amount=F("project__expense_budget_amount")
            - F("project__opex_actuals")
        )

    def total_capital_carry_in(self) -> Dict[str, int]:
        return (
            self.with_has_carry_in()
            .filter(has_carry_in=True)
            .filter(capital_amount__isnull=False)
            .aggregate(Sum("capital_amount"))
        )

    def total_capital_allocated(self) -> Dict[str, int]:
        return (
            self.filter(capital_amount__isnull=False)
            .filter(status=PlanProject.Status.ALLOCATED)
            .aggregate(Sum("capital_amount"))
        )

    def total_expense_allocated(self) -> Dict[str, int]:
        return (
            self.filter(expense_amount__isnull=False)
            .filter(status=PlanProject.Status.ALLOCATED)
            .aggregate(Sum("expense_amount"))
        )

    def total_capital_roi_amount(self) -> Dict[str, int]:
        return (
            self.filter(capital_amount__isnull=False)
            .exclude(
                project__strategic_pillars__name__in=[
                    "Replacement",
                    "Regulatory",
                    "Sustainability",
                ]
            )
            .aggregate(Sum("capital_amount"))
        )

    def total_capital_roi_allocated(self) -> Dict[str, int]:
        return (
            self.filter(capital_amount__isnull=False)
            .exclude(
                project__strategic_pillars__name__in=[
                    "Replacement",
                    "Regulatory",
                    "Sustainability",
                ]
            )
            .filter(status=PlanProject.Status.ALLOCATED)
            .aggregate(Sum("capital_amount"))
        )

    def total_capital_roi_carry_in(self) -> Dict[str, int]:
        return (
            self.with_has_carry_in()
            .filter(has_carry_in=True)
            .filter(capital_amount__isnull=False)
            .exclude(
                project__strategic_pillars__name__in=[
                    "Replacement",
                    "Regulatory",
                    "Sustainability",
                ]
            )
            .aggregate(Sum("capital_amount"))
        )

    def total_capital_rrns_amount(self) -> Dict[str, int]:
        return (
            self.filter(capital_amount__isnull=False)
            .filter(
                project__strategic_pillars__name__in=[
                    "Replacement",
                    "Regulatory",
                    "Sustainability",
                ]
            )
            .aggregate(Sum("capital_amount"))
        )

    def total_capital_rrns_allocated(self) -> Dict[str, int]:
        return (
            self.filter(capital_amount__isnull=False)
            .filter(
                project__strategic_pillars__name__in=[
                    "Replacement",
                    "Regulatory",
                    "Sustainability",
                ]
            )
            .filter(status=PlanProject.Status.ALLOCATED)
            .aggregate(Sum("capital_amount"))
        )

    def total_capital_rrns_carry_in(self) -> Dict[str, int]:
        return (
            self.with_has_carry_in()
            .filter(has_carry_in=True)
            .filter(capital_amount__isnull=False)
            .filter(
                project__strategic_pillars__name__in=[
                    "Replacement",
                    "Regulatory",
                    "Sustainability",
                ]
            )
            .aggregate(Sum("capital_amount"))
        )

    def with_total_capital_amount(self):
        return self.annotate(
            capital_amount_total=Subquery(
                PlanProject.objects.filter(project_id=OuterRef("project_id"))
            ).aggregate(Sum("capital_amount"))
        )

    def with_total_expense_amount(self):
        pass

    def total_expense_amount(self) -> Dict[str, int]:
        return self.filter(expense_amount__isnull=False).aggregate(
            Sum("expense_amount")
        )

    def total_expense_only_amount(self) -> Dict[str, int]:
        return (
            self.filter(expense_amount__isnull=False)
            .filter(Q(capital_amount__isnull=True) | Q(capital_amount=0))
            .aggregate(Sum("expense_amount"))
        )

    def total_expense_with_capital_amount(self) -> Dict[str, int]:
        return (
            self.filter(expense_amount__isnull=False)
            .filter(capital_amount__gt=0)
            .aggregate(Sum("expense_amount"))
        )


class PlanProject(LifecycleModel, auto_prefetch.Model):
    class Status(models.TextChoices):
        INCOMPLETE = "incomplete", "Draft"
        SUBMITTED = "submitted", "Submitted"
        ALLOCATED = "allocated", "Allocated"
        WITHHELD = "withheld", "Returned"
        REJECTED = "rejected", "Rejected"

    plan = auto_prefetch.ForeignKey(Plan, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    capital_amount = models.BigIntegerField(blank=True, null=True)  # dollars
    expense_amount = models.BigIntegerField(blank=True, null=True)  # dollars
    status = models.CharField(max_length=50, choices=Status.choices)
    global_rank = models.PositiveIntegerField(blank=True, null=True)
    location_rank = models.PositiveIntegerField(blank=True, null=True)
    vp_rank = models.PositiveIntegerField(blank=True, null=True)
    svp_rank = models.PositiveIntegerField(blank=True, null=True)
    executive_rank = models.PositiveIntegerField(blank=True, null=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    attachments = GenericRelation(Attachment)
    comments = GenericRelation(Comment)
    links = GenericRelation(Link)

    class Meta(auto_prefetch.Model.Meta):
        indexes = [
            models.Index(fields=["status"]),
            models.Index(fields=["location_rank"]),
            models.Index(fields=["vp_rank"]),
            models.Index(fields=["svp_rank"]),
            models.Index(fields=["executive_rank"]),
        ]
        unique_together = ["plan", "project"]

    objects = PlanProjectQuerySet.as_manager()

    def __str__(self):
        return f"{self.plan}: {self.project}"

    def get_absolute_url(self):
        return reverse("planner_detail_project", kwargs={"pk": self.project.pk})

    @property
    def serialized_attachments(self) -> List[Dict[str, Any]]:
        """
        Returns a serialized list of attachments for this plan project.
        """
        from apps.attachments.serializers import AttachmentSerializer

        return AttachmentSerializer(self.attachments.all(), many=True).data

    @property
    def serialized_links(self) -> List[Dict[str, Any]]:
        """
        Returns a serialized list of links for this plan project.
        """
        from apps.attachments.serializers import LinkSerializer

        return LinkSerializer(self.links.all(), many=True).data

    @staticmethod
    def get_rank_field(phase: Plan.Phase) -> str:
        if phase == Plan.Phase.NOT_STARTED:
            return "location_rank"
        elif phase == Plan.Phase.LOCATIONS_RANK:
            return "location_rank"
        elif phase == Plan.Phase.VP_RANK:
            return "vp_rank"
        elif phase == Plan.Phase.SVP_RANK:
            return "svp_rank"
        elif phase == Plan.Phase.EXECUTIVE_ADJUSTMENT:
            return "executive_rank"
        else:
            return ""

    @staticmethod
    def set_rank(
        queryset: QuerySet["PlanProject"], plan_project: "PlanProject", rank: int
    ) -> "PlanProject":
        if queryset.exclude(plan=plan_project.plan).exists():
            raise Exception(f"Received a list with plans not in {plan_project.plan}.")
        rank_field = PlanProject.get_rank_field(plan_project.plan.get_phase())
        current_rank = getattr(plan_project, rank_field) or LOWEST_RANK
        rank = min(
            queryset.count() + 1, max(1, rank)
        )  # clamp rank from 1 to list length
        if rank < current_rank:
            filter_kwargs = {
                f"{rank_field}__gte": rank,
                f"{rank_field}__lt": current_rank,
            }
            update_kwargs = {f"{rank_field}": F(rank_field) + 1}
            queryset.filter(plan=plan_project.plan).filter(
                status__in=[
                    PlanProject.Status.SUBMITTED,
                    PlanProject.Status.ALLOCATED,
                    PlanProject.Status.WITHHELD,
                ]
            ).filter(**filter_kwargs).update(**update_kwargs)
        elif rank > current_rank:
            filter_kwargs = {
                f"{rank_field}__gt": current_rank,
                f"{rank_field}__lte": rank,
            }
            update_kwargs = {f"{rank_field}": F(rank_field) - 1}
            queryset.filter(plan=plan_project.plan).filter(
                status__in=[
                    PlanProject.Status.SUBMITTED,
                    PlanProject.Status.ALLOCATED,
                    PlanProject.Status.WITHHELD,
                ]
            ).filter(**filter_kwargs).update(**update_kwargs)
        setattr(plan_project, rank_field, rank)
        if plan_project.status == PlanProject.Status.REJECTED:
            plan_project.status = PlanProject.Status.SUBMITTED
        print(["set rank", getattr(plan_project, rank_field)])
        plan_project.save()
        # queryset.defragment_ranks(rank_field=rank_field)
        plan_project.plan.set_allocations()
        return plan_project

    @staticmethod
    def move_rank_up(
        queryset: QuerySet["PlanProject"], plan_project: "PlanProject"
    ) -> "PlanProject":
        rank_field = PlanProject.get_rank_field(plan_project.plan.get_phase())
        rank = getattr(plan_project, rank_field) or LOWEST_RANK
        return PlanProject.set_rank(queryset, plan_project, rank - 1)

    @staticmethod
    def move_rank_down(
        queryset: QuerySet["PlanProject"], plan_project: "PlanProject"
    ) -> "PlanProject":
        rank_field = PlanProject.get_rank_field(plan_project.plan.get_phase())
        rank = getattr(plan_project, rank_field) or LOWEST_RANK
        return PlanProject.set_rank(queryset, plan_project, rank + 1)

    @property
    def expense_only(self) -> bool:
        has_capital = self.capital_amount is not None and self.capital_amount > 0
        has_expense = self.expense_amount is not None and self.expense_amount > 0
        return not has_capital and has_expense

    def ready_to_submit(self) -> bool:
        required_fields = ["name", "location_id"]
        has_capital = self.capital_amount and self.capital_amount > 0
        has_expense = self.expense_amount and self.expense_amount > 0
        has_fields_complete = self.project and all(
            (getattr(self.project, field) is not None for field in required_fields)
        )
        return (has_capital or has_expense) and has_fields_complete

    def _has_carry_in(self) -> bool:
        if hasattr(self, "has_carry_in"):
            return self.has_carry_in
        else:
            return (
                PlanProject.objects.exclude(id=self.id)
                .filter(
                    project_id=self.project_id,
                    status=PlanProject.Status.ALLOCATED,
                    plan__status=Plan.Status.CURRENT,
                )
                .exists()
            )

    def get_current_rank_display(self) -> str:
        if self.current_rank is None:
            return "-"
        elif self.current_rank == CARRY_IN_RANK:
            return "C"
        elif self.current_rank == EXPENSE_ONLY_RANK:
            return "E"
        elif self.current_rank == REJECTED_RANK:
            return "R"
        else:
            return self.current_rank

    def get_current_rank_type(self) -> str:
        if self.current_rank == CARRY_IN_RANK:
            return "Carry-In"
        elif self.current_rank == EXPENSE_ONLY_RANK:
            return "Expense Only"
        elif self.current_rank == REJECTED_RANK:
            return "Rejected"
        else:
            return "New Capital"

    def can_user_rank(self, user: User) -> bool:
        return (
            self.project.active
            and self.status
            in [
                PlanProject.Status.SUBMITTED,
                PlanProject.Status.ALLOCATED,
                PlanProject.Status.WITHHELD,
                PlanProject.Status.REJECTED,
            ]
            and self.plan.phase in Plan.RANKING_PHASES
            and not self.is_expense_only
            and not self.has_carry_in
            and self.plan.can_user_rank_or_evaluate_projects(user)
        )

    def can_user_evaluate_need(self, user: User) -> bool:
        return (
            self.plan.phase == Plan.Phase.OPERATIONS_NEED_AND_RISK_FACTOR
            and self.project.active
            and self.status == PlanProject.Status.SUBMITTED
            and not self.expense_only
            and not self._has_carry_in()
            and self.project.location.operations_director.email == user.email
        )

    def can_user_evaluate_risk(self, user: User) -> bool:
        return (
            self.plan.phase == Plan.Phase.OPERATIONS_NEED_AND_RISK_FACTOR
            and self.project.active
            and self.status == PlanProject.Status.SUBMITTED
            and not self.expense_only
            and not self._has_carry_in()
            and self.project.location.engineering_director.email == user.email
        )

    @hook(BEFORE_SAVE)
    def set_ranks(self) -> None:
        initial_rank = 0
        phase = self.plan.phase
        if (
            self.status == self.Status.REJECTED
            or self.expense_only
            or self._has_carry_in()
        ):
            self.location_rank = None
            self.vp_rank = None
            self.svp_rank = None
            self.executive_rank = None
        elif phase in [
            Plan.Phase.NOT_STARTED,
            Plan.Phase.LOCATIONS_RANK,
            Plan.Phase.OPERATIONS_NEED_AND_RISK_FACTOR,
        ]:
            self.location_rank = self.location_rank or initial_rank
            self.vp_rank = None
            self.svp_rank = None
            self.executive_rank = None
        elif phase == Plan.Phase.VP_RANK:
            self.location_rank = self.location_rank or initial_rank
            self.vp_rank = self.vp_rank or initial_rank
            self.svp_rank = None
            self.executive_rank = None
        elif phase == Plan.Phase.SVP_RANK:
            self.location_rank = self.location_rank or initial_rank
            self.vp_rank = self.vp_rank or initial_rank
            self.svp_rank = self.svp_rank or initial_rank
            self.executive_rank = None
        elif phase in [Plan.Phase.EXECUTIVE_ADJUSTMENT, Plan.Phase.ALLOCATED]:
            self.location_rank = self.location_rank or initial_rank
            self.vp_rank = self.vp_rank or initial_rank
            self.svp_rank = self.svp_rank or initial_rank
            self.executive_rank = self.executive_rank or initial_rank

    @hook(BEFORE_SAVE)
    def set_status(self) -> None:
        if self.status and self.status != self.Status.INCOMPLETE:
            return
        today = date.today()
        end_date = self.plan.get_end_date()
        if self._has_carry_in():
            self.status = self.Status.ALLOCATED
        elif not self.ready_to_submit():
            self.status = self.Status.INCOMPLETE
        elif (
            self.status is None or self.status == self.Status.INCOMPLETE
        ) and self.ready_to_submit():
            self.status = self.Status.SUBMITTED
        elif end_date and today <= end_date:
            self.status = self.Status.SUBMITTED
        elif end_date and today > end_date:
            self.status = self.Status.ALLOCATED
        else:
            self.status = self.Status.SUBMITTED

    def reject(self):
        self.status = self.Status.REJECTED
        self.save()
