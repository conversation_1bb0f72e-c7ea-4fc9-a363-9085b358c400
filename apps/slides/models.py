from io import BytesIO

import humanize
from django.contrib.humanize.templatetags.humanize import intcomma
from django.contrib.staticfiles import finders
from django.db import models
from lxml import etree, html
from pptx import Presentation
from pptx.dml.color import RGBColor
from pptx.util import Pt

from apps.ideas.forms import EXPENDITURE_TYPE_CAPITAL, EXPENDITURE_TYPE_OPEX
from apps.projects.models import Project, StrategicPillar
from apps.projects.models.raid import RiskStatusOption, StatusOptions
import auto_prefetch


def format_number(value):
    number = abs(value)
    formatted_value = intcomma(number)
    if value < 0:
        formatted_value = f"({formatted_value})"
    return formatted_value


def format_money(value):
    amount = abs(value)
    formatted_value = f"${intcomma(amount)}"
    if value < 0:
        formatted_value = f"({formatted_value})"
    return formatted_value


def format_words(run, element):
    if element is None:
        return
    if element.tag in ["b", "strong"]:
        run.font.bold = True
    elif element.tag in ["i", "em"]:
        run.font.italic = True


def format_paragraph(paragraph, element, last_child=False):
    text = element.text or ""
    text = text.lstrip()

    tail = element.tail or ""
    if element.tag == "li":
        text = f"\u2022 {text}\n"
        if "ul" in [element.tag for element in element.getchildren()]:
            ul_tail = [
                element.tail for element in element.getchildren() if element.tag == "ul"
            ][0]
            tail = ul_tail or "" + "\t"
        if last_child:
            if len(element.getchildren()) == 0:
                tail = tail.replace("\t", "", 2)

    elif element.tag == "ul":
        text = ""
        tail = ""

    if text:
        run = paragraph.add_run()
        run.text = text
        format_words(run, element)
        if tail:
            tail = tail.replace("\t", "    ")
            run = paragraph.add_run()
            run.text = tail
            format_words(run, element.getparent())

    for idx, child in enumerate(element):
        last_child = idx == len(element) - 1
        format_paragraph(paragraph, child, last_child=last_child)


def format_text(paragraph, text):
    run = paragraph.add_run()
    run.text = text
    return run


def format_text_with_heading(paragraph, data):
    run = paragraph.add_run()
    run.text = data["action"] + "\n" + data["value"]


def format_list_items(shape, values):
    if not values:
        return
    text_frame = shape.text_frame
    text_frame.clear()
    for index, element in enumerate(values):
        paragraph = (
            text_frame.paragraphs[0] if index == 0 else text_frame.add_paragraph()
        )
        element = element.replace("\r", "").replace("\n", " ")
        format_text(paragraph, element)


def format_list_items_with_actions(shape, values):
    if not values:
        return
    text_frame = shape.text_frame
    text_frame.clear()
    for index, element in enumerate(values):
        paragraph = (
            text_frame.paragraphs[0] if index == 0 else text_frame.add_paragraph()
        )
        # the action title comes first and is bold.

        heading = format_text(paragraph, f"{element['action'].title()}\n")
        heading.font.bold = True
        text = "\n".join(element["value"])
        format_text(paragraph, f"{text}\n")


def format_text_box(shape, text):
    # rich text editor adds this in place of \n for some reason...
    text = text.replace("\r\n\r\n", "\r\n")

    if not text:
        return
    text_frame = shape.text_frame
    text_frame.clear()
    try:
        doc = html.fragment_fromstring(
            text.replace("\n", "").replace("\r", ""), create_parent=True
        )
        # children = doc.cssselect('p,ul,ol')
        children = doc.getchildren()
        if not len(children):
            format_paragraph(text_frame.paragraphs[0], doc)
        for index, element in enumerate(children):
            paragraph = (
                text_frame.paragraphs[0] if index == 0 else text_frame.add_paragraph()
            )
            format_paragraph(paragraph, element)
    except etree.ParseError:
        text_frame.text = text


def format_people_box(shape, text):
    # rich text editor adds this in place of \n for some reason...
    text = text.replace("\r\n\r\n", "\r\n")
    if not text:
        return
    text_frame = shape.text_frame
    text_frame.clear()
    try:
        doc = html.fragment_fromstring(
            text.replace("\n", "").replace("\r", ""), create_parent=True
        )
        # children = doc.cssselect('p,ul,ol')
        # children = doc.getchildren()
        # if not len(children):
        format_paragraph(text_frame.paragraphs[0], doc)
        # for index, element in enumerate(children):
        #     paragraph = text_frame.paragraphs[0] if index == 0 else text_frame.add_paragraph()
        #     format_paragraph(paragraph, element)
    except etree.ParseError:
        text_frame.text = text


def format_number_cell(shape, value):
    text_frame = shape.text_frame
    text_frame.clear()
    p = text_frame.paragraphs[0]
    run = p.add_run()
    run.text = format_number(value)
    if value < 0:
        run.font.color.rgb = RGBColor(0xC3, 0x4B, 0x2C)


def format_money_cell(shape, value):
    text_frame = shape.text_frame
    text_frame.clear()
    p = text_frame.paragraphs[0]
    run = p.add_run()
    run.text = format_money(value)
    if value < 0:
        run.font.color.rgb = RGBColor(0xC3, 0x4B, 0x2C)


def format_get_to_green_heading(shape, value):
    text_frame = shape.text_frame
    text_frame.clear()
    p = text_frame.paragraphs[0]
    run = p.add_run()
    run.text = value
    run.font.size = Pt(14)
    run.font.bold = True
    run.font.color.rgb = RGBColor(201, 91, 18)


def format_indicator_icon(shape, value):
    text_frame = shape.text_frame
    text_frame.clear()
    p = text_frame.paragraphs[0]
    run = p.add_run()
    run.text = "●"
    color = (238, 238, 238)
    if value:
        if value == "green":
            color = (86, 165, 23)
        elif value == "red":
            color = (210, 20, 20)
        elif value == "yellow":
            color = (209, 198, 92)
        else:
            color = (220, 220, 220)

    run.font.size = Pt(55)
    run.font.color.rgb = RGBColor(*color)


def format_strategic_pillar_icons(placeholders, placeholder_index, idea):
    STRATEGIC_PILLAR_ICONS_DICT = {
        "Continuous Improvement": "static/src/images/pptx_icons/continuous_improvement.png",
        "Mergers and Acquisitions": "static/src/images/pptx_icons/mergers_and_acquisitions.png",
        "New Plant Capacity": "static/src/images/pptx_icons/new_plant_capacity.png",
        "Organic Growth": "static/src/images/pptx_icons/organic_growth.png",
        "Strategy Pivot": "static/src/images/pptx_icons/strategic_value.png",
        "Replacement": "static/src/images/pptx_icons/replacement.png",
        "Regulatory": "static/src/images/pptx_icons/regulatory.png",
        "Sustainability": "static/src/images/pptx_icons/sustainability.png",
    }
    EMPTY_PILLAR_PLACEHOLDER = "static/src/images/pptx_icons/empty_pillar.png"
    strategic_pillars = [
        pillar.name
        for pillar in StrategicPillar.objects.order_by("name")
        .filter(subpillar__project=idea)
        .distinct()
    ]
    strategic_pillar_icons = [""] * 4
    strategic_pillar_icons[: len(strategic_pillars)] = strategic_pillars
    for index, pillar in enumerate(strategic_pillar_icons[:4]):
        icon = STRATEGIC_PILLAR_ICONS_DICT.get(pillar, EMPTY_PILLAR_PLACEHOLDER)
        placeholders[placeholder_index + index].insert_picture(icon)


def person_string(people):
    return ", ".join([person.full_name for person in people])


def make_people_string(project):
    # Order:
    # Executive Owner
    # Project Manager
    # Business Leads
    # Finance Lead
    # Business Analysts
    # IT&T Business Relationship Manager
    people_string = ""
    if project.executive_owners.all().count():
        people_string += "<b>Executive Owner: </b>" + person_string(
            project.executive_owners.all()
        )
        if (
            project.project_managers.all().count()
            or project.business_leads.all().count()
            or project.finance_leads.all().count()
            or project.business_analysts.all().count()
        ):
            people_string += "; "

    if project.project_managers.all().count():
        people_string += "<b>Project Manager: </b>" + person_string(
            project.project_managers.all()
        )
        if (
            project.business_leads.all().count()
            or project.finance_leads.all().count()
            or project.business_analysts.all().count()
        ):
            people_string += "; "

    if project.business_leads.all().count():
        people_string += "<b>Business Leads: </b>" + person_string(
            project.business_leads.all()
        )
        if (
            project.finance_leads.all().count()
            or project.business_analysts.all().count()
        ):
            people_string += "; "

    if project.finance_leads.all().count():
        people_string += "<b>Finance Lead: </b>" + person_string(
            project.finance_leads.all()
        )
        if project.business_analysts.all().count():
            people_string += "; "

    if project.business_analysts.all().count():
        people_string += "<b>Business Analysts: </b>" + person_string(
            project.business_analysts.all()
        )

    return people_string if people_string else " "


def generate_page_slide(presentation, project: Project):
    masters = [master for master in presentation.slide_masters]
    layouts = [layout for layout in masters[0].slide_layouts]

    layout = layouts[0]
    slide = presentation.slides.add_slide(layout)
    placeholders = [placeholder for placeholder in slide.placeholders]

    placeholders[0].text = project.name

    if project.projectrecentaccomplishment_set.count() > 0:
        format_list_items(
            placeholders[1],
            [
                accomplishment.text
                for accomplishment in project.projectrecentaccomplishment_set.filter(
                    add_to_slide=True
                )
            ],
        )
    else:
        placeholders[1].text = "No Accomplishments"

    placeholders[2].text = str(project.id)

    if project.phase:
        placeholders[3].text = f" {project.phase.capitalize()}"
    else:
        placeholders[3].text = "None"

    if project.percent_complete:
        placeholders[4].text = f"{project.percent_complete.percentage}% Complete"
    else:
        placeholders[4].text = " "

    capex_totals = project.get_capital_actuals_total()
    if capex_totals:
        placeholders[5].text = f"${humanize.intcomma(capex_totals)}"
    else:
        placeholders[5].text = "$0"

    if project.capital_expenditure:
        placeholders[
            6
        ].text = f"of ${humanize.intcomma(project.get_capital_forecast_total())}"
    else:
        placeholders[6].text = "$0"

    opex_actuals = project.get_operational_actuals_total()
    if opex_actuals:
        placeholders[7].text = f"${humanize.intcomma(opex_actuals)}"
    else:
        placeholders[7].text = "$0"

    if project.opex_expenditure:
        placeholders[
            8
        ].text = f"of ${humanize.intcomma(project.get_operational_forecast_total())}"
    else:
        placeholders[8].text = "$0"

    if project.projectplannedactivity_set.count() > 0:
        format_list_items(
            placeholders[9],
            [
                plannedactivity.text
                for plannedactivity in project.projectplannedactivity_set.filter(
                    add_to_slide=True
                )
            ],
        )
    else:
        placeholders[9].text = "No Activities"

    if project.projectexecutiveaction_set.count() > 0:
        actions = {}
        for executiveaction in project.projectexecutiveaction_set.filter(
            add_to_slide=True
        ):
            if executiveaction.action_display not in actions.keys():
                actions[executiveaction.action_display] = []
            action_text = executiveaction.text.replace("\n", " ").replace("\r", "")
            actions[executiveaction.action_display].append(action_text)

        format_list_items_with_actions(
            placeholders[10],
            [
                {"value": actions[executiveaction], "action": executiveaction}
                for executiveaction in actions.keys()
            ],
        )
    else:
        placeholders[10].text = "No Actions"

    if project.start_date:
        placeholders[11].text = project.start_date.strftime("%-m/%-d/%Y")
    else:
        placeholders[11].text = " "

    if project.end_date:
        placeholders[12].text = project.end_date.strftime("%-m/%-d/%Y")
    else:
        placeholders[12].text = " "

    format_indicator_icon(placeholders[15], "none")
    format_indicator_icon(placeholders[16], "none")
    format_indicator_icon(placeholders[17], "none")
    format_indicator_icon(placeholders[18], "none")

    if project.projecthealth_set.count():
        health = project.projecthealth_set.first()
        if health.health:
            format_indicator_icon(placeholders[15], health.health)
        if health.budget_health:
            format_indicator_icon(placeholders[16], health.budget_health)
        if health.schedule_health:
            format_indicator_icon(placeholders[17], health.schedule_health)
        if health.scope_health:
            format_indicator_icon(placeholders[18], health.scope_health)

    if project.get_to_green:
        format_text_box(placeholders[13], project.get_to_green)
    else:
        placeholders[13].text = "N/A"

    format_get_to_green_heading(placeholders[14], "Get to Green")
    format_text_box(placeholders[19], project.summary)

    format_people_box(placeholders[20], make_people_string(project))

    # Slide 2
    layouts = [layout for layout in masters[1].slide_layouts]
    layout2 = layouts[0]
    issues = project.raidissues_set.filter(status=StatusOptions.active)
    risks = project.raidrisk_set.filter(status=RiskStatusOption.active)
    if issues.count() or risks.count():
        slide2 = presentation.slides.add_slide(layout2)
        placeholders = [placeholder for placeholder in slide2.placeholders]

        placeholders[0].text = project.name
        placeholders[1].text = str(project.id)
        if project.phase:
            placeholders[2].text = f" {project.phase.capitalize()}"
        else:
            placeholders[2].text = "None"
        if project.percent_complete:
            placeholders[3].text = f"{project.percent_complete.percentage}% Complete"
        else:
            placeholders[3].text = " "
        placeholders[4].text = " "
        if project.start_date:
            placeholders[5].text = project.start_date.strftime("%-m/%-d/%Y")
        else:
            placeholders[5].text = " "

        if project.end_date:
            placeholders[6].text = project.end_date.strftime("%-m/%-d/%Y")
        else:
            placeholders[6].text = " "
        placeholders[7].text = " "

        # Issues paragraphs
        issues_shape = placeholders[4]
        text_frame = issues_shape.text_frame
        text_frame.clear()
        para = text_frame.paragraphs[0]

        def format_risk_issue_text(paragraph, text):
            text = text.replace("\r\n\r\n", "\r\n")
            try:
                doc = html.fragment_fromstring(
                    text.replace("\n", "\x0a").replace("\r", ""), create_parent=True
                )
                children = doc.getchildren()
                if not len(children):
                    format_paragraph(paragraph, doc)
                for index, element in enumerate(children):
                    p = paragraph if index == 0 else text_frame.add_paragraph()
                    format_paragraph(p, element)
            except etree.ParseError:
                paragraph.text = text

        for issue in issues:
            para.line_spacing = 0.2
            header = para.add_run()
            header.text = "\n{issue.section} - {issue.priority}".format(issue=issue)
            font = header.font
            font.bold = True
            summary = text_frame.add_paragraph()
            summary.line_spacing = 1
            format_risk_issue_text(summary, issue.issue_description)
            para = text_frame.add_paragraph()

        if not issues.count():
            para.line_spacing = 0.2
            empty = para.add_run()
            empty.text = "\nThere are no Issues to report at this time."

        # Risks paragraphs
        risks_shape = placeholders[7]
        text_frame = risks_shape.text_frame
        text_frame.clear()
        para = text_frame.paragraphs[0]

        for risk in risks:
            para.line_spacing = 0.2
            header = para.add_run()
            header.text = "\n{risk.section} - {risk.risk_rating}".format(risk=risk)
            font = header.font
            font.bold = True
            summary = text_frame.add_paragraph()
            summary.line_spacing = 1
            format_risk_issue_text(summary, risk.risk_description)
            para = text_frame.add_paragraph()

        if not risks.count():
            para.line_spacing = 0.2
            empty = para.add_run()
            empty.text = "\nThere are no Risks to report at this time."


def generate_idea_page_slide(presentation, idea: Project):
    layouts = [layout for layout in presentation.slide_layouts]

    layout = layouts[0]
    slide = presentation.slides.add_slide(layout)
    placeholders = [placeholder for placeholder in slide.placeholders]
    for index, placeholder in enumerate(placeholders):
        placeholder.text = f"{index}"

    placeholders[0].text = idea.name
    placeholders[1].text = f"{idea.id}"
    format_text_box(placeholders[5], idea.summary)

    project_type = ", ".join(
        [project_type.name for project_type in idea.project_types.all()]
    )
    if project_type:
        placeholders[3].text = project_type
    else:
        placeholders[3].text = " "

    if idea.business_case:
        format_text_box(placeholders[6], idea.business_case)
    else:
        placeholders[6].text = "N/A"

    placeholders[2].text = idea.primary_division.name

    if idea.funding_size:
        funding_size = [
            item
            for item in Project.MONEY_AMOUNT_CHOICES
            if item[0] == idea.funding_size
        ][0][1]
        placeholders[9].text = funding_size
    else:
        placeholders[9].text = "N/A"

    expenditure_type = []
    if idea.capital_expenditure:
        expenditure_type.append(EXPENDITURE_TYPE_CAPITAL)
    if idea.opex_expenditure:
        expenditure_type.append(EXPENDITURE_TYPE_OPEX)
    if len(expenditure_type):
        placeholders[8].text = ", ".join(expenditure_type)
    else:
        placeholders[8].text = "N/A"

    if idea.annualized_savings:
        annualized_savings = [
            item
            for item in Project.MONEY_AMOUNT_CHOICES
            if item[0] == idea.annualized_savings
        ][0][1]
        placeholders[10].text = annualized_savings
    else:
        placeholders[10].text = "N/A"

    if idea.start_date:
        start_date = idea.start_date.strftime("%-m/%-d/%Y")
    else:
        start_date = "N/A"
    placeholders[4].text = start_date

    # All the people named
    people_text = make_people_string(idea)
    if not people_text.strip():
        people_text = "No roles have been assigned yet."
    format_people_box(placeholders[7], people_text)

    strategic_pillar_icon_placeholder_start = 11
    format_strategic_pillar_icons(
        placeholders, strategic_pillar_icon_placeholder_start, idea
    )

    pillars = "; ".join(
        [
            pillar.name
            for pillar in StrategicPillar.objects.filter(
                subpillar__project=idea
            ).distinct()
        ]
    )

    if pillars:
        placeholders[15].text = pillars
    else:
        placeholders[15].text = " "

    if idea.location:
        placeholders[16].text = idea.location.name
    else:
        placeholders[16].text = " "


def generate_health_card_presentation(page):
    template_file = finders.find("src/doc/healthcard_v8.pptx")
    presentation = Presentation(template_file)
    xml_slides = presentation.slides._sldIdLst
    for slide in list(xml_slides):
        presentation.part.drop_rel(slide.rId)
    del presentation.slides._sldIdLst[0]
    del presentation.slides._sldIdLst[0]
    generate_page_slide(presentation, page)
    memory_file = BytesIO()
    presentation.save(memory_file)
    memory_file.seek(0)
    return memory_file


def generate_idea_presentation(page):
    template_file = finders.find(
        "src/doc/idea-tracker-onepage-v4-add_locations_and_pillar_subpillar.pptx"
    )
    presentation = Presentation(template_file)
    xml_slides = list(presentation.slides._sldIdLst)
    if len(xml_slides):
        slide = xml_slides[0]
        presentation.part.drop_rel(slide.rId)
        del presentation.slides._sldIdLst[0]
    generate_idea_page_slide(presentation, page)
    memory_file = BytesIO()
    presentation.save(memory_file)
    memory_file.seek(0)
    return memory_file


def generate_presentation_deck(pages):
    template_file = finders.find("src/doc/healthcard_v8.pptx")
    presentation = Presentation(template_file)
    xml_slides = presentation.slides._sldIdLst
    for slide in list(xml_slides):
        presentation.part.drop_rel(slide.rId)
    del presentation.slides._sldIdLst[0]
    del presentation.slides._sldIdLst[0]
    for page in pages:
        generate_page_slide(presentation, page)
    memory_file = BytesIO()
    presentation.save(memory_file)
    memory_file.seek(0)
    return memory_file


class Slide(models.Model):
    file = models.FileField(upload_to="slides")
    health_graph = models.ImageField(upload_to="slides", blank=True, null=True)
    created = models.DateTimeField(auto_now_add=True, editable=False)
    modified = models.DateTimeField(auto_now=True)
    project = auto_prefetch.OneToOneField(
        Project, on_delete=models.CASCADE, null=True, db_constraint=False
    )

    def __str__(self):
        return f"Slide created on {self.created:%m/%d/%y}"

    def set_file(self):
        if self.project.idea_id and self.project.phase == Project.PHASE_IDEA:
            presentation = generate_idea_presentation(self.project)
        else:
            presentation = generate_health_card_presentation(self.project)
        self.file.save(f"{self.project.name}-{self.pk}.pptx", presentation)


class SlideDeck(auto_prefetch.Model):
    file = models.FileField(upload_to="slide_decks")
    created = models.DateTimeField(auto_now_add=True, editable=False)
    modified = models.DateTimeField(auto_now=True)
    projects = models.ManyToManyField(Project)

    def __str__(self):
        return f"Slide created on {self.created:%m/%d/%y}"

    def set_file(self):
        presentation = generate_presentation_deck(self.projects.all())
        self.file.save("deck.pptx", presentation)
