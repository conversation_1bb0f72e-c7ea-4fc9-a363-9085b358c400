import json

from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.utils.text import slugify
from django.views.generic import View

from apps.projects.models import Project
from apps.utils.mixins import LoginRequiredMixin

from .models import Slide, SlideDeck


class SlideCreateAndDownloadView(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        project = get_object_or_404(Project, pk=kwargs["pk"])

        try:
            slide = Slide.objects.get(project=project)
        except Slide.DoesNotExist:
            slide = Slide.objects.create(project=project)

        slide.set_file()
        slide.save()

        filename = slugify(project.name, allow_unicode=True) + "-summary.ppt"

        response = HttpResponse(
            content_type="application/vnd.openxmlformats-officedocument.presentationml.presentation"
        )
        response["Content-Disposition"] = f'attachment; filename="{filename}"'
        response.write(slide.file.read())

        return response


class SlideDeckCreateAndDownloadView(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        project_ids = json.loads(request.body)
        projects = Project.objects.filter(active=True).filter(pk__in=project_ids)

        slide_deck = SlideDeck.objects.create()
        slide_deck.projects.set(projects)
        slide_deck.set_file()
        slide_deck.save()

        filename = "project-deck.pptx"

        response = HttpResponse(
            content_type="application/vnd.openxmlformats-officedocument.presentationml.presentation"
        )
        response["Content-Disposition"] = f'attachment; filename="{filename}"'
        response.write(slide_deck.file.read())

        return response
