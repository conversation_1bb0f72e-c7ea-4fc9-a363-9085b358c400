# Generated by Django 5.2.4 on 2025-10-28 00:03

import auto_prefetch
import django.db.models.deletion
from django.db import migrations


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("meetings", "0001_initial"),
        ("projects", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="meetingitem",
            name="project",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="projects.project"
            ),
        ),
        migrations.AddField(
            model_name="document",
            name="meeting_item",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="documents",
                to="meetings.meetingitem",
            ),
        ),
        migrations.AddField(
            model_name="meetingitemreview",
            name="meeting_item",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="reviews",
                to="meetings.meetingitem",
            ),
        ),
    ]
