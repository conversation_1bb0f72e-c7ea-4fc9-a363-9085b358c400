import auto_prefetch
from datetime import datetime, timedelta
from typing import Union, cast

from django.contrib.contenttypes.fields import GenericF<PERSON>ign<PERSON><PERSON>
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models import Case, OuterRef, Q, Subquery, Value, When
from django.db.models.query import QuerySet
from django.utils import timezone

from apps.notifications.models import MessageTypes
from apps.projects.models import Project, ProjectHealth
from apps.projects.serializers import ProjectChangesSerializer


def rounder(t):
    return t.replace(
        day=t.day if t.hour < 23 else t.day + 1,
        second=0,
        microsecond=0,
        minute=0,
        hour=t.hour + 1 if t.hour < 23 else 0,
    )


class MeetingQuerySet(QuerySet):
    def get_current(self):
        try:
            return self.get(
                Q(locked=False)
                | Q(locked=True, meeting_date__gte=timezone.now() - timedelta(hours=6))
            )
        except Meeting.DoesNotExist:
            next_week = rounder(timezone.now() + timedelta(days=7))
            new_meeting = Meeting.objects.create(meeting_date=next_week)
            return new_meeting


class Meeting(models.Model):
    meeting_date = models.DateTimeField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)
    locked = models.BooleanField(default=False)

    objects = MeetingQuerySet.as_manager()

    @property
    def name(self):
        return "Tollgate Meeting on {date}".format(
            date=self.meeting_date.strftime("%B %-d")
        )


class MeetingItemQuerySet(QuerySet):
    def agenda(self):
        return self.filter(state=MeetingItem.ItemState.AGENDA, meeting__isnull=True)

    def queue(self):
        return self.filter(state=MeetingItem.ItemState.QUEUED)

    def with_latest_health(self):
        queryset = self.annotate(
            latest_health=Subquery(
                ProjectHealth.objects.filter(project=OuterRef("project__pk"))
                .order_by("-year", "-week")
                .values("health")[:1]
            )
        )
        return cast(MeetingItemQuerySet, queryset)

    def with_results(self):
        whens = [
            When(vote="Reject", then=Value(0)),
            When(vote="Conditionally Accept", then=Value(1)),
            When(vote="Accept", then=Value(2)),
        ]
        attendees = Subquery(
            MeetingItemReview.objects.filter(meeting_item=OuterRef("id"))
            .annotate(vote_value=Case(*whens, output_field=models.IntegerField()))
            .order_by("vote_value")
            .values("vote")[:1]
        )
        return self.annotate(vote_result=attendees)


class MeetingItem(auto_prefetch.Model):
    class ItemState(models.TextChoices):
        QUEUED = "Queued"
        AGENDA = "Agenda"

    project = auto_prefetch.ForeignKey(Project, on_delete=models.CASCADE)
    phase = models.CharField(
        max_length=50,
        choices=Project.PHASE_CHOICES,
        default=Project.PHASE_INITIATION,
        blank=True,
        null=True,
    )
    state = models.CharField(
        max_length=50,
        choices=ItemState.choices,
        default=ItemState.QUEUED,
        blank=True,
        null=True,
    )
    meeting = auto_prefetch.ForeignKey(
        Meeting, null=True, on_delete=models.CASCADE, related_name="items"
    )
    order = models.PositiveSmallIntegerField(null=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = MeetingItemQuerySet.as_manager()

    @property
    def meeting_date(self) -> Union[datetime, None]:
        return self.meeting.meeting_date if self.meeting else None

    @property
    def phase_display(self) -> str:
        return self.project.PHASE_DICT.get(self.phase, "None")

    def get_result(self) -> str:
        meeting_reviews = self.reviews.all()
        votes = meeting_reviews.values_list("vote", flat=True)
        if not len(votes):
            return "N/A"
        if MeetingItemReview.ReviewVote.REJECT in votes:
            result = "Rejected"
        elif MeetingItemReview.ReviewVote.CONDITIONALLY_ACCEPT in votes:
            result = "Conditionally Accepted"
        else:
            result = "Accepted"
        return result

    def create_notifications(self, user):
        from apps.notifications.models import notify

        project_data = ProjectChangesSerializer(self.project).data
        message = "{result} at the {phase} Tollgate".format(
            result=self.get_result(), phase=self.phase
        )
        recipients = (
            set(project_data["business_leads"])
            + set(project_data["business_analysts"])
            + set(project_data["project_managers"])
        )
        notify(
            message_type=MessageTypes.TOLLGATE_HELD,
            message=message,
            creator=user,
            content_object=self,
            recipients=recipients,
        )


class MeetingItemReview(auto_prefetch.Model):
    class ReviewVote(models.TextChoices):
        ACCEPT = "Accept"
        CONDITIONALLY_ACCEPT = "Conditionally Accept"
        REJECT = "Reject"

    reviewer = auto_prefetch.ForeignKey("users.User", on_delete=models.CASCADE)
    vote = models.CharField(
        max_length=50,
        choices=ReviewVote.choices,
        default=ReviewVote.ACCEPT,
        blank=True,
        null=True,
    )
    notes = models.TextField(blank=True, null=True)
    meeting_item = auto_prefetch.ForeignKey(
        MeetingItem, on_delete=models.CASCADE, related_name="reviews"
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta(auto_prefetch.Model.Meta):
        unique_together = ["reviewer", "meeting_item"]


class Document(auto_prefetch.Model):
    content_type = auto_prefetch.ForeignKey(
        ContentType, null=True, on_delete=models.CASCADE
    )
    object_id = models.PositiveIntegerField(null=True)
    content_object = GenericForeignKey("content_type", "object_id")
    meeting_item = auto_prefetch.ForeignKey(
        MeetingItem, on_delete=models.CASCADE, related_name="documents"
    )

    @property
    def document_name(self) -> str:
        if hasattr(self.content_object, "file_name"):
            return self.content_object.file_name
        return self.content_object.name if self.content_object else ""

    @property
    def document_url(self) -> str:
        if hasattr(self.content_object, "file"):
            return self.content_object.file.url
        return self.content_object.url if self.content_object else ""

    @property
    def application_type(self) -> str:
        if hasattr(self.content_object, "application_type"):
            return self.content_object.application_type
        return "Link"
