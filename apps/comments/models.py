import auto_prefetch
from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.db import models


class Commentable(models.Model):
    """A model to be added to other to inherit methods and other fields that will be common with comments"""

    def get_comments(self):
        """Return the comments of a specific model"""

        return self.comments.all()

    class Meta:
        abstract = True


class Comment(auto_prefetch.Model):
    content_type = auto_prefetch.ForeignKey(
        ContentType, null=True, on_delete=models.CASCADE
    )
    object_id = models.PositiveIntegerField(null=True)
    content_object = GenericForeignKey("content_type", "object_id")
    text = models.TextField()
    creator = auto_prefetch.ForeignKey("users.User", on_delete=models.PROTECT)
    created = models.DateTimeField(auto_now_add=True, editable=False)
    modified = models.DateTimeField(auto_now=True, editable=False)

    def create_action_record(self):
        from apps.projects.models import Project
        from apps.actions.models import record, ActionType

        project = (
            self.content_object
            if isinstance(self.content_object, Project)
            else getattr(self.content_object, "project", None)
        )
        if project:
            text = '{user} added comment: "{comment}"'.format(
                user=self.creator.full_name(), comment=self.text
            )
            record(
                project=project,
                action_type=ActionType.ADDED_COMMENT,
                editor=self.creator,
                description=text,
            )

    def create_notifications(self, user):
        from apps.notifications.models import MessageTypes, notify

        message = (
            "{user} added a comment to {target} <a href='{url}'>{name}</a>.".format(
                user=user.full_name,
                url=self.content_object.get_absolute_url(),
                target=self.content_type.name,
                name=self.content_object,
            )
        )
        notify(
            message_type=MessageTypes.COMMENT_ADDED,
            message=message,
            creator=user,
            content_object=self.content_object,
        )
