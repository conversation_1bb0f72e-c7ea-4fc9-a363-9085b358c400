[{"model": "organizations.company", "pk": 1, "fields": {"name": "Acme Corporation", "logo": ""}}, {"model": "organizations.company", "pk": 2, "fields": {"name": "Globex Inc", "logo": ""}}, {"model": "organizations.company", "pk": 3, "fields": {"name": "Initech", "logo": ""}}, {"model": "organizations.authenticateddomains", "pk": 1, "fields": {"company": 1, "domain": "acme.com"}}, {"model": "organizations.authenticateddomains", "pk": 2, "fields": {"company": 2, "domain": "globex.io"}}, {"model": "organizations.authenticateddomains", "pk": 3, "fields": {"company": 3, "domain": "initech.org"}}, {"model": "locations.location", "pk": 1, "fields": {"name": "Arnold, PA Facility", "code": "ARN", "company": 1}}, {"model": "locations.location", "pk": 2, "fields": {"name": "Carroll, IA Facility", "code": "CAR", "company": 2}}, {"model": "locations.location", "pk": 3, "fields": {"name": "Charlotte, NC Facility", "code": "CHH", "company": 3}}, {"model": "locations.location", "pk": 4, "fields": {"name": "Cincinnati, OH BioScience Office", "code": "CEL", "company": 1}}, {"model": "locations.location", "pk": 5, "fields": {"name": "Cincinnati, OH Office", "code": "CEL", "company": 2}}, {"model": "locations.location", "pk": 6, "fields": {"name": "Clinton, NC Facility", "code": "CLN", "company": 3}}, {"model": "locations.location", "pk": 7, "fields": {"name": "Crete, NE Facility", "code": "CRT", "company": 1}}, {"model": "locations.location", "pk": 8, "fields": {"name": "Cudahy, WI Facility", "code": "CUD", "company": 2}}, {"model": "locations.location", "pk": 9, "fields": {"name": "Cumming, GA Facility", "code": "CMG", "company": 3}}, {"model": "locations.location", "pk": 10, "fields": {"name": "Denison, IA Facility", "code": "DEN", "company": 1}}, {"model": "locations.location", "pk": 11, "fields": {"name": "Denver, CO Pet Food Facility", "code": "DNV", "company": 2}}, {"model": "locations.location", "pk": 12, "fields": {"name": "East Central Region Office", "code": "KEN", "company": 3}}, {"model": "locations.location", "pk": 13, "fields": {"name": "Edwardsville, KS Distribution Center", "code": "EDW", "company": 1}}, {"model": "locations.location", "pk": 14, "fields": {"name": "Elizabeth, NJ Facility", "code": "ELZ", "company": 2}}, {"model": "locations.location", "pk": 15, "fields": {"name": "Grayson, KY Facility", "code": "GRY", "company": 3}}, {"model": "locations.location", "pk": 16, "fields": {"name": "Greenfield, Indiana Distribution Center", "code": "GRN", "company": 1}}, {"model": "locations.location", "pk": 17, "fields": {"name": "Junction City, KS Facility", "code": "JCT", "company": 2}}, {"model": "locations.location", "pk": 18, "fields": {"name": "Kansas City, MO Office", "code": "KCS", "company": 3}}, {"model": "locations.location", "pk": 19, "fields": {"name": "Kinston, NC Facility", "code": "SK2", "company": 1}}, {"model": "locations.location", "pk": 20, "fields": {"name": "Lincoln, NE Facility", "code": "LNC", "company": 2}}, {"model": "locations.location", "pk": 21, "fields": {"name": "Lisle, IL Office", "code": "ZZZ", "company": 3}}, {"model": "locations.location", "pk": 22, "fields": {"name": "Martin City, MO Facility", "code": "MAR", "company": 1}}, {"model": "locations.location", "pk": 23, "fields": {"name": "Mason City, IA Facility", "code": "MAS", "company": 2}}, {"model": "locations.location", "pk": 24, "fields": {"name": "Middlesboro, KY Facility", "code": "MDL", "company": 3}}, {"model": "locations.location", "pk": 25, "fields": {"name": "Midwest Region Office", "code": "MDW", "company": 1}}, {"model": "locations.location", "pk": 26, "fields": {"name": "Milan, MO Facility", "code": "MIL", "company": 2}}, {"model": "locations.location", "pk": 27, "fields": {"name": "Missouri Region Office", "code": "MBM", "company": 3}}, {"model": "locations.location", "pk": 28, "fields": {"name": "Monmouth, IL Facility", "code": "MON", "company": 1}}, {"model": "locations.location", "pk": 29, "fields": {"name": "Newport News, VA Distribution Center", "code": "NNS", "company": 2}}, {"model": "locations.location", "pk": 30, "fields": {"name": "North Region Office", "code": "WAV", "company": 3}}, {"model": "locations.location", "pk": 31, "fields": {"name": "Omaha, NE Facility", "code": "OMA", "company": 1}}, {"model": "locations.location", "pk": 32, "fields": {"name": "Orange City, IA Pet Food Facility", "code": "ORG", "company": 2}}, {"model": "locations.location", "pk": 33, "fields": {"name": "Peru, IN Facility", "code": "PER", "company": 3}}, {"model": "locations.location", "pk": 34, "fields": {"name": "Rocky Mountain Region - AZ Office", "code": "C4F", "company": 1}}, {"model": "locations.location", "pk": 35, "fields": {"name": "Rocky Mountain Region - CA Office", "code": "C4F", "company": 2}}, {"model": "locations.location", "pk": 36, "fields": {"name": "Rocky Mountain Region - CO Office", "code": "ZZZ", "company": 3}}, {"model": "locations.location", "pk": 37, "fields": {"name": "Rocky Mountain Region - UT Office", "code": "C4F", "company": 1}}, {"model": "locations.location", "pk": 38, "fields": {"name": "Rocky Mountain Region - WY Office", "code": "ZZZ", "company": 2}}, {"model": "locations.location", "pk": 39, "fields": {"name": "Salt Lake City, UT Facility", "code": "SLC", "company": 3}}, {"model": "locations.location", "pk": 40, "fields": {"name": "San Jose, CA Facility", "code": "ZZZ", "company": 1}}, {"model": "locations.location", "pk": 41, "fields": {"name": "San Leandro, CA Facility", "code": "SAG", "company": 2}}, {"model": "locations.location", "pk": 42, "fields": {"name": "Sioux Center, IA Facility", "code": "SOU", "company": 3}}, {"model": "locations.location", "pk": 43, "fields": {"name": "Sioux City, IA Facility", "code": "TUR", "company": 1}}, {"model": "locations.location", "pk": 44, "fields": {"name": "Sioux Falls, SD Facility", "code": "SFL", "company": 2}}, {"model": "locations.location", "pk": 45, "fields": {"name": "Smithfield Premium Genetics", "code": "SPG", "company": 3}}, {"model": "locations.location", "pk": 46, "fields": {"name": "Smithfield, VA Facility", "code": "ZZZ", "company": 1}}, {"model": "locations.location", "pk": 47, "fields": {"name": "Smithfield, VA Offices", "code": "ZZZ", "company": 2}}, {"model": "locations.location", "pk": 48, "fields": {"name": "Smithfield, VA Pet Food Facility", "code": "ZZZ", "company": 3}}, {"model": "locations.location", "pk": 49, "fields": {"name": "South Central Region Office", "code": "LAU", "company": 1}}, {"model": "locations.location", "pk": 50, "fields": {"name": "Springdale, OH Facility", "code": "SPR", "company": 2}}, {"model": "locations.location", "pk": 51, "fields": {"name": "Springfield, MA Facility", "code": "SPF", "company": 3}}, {"model": "locations.location", "pk": 52, "fields": {"name": "St. Charles, IL Facility", "code": "SCH", "company": 1}}, {"model": "locations.location", "pk": 53, "fields": {"name": "St. James, MN Facility", "code": "STJ", "company": 2}}, {"model": "locations.location", "pk": 54, "fields": {"name": "Tar Heel, NC Distribution Center", "code": "TDC", "company": 3}}, {"model": "locations.location", "pk": 55, "fields": {"name": "Tar Heel, NC Facility", "code": "TAR", "company": 1}}, {"model": "locations.location", "pk": 56, "fields": {"name": "Toano, VA Facility", "code": "TOA", "company": 2}}, {"model": "locations.location", "pk": 57, "fields": {"name": "Vernon, CA Facility", "code": "VDC", "company": 3}}, {"model": "locations.location", "pk": 58, "fields": {"name": "Warsaw, NC Office", "code": "ZZZ", "company": 1}}, {"model": "locations.location", "pk": 59, "fields": {"name": "West Region Office", "code": "ZZZ", "company": 2}}, {"model": "locations.location", "pk": 60, "fields": {"name": "Wichita, KS Facility", "code": "WCH", "company": 3}}, {"model": "locations.location", "pk": 61, "fields": {"name": "Wilson, NC Facility", "code": "WIL", "company": 1}}, {"model": "locations.location", "pk": 62, "fields": {"name": "Smithfield Corporate", "code": "SFI", "company": 2}}, {"model": "locations.location", "pk": 63, "fields": {"name": "Agri Plus", "code": "AGP", "company": 3}}, {"model": "locations.location", "pk": 64, "fields": {"name": "Animex Corporate", "code": "ANI", "company": 1}}, {"model": "locations.location", "pk": 65, "fields": {"name": "<PERSON>z<PERSON><PERSON>in, PL", "code": "AGR", "company": 2}}, {"model": "locations.location", "pk": 66, "fields": {"name": "Poultry Procurement, PL", "code": "AGS", "company": 3}}, {"model": "locations.location", "pk": 67, "fields": {"name": "Suwalki, PL", "code": "ASW", "company": 1}}, {"model": "locations.location", "pk": 68, "fields": {"name": "CF Morliny, PL", "code": "CFM", "company": 2}}, {"model": "locations.location", "pk": 69, "fields": {"name": "Grodkow, PL", "code": "CTP", "company": 3}}, {"model": "locations.location", "pk": 70, "fields": {"name": "Debica, PL", "code": "DEB", "company": 1}}, {"model": "locations.location", "pk": 71, "fields": {"name": "Ilawa, PL", "code": "EKO", "company": 2}}, {"model": "locations.location", "pk": 72, "fields": {"name": "Kutno Chicken, PL", "code": "KU3", "company": 3}}, {"model": "locations.location", "pk": 73, "fields": {"name": "Kutno Fresh, PL", "code": "KU4", "company": 1}}, {"model": "locations.location", "pk": 74, "fields": {"name": "Kutno Processed, PL", "code": "KU1", "company": 2}}, {"model": "locations.location", "pk": 75, "fields": {"name": "Kutno Semi, PL", "code": "KU2", "company": 3}}, {"model": "locations.location", "pk": 76, "fields": {"name": "Mazury, PL", "code": "MAZ", "company": 1}}, {"model": "locations.location", "pk": 77, "fields": {"name": "Morliny, PL", "code": "MOR", "company": 2}}, {"model": "locations.location", "pk": 78, "fields": {"name": "Opole, PL", "code": "OZD", "company": 3}}, {"model": "locations.location", "pk": 79, "fields": {"name": "Krakow, PL", "code": "PRZ", "company": 1}}, {"model": "locations.location", "pk": 80, "fields": {"name": "Starachowice, PL", "code": "ZMA", "company": 2}}, {"model": "locations.location", "pk": 81, "fields": {"name": "Zamosc, PL", "code": "ANP", "company": 3}}, {"model": "locations.location", "pk": 82, "fields": {"name": "Elit", "code": "ELT", "company": 1}}, {"model": "locations.location", "pk": 83, "fields": {"name": "<PERSON><PERSON>", "code": "MAI", "company": 2}}, {"model": "locations.location", "pk": 84, "fields": {"name": "Smithfield Ferme", "code": "ROF", "company": 3}}, {"model": "locations.location", "pk": 85, "fields": {"name": "Smithfield PROD", "code": "ROP", "company": 1}}, {"model": "locations.location", "pk": 86, "fields": {"name": "Smithfield Foods UK", "code": "UKS", "company": 2}}, {"model": "programs.program", "pk": 1, "fields": {"company": 1, "active": true, "name": "Employee Engagement Program", "summary": "A program to improve employee engagement across the company.", "state": "active", "private": false, "created_by": 1, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 2, "fields": {"company": 2, "active": true, "name": "Sustainability Initiative", "summary": "Focus on sustainable business practices across divisions.", "state": "active", "private": false, "created_by": 2, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 3, "fields": {"company": 3, "active": true, "name": "Digital Transformation", "summary": "Program to improve digital workflows and automation.", "state": "active", "private": false, "created_by": 3, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 4, "fields": {"company": 1, "active": true, "name": "Leadership Development", "summary": "Training programs to build leadership capabilities.", "state": "active", "private": false, "created_by": 1, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 5, "fields": {"company": 2, "active": true, "name": "Customer Experience Enhancement", "summary": "Improving client satisfaction and retention.", "state": "active", "private": false, "created_by": 2, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 6, "fields": {"company": 3, "active": true, "name": "Innovation Accelerator", "summary": "Programs to drive new product innovation.", "state": "active", "private": false, "created_by": 3, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 7, "fields": {"company": 1, "active": true, "name": "Operational Excellence", "summary": "Enhancing efficiency and quality in operations.", "state": "active", "private": false, "created_by": 1, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 8, "fields": {"company": 2, "active": true, "name": "Cybersecurity Awareness", "summary": "Educating staff on best cybersecurity practices.", "state": "active", "private": false, "created_by": 2, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 9, "fields": {"company": 3, "active": true, "name": "Diversity & Inclusion", "summary": "Programs to enhance workplace diversity.", "state": "active", "private": false, "created_by": 3, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 10, "fields": {"company": 1, "active": true, "name": "Supply Chain Optimization", "summary": "Improving supply chain efficiency and resilience.", "state": "active", "private": false, "created_by": 1, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 11, "fields": {"company": 2, "active": true, "name": "Brand Awareness Campaign", "summary": "Marketing initiative to grow brand recognition.", "state": "active", "private": false, "created_by": 2, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 12, "fields": {"company": 3, "active": true, "name": "Data Analytics Expansion", "summary": "Leverage data for smarter decision-making.", "state": "active", "private": false, "created_by": 3, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 13, "fields": {"company": 1, "active": true, "name": "Employee Wellness", "summary": "Promoting employee health and well-being.", "state": "active", "private": false, "created_by": 1, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 14, "fields": {"company": 2, "active": true, "name": "Cost Reduction Initiative", "summary": "Programs aimed at reducing operational costs.", "state": "active", "private": false, "created_by": 2, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 15, "fields": {"company": 3, "active": true, "name": "Customer Loyalty Program", "summary": "Programs to increase customer retention and loyalty.", "state": "active", "private": false, "created_by": 3, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 16, "fields": {"company": 1, "active": true, "name": "Process Automation", "summary": "Automation of repetitive business processes.", "state": "active", "private": false, "created_by": 1, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 17, "fields": {"company": 2, "active": true, "name": "Strategic Partnerships", "summary": "Developing key partnerships to grow business.", "state": "active", "private": false, "created_by": 2, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 18, "fields": {"company": 3, "active": true, "name": "IT Infrastructure Upgrade", "summary": "Modernizing IT infrastructure for performance and security.", "state": "active", "private": false, "created_by": 3, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 19, "fields": {"company": 1, "active": true, "name": "Compliance & Risk Management", "summary": "Ensuring regulatory compliance and risk mitigation.", "state": "active", "private": false, "created_by": 1, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "programs.program", "pk": 20, "fields": {"company": 2, "active": true, "name": "Market Expansion", "summary": "Expanding business into new markets and territories.", "state": "active", "private": false, "created_by": 2, "created": "2025-10-02T00:00:00Z", "modified": "2025-10-02T00:00:00Z"}}, {"model": "users.user", "pk": 1, "fields": {"username": "jdoe", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 1, "date_joined": "2025-01-01T10:00:00Z"}}, {"model": "users.user", "pk": 2, "fields": {"username": "asmith", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": true, "is_superuser": false, "is_active": true, "company": 1, "date_joined": "2025-01-02T09:00:00Z"}}, {"model": "users.user", "pk": 3, "fields": {"username": "bwayne", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 1, "date_joined": "2025-01-03T08:00:00Z"}}, {"model": "users.user", "pk": 4, "fields": {"username": "ckent", "first_name": "<PERSON>", "last_name": "Kent", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 1, "date_joined": "2025-01-04T11:00:00Z"}}, {"model": "users.user", "pk": 5, "fields": {"username": "dprince", "first_name": "<PERSON>", "last_name": "Prince", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 1, "date_joined": "2025-01-05T12:00:00Z"}}, {"model": "users.user", "pk": 6, "fields": {"username": "tstark", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": true, "is_superuser": false, "is_active": true, "company": 2, "date_joined": "2025-01-06T08:00:00Z"}}, {"model": "users.user", "pk": 7, "fields": {"username": "ssummers", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 2, "date_joined": "2025-01-07T09:00:00Z"}}, {"model": "users.user", "pk": 8, "fields": {"username": "j<PERSON>", "first_name": "<PERSON>", "last_name": "Lane", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 2, "date_joined": "2025-01-08T10:00:00Z"}}, {"model": "users.user", "pk": 9, "fields": {"username": "pparker", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 2, "date_joined": "2025-01-09T11:00:00Z"}}, {"model": "users.user", "pk": 10, "fields": {"username": "r<PERSON>ers", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 2, "date_joined": "2025-01-10T12:00:00Z"}}, {"model": "users.user", "pk": 11, "fields": {"username": "neo", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 3, "date_joined": "2025-01-11T08:00:00Z"}}, {"model": "users.user", "pk": 12, "fields": {"username": "trinity", "first_name": "Trinity", "last_name": "", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 3, "date_joined": "2025-01-12T09:00:00Z"}}, {"model": "users.user", "pk": 13, "fields": {"username": "mre", "first_name": "Morpheus", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": true, "is_superuser": false, "is_active": true, "company": 3, "date_joined": "2025-01-13T10:00:00Z"}}, {"model": "users.user", "pk": 14, "fields": {"username": "agent_smith", "first_name": "Agent", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 3, "date_joined": "2025-01-14T11:00:00Z"}}, {"model": "users.user", "pk": 15, "fields": {"username": "cypher", "first_name": "<PERSON><PERSON>", "last_name": "", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 3, "date_joined": "2025-01-15T12:00:00Z"}}, {"model": "users.user", "pk": 16, "fields": {"username": "harley", "first_name": "Harley", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 1, "date_joined": "2025-01-16T08:00:00Z"}}, {"model": "users.user", "pk": 17, "fields": {"username": "banner", "first_name": "<PERSON>", "last_name": "Banner", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 2, "date_joined": "2025-01-17T09:00:00Z"}}, {"model": "users.user", "pk": 18, "fields": {"username": "barrya", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 3, "date_joined": "2025-01-18T10:00:00Z"}}, {"model": "users.user", "pk": 19, "fields": {"username": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 2, "date_joined": "2025-01-19T11:00:00Z"}}, {"model": "users.user", "pk": 20, "fields": {"username": "selina", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": false, "is_superuser": false, "is_active": true, "company": 1, "date_joined": "2025-01-20T12:00:00Z"}}, {"model": "users.user", "pk": 21, "fields": {"username": "admin", "first_name": "Super", "last_name": "User", "email": "<EMAIL>", "is_staff": true, "is_superuser": true, "is_active": true, "company": null, "date_joined": "2025-01-21T08:00:00Z"}}, {"model": "users.user", "pk": 22, "fields": {"username": "levimoore", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": true, "is_superuser": true, "is_active": true, "company": 1, "date_joined": "2025-01-22T09:00:00Z"}}, {"model": "projects.project", "pk": 1, "fields": {"company": 1, "location": 1, "private": false, "ready_to_begin": true, "idea_id": "IDEA-001", "name": "New Pork Processing Facility", "project_rigor": "high", "summary": "Build a facility to expand processing capacity.", "funding_source": "business", "payback_period": "1-2 years", "priority": "high", "complexity": "high", "project_state": "active", "phase": "execution", "departments_involved": "6+", "vendors_involved": "4-6", "schedule_motivation": "regulatory_mandate", "similar_experience": "medium", "infrastructure_readiness": "medium", "external_user_privileges": "view", "post_project_support": "high", "created_by": 1, "created": "2025-01-01T09:00:00Z", "modified": "2025-01-10T09:00:00Z", "active": true, "current_environment": 2, "failure_severity": 1, "response_to_audit": 1, "estimation_confidence": 2, "program": 1}}, {"model": "projects.project", "pk": 2, "fields": {"company": 2, "location": 2, "private": false, "ready_to_begin": false, "idea_id": "IDEA-002", "name": "IT Security Upgrade", "project_rigor": "standard", "summary": "Upgrade IT systems for compliance.", "funding_source": "it&t", "payback_period": "2-3 years", "priority": "medium", "complexity": "medium", "project_state": "planning", "phase": "planning", "departments_involved": "2-3", "vendors_involved": "1-3", "schedule_motivation": "resource_constraint", "similar_experience": "high", "infrastructure_readiness": "low", "external_user_privileges": "download", "post_project_support": "medium", "created_by": 2, "created": "2025-02-01T09:00:00Z", "modified": "2025-02-05T09:00:00Z", "active": true, "current_environment": 1, "failure_severity": 2, "response_to_audit": 0, "estimation_confidence": 3, "program": 2}}, {"model": "projects.project", "pk": 3, "fields": {"company": 3, "location": 3, "private": true, "ready_to_begin": false, "idea_id": "IDEA-003", "name": "Sustainability Initiative", "project_rigor": "small work effort", "summary": "Pilot program for reducing energy consumption.", "funding_source": "to be determined", "payback_period": "<1 year", "priority": "low", "complexity": "low", "project_state": "draft", "phase": "idea", "departments_involved": "1", "vendors_involved": "0", "schedule_motivation": "none", "similar_experience": "low", "infrastructure_readiness": "none", "external_user_privileges": "none", "post_project_support": "low", "created_by": 3, "created": "2025-03-01T09:00:00Z", "modified": "2025-03-02T09:00:00Z", "active": true, "current_environment": 0, "failure_severity": 0, "response_to_audit": 2, "estimation_confidence": 1, "program": null}}, {"model": "projects.project", "pk": 4, "fields": {"company": 1, "location": 2, "private": false, "ready_to_begin": true, "idea_id": "IDEA-004", "name": "Cloud Migration", "project_rigor": "high", "summary": "Migrate on-prem systems to cloud.", "funding_source": "it&t", "payback_period": "3-4 years", "priority": "high", "complexity": "high", "project_state": "active", "phase": "execution", "departments_involved": "4-5", "vendors_involved": "7+", "schedule_motivation": "business_motivation", "similar_experience": "medium", "infrastructure_readiness": "medium", "external_user_privileges": "edit", "post_project_support": "high", "created_by": 4, "created": "2025-03-10T09:00:00Z", "modified": "2025-03-20T09:00:00Z", "active": true, "current_environment": 1, "failure_severity": 3, "response_to_audit": 1, "estimation_confidence": 2, "program": 1}}, {"model": "projects.project", "pk": 5, "fields": {"company": 2, "location": 1, "private": false, "ready_to_begin": true, "idea_id": "IDEA-005", "name": "Warehouse Automation", "project_rigor": "standard", "summary": "Introduce robotics for warehouse operations.", "funding_source": "business", "payback_period": "1-2 years", "priority": "medium", "complexity": "high", "project_state": "active", "phase": "execution", "departments_involved": "2-3", "vendors_involved": "4-6", "schedule_motivation": "regulatory_mandate", "similar_experience": "medium", "infrastructure_readiness": "high", "external_user_privileges": "view", "post_project_support": "medium", "created_by": 5, "created": "2025-03-15T09:00:00Z", "modified": "2025-03-25T09:00:00Z", "active": true, "current_environment": 2, "failure_severity": 1, "response_to_audit": 0, "estimation_confidence": 3, "program": 2}}, {"model": "projects.project", "pk": 6, "fields": {"company": 3, "location": 2, "private": true, "ready_to_begin": false, "idea_id": "IDEA-006", "name": "Customer Portal Redesign", "project_rigor": "small work effort", "summary": "Redesign UI/UX for better customer experience.", "funding_source": "business", "payback_period": "<1 year", "priority": "low", "complexity": "medium", "project_state": "draft", "phase": "idea", "departments_involved": "1", "vendors_involved": "0", "schedule_motivation": "business_motivation", "similar_experience": "high", "infrastructure_readiness": "low", "external_user_privileges": "comment", "post_project_support": "low", "created_by": 6, "created": "2025-03-20T09:00:00Z", "modified": "2025-03-21T09:00:00Z", "active": true, "current_environment": 0, "failure_severity": 0, "response_to_audit": 2, "estimation_confidence": 2, "program": null}}, {"model": "projects.project", "pk": 7, "fields": {"company": 1, "location": 3, "private": false, "ready_to_begin": true, "idea_id": "IDEA-007", "name": "AI Predictive Maintenance", "project_rigor": "high", "summary": "Use AI to predict equipment failures.", "funding_source": "business", "payback_period": "2-3 years", "priority": "high", "complexity": "high", "project_state": "active", "phase": "execution", "departments_involved": "4-5", "vendors_involved": "4-6", "schedule_motivation": "resource_constraint", "similar_experience": "medium", "infrastructure_readiness": "medium", "external_user_privileges": "edit", "post_project_support": "high", "created_by": 7, "created": "2025-03-25T09:00:00Z", "modified": "2025-03-28T09:00:00Z", "active": true, "current_environment": 2, "failure_severity": 2, "response_to_audit": 1, "estimation_confidence": 3, "program": 1}}, {"model": "projects.project", "pk": 8, "fields": {"company": 2, "location": 1, "private": false, "ready_to_begin": false, "idea_id": "IDEA-008", "name": "Employee Wellness Program", "project_rigor": "standard", "summary": "Develop a new wellness initiative.", "funding_source": "business", "payback_period": "3-4 years", "priority": "medium", "complexity": "low", "project_state": "planning", "phase": "planning", "departments_involved": "2-3", "vendors_involved": "1-3", "schedule_motivation": "none", "similar_experience": "low", "infrastructure_readiness": "none", "external_user_privileges": "none", "post_project_support": "medium", "created_by": 8, "created": "2025-04-01T09:00:00Z", "modified": "2025-04-02T09:00:00Z", "active": true, "current_environment": 1, "failure_severity": 1, "response_to_audit": 0, "estimation_confidence": 2, "program": 2}}, {"model": "projects.project", "pk": 9, "fields": {"company": 3, "location": 2, "private": true, "ready_to_begin": false, "idea_id": "IDEA-009", "name": "Green Energy Pilot", "project_rigor": "small work effort", "summary": "Test solar and wind integration.", "funding_source": "to be determined", "payback_period": ">4 years", "priority": "low", "complexity": "medium", "project_state": "draft", "phase": "idea", "departments_involved": "2-3", "vendors_involved": "0", "schedule_motivation": "business_motivation", "similar_experience": "medium", "infrastructure_readiness": "low", "external_user_privileges": "view", "post_project_support": "low", "created_by": 9, "created": "2025-04-05T09:00:00Z", "modified": "2025-04-06T09:00:00Z", "active": true, "current_environment": 0, "failure_severity": 0, "response_to_audit": 2, "estimation_confidence": 1, "program": null}}, {"model": "projects.project", "pk": 10, "fields": {"company": 1, "location": 1, "private": false, "ready_to_begin": true, "idea_id": "IDEA-010", "name": "Data Warehouse Modernization", "project_rigor": "high", "summary": "Rebuild data warehouse with modern stack.", "funding_source": "business", "payback_period": "1-2 years", "priority": "high", "complexity": "high", "project_state": "active", "phase": "execution", "departments_involved": "6+", "vendors_involved": "7+", "schedule_motivation": "resource_constraint", "similar_experience": "medium", "infrastructure_readiness": "medium", "external_user_privileges": "download", "post_project_support": "high", "created_by": 10, "created": "2025-04-10T09:00:00Z", "modified": "2025-04-15T09:00:00Z", "active": true, "current_environment": 2, "failure_severity": 3, "response_to_audit": 1, "estimation_confidence": 2, "program": 1}}, {"model": "projects.businesssegment", "pk": 1, "fields": {"name": "Corporate", "company": 2}}, {"model": "projects.businesssegment", "pk": 2, "fields": {"name": "Fresh", "company": 1}}, {"model": "projects.businesssegment", "pk": 3, "fields": {"name": "Packaged", "company": 3}}, {"model": "projects.businesssegment", "pk": 4, "fields": {"name": "International", "company": 1}}, {"model": "projects.businesssegment", "pk": 5, "fields": {"name": "Smithfield Hog Production", "company": 3}}, {"model": "projects.businesssegment", "pk": 6, "fields": {"name": "Supply Chain", "company": 2}}, {"model": "projects.pod", "pk": 1, "fields": {"name": "Bellies"}}, {"model": "projects.pod", "pk": 2, "fields": {"name": "Fresh Retail Cuts"}}, {"model": "projects.pod", "pk": 3, "fields": {"name": "Hams"}}, {"model": "projects.pod", "pk": 4, "fields": {"name": "<PERSON><PERSON>"}}, {"model": "projects.pod", "pk": 5, "fields": {"name": "Prepared Foods"}}, {"model": "projects.pod", "pk": 6, "fields": {"name": "DSD"}}, {"model": "projects.pod", "pk": 7, "fields": {"name": "Emerging Business"}}, {"model": "projects.projecttype", "pk": 1, "fields": {"name": "Environmental"}}, {"model": "projects.projecttype", "pk": 2, "fields": {"name": "Equipment (New or Improved)"}}, {"model": "projects.projecttype", "pk": 3, "fields": {"name": "Expense Project"}}, {"model": "projects.projecttype", "pk": 4, "fields": {"name": "Food Safety"}}, {"model": "projects.projecttype", "pk": 5, "fields": {"name": "Infrastructure Maintenance"}}, {"model": "projects.projecttype", "pk": 6, "fields": {"name": "Innovation Product or Package Change"}}, {"model": "projects.projecttype", "pk": 7, "fields": {"name": "IT&T Project"}}, {"model": "projects.projecttype", "pk": 8, "fields": {"name": "Labor Reduction"}}, {"model": "projects.projecttype", "pk": 9, "fields": {"name": "Lease Notification Only"}}, {"model": "projects.projecttype", "pk": 10, "fields": {"name": "PSM"}}, {"model": "projects.projecttype", "pk": 11, "fields": {"name": "Sales Growth"}}, {"model": "projects.projecttype", "pk": 12, "fields": {"name": "Transportation"}}, {"model": "projects.projecttype", "pk": 13, "fields": {"name": "Warehousing"}}, {"model": "projects.projecttype", "pk": 14, "fields": {"name": "Worker Safety"}}, {"model": "projects.strategicpillar", "pk": 1, "fields": {"name": "Continuous Improvement"}}, {"model": "projects.strategicpillar", "pk": 3, "fields": {"name": "New Plant Capacity"}}, {"model": "projects.strategicpillar", "pk": 4, "fields": {"name": "Organic Growth"}}, {"model": "projects.strategicpillar", "pk": 5, "fields": {"name": "Strategy Pivot"}}, {"model": "projects.strategicpillar", "pk": 6, "fields": {"name": "Replacement"}}, {"model": "projects.strategicpillar", "pk": 7, "fields": {"name": "Regulatory"}}, {"model": "projects.strategicpillar", "pk": 8, "fields": {"name": "Sustainability"}}]