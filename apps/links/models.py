import auto_prefetch
import reversion
from django.contrib.contenttypes.fields import Generic<PERSON><PERSON><PERSON><PERSON><PERSON>, GenericRelation
from django.contrib.contenttypes.models import ContentType
from django.db import models
from taggit.managers import TaggableManager


@reversion.register()
class Link(auto_prefetch.Model):
    content_type = auto_prefetch.ForeignKey(
        ContentType, null=True, on_delete=models.CASCADE
    )
    object_id = models.PositiveIntegerField(null=True)
    content_object = GenericForeignKey("content_type", "object_id")
    name = models.CharField(max_length=255)
    url = models.URLField(max_length=1000)
    weight = models.SmallIntegerField(default=0, db_index=True)
    created = models.DateTimeField(auto_now_add=True)
    created_by = auto_prefetch.ForeignKey(
        "users.User", editable=False, on_delete=models.PROTECT
    )
    tags = TaggableManager(blank=True)
    documents = GenericRelation("meetings.Document")

    class Meta(auto_prefetch.Model.Meta):
        ordering = ["weight", "created"]

    def __str__(self):
        return self.name

    @property
    def author(self):
        return self.created_by.full_name
