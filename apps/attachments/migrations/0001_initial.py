# Generated by Django 5.2.4 on 2025-10-28 00:03

import django.db.models.deletion
import django.db.models.manager
import django_lifecycle.mixins
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="Attachment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("object_id", models.PositiveIntegerField(null=True)),
                ("file", models.FileField(max_length=500, upload_to="projects")),
                ("name", models.CharField(max_length=255)),
                (
                    "extension",
                    models.CharField(
                        choices=[
                            ("aac", "aac"),
                            ("ai", "ai"),
                            ("avi", "avi"),
                            ("bmp", "bmp"),
                            ("bpg", "bpg"),
                            ("csv", "csv"),
                            ("doc", "doc"),
                            ("docx", "docx"),
                            ("exif", "exif"),
                            ("flac", "flac"),
                            ("flv", "flv"),
                            ("gif", "gif"),
                            ("jpeg", "jpeg"),
                            ("jpg", "jpg"),
                            ("m4a", "m4a"),
                            ("m4v", "m4v"),
                            ("mkv", "mkv"),
                            ("mov", "mov"),
                            ("mp3", "mp3"),
                            ("mp4", "mp4"),
                            ("mpeg4", "mpeg4"),
                            ("pdf", "pdf"),
                            ("png", "png"),
                            ("potx", "potx"),
                            ("ppt", "ppt"),
                            ("pptm", "pptm"),
                            ("pptx", "pptx"),
                            ("psd", "psd"),
                            ("svg", "svg"),
                            ("tar", "tar"),
                            ("tif", "tif"),
                            ("tiff", "tiff"),
                            ("txt", "txt"),
                            ("vsd", "vsd"),
                            ("vsdx", "vsdx"),
                            ("wav", "wav"),
                            ("webm", "webm"),
                            ("webp", "webp"),
                            ("wma", "wma"),
                            ("wmv", "wmv"),
                            ("xls", "xls"),
                            ("xlsb", "xlsb"),
                            ("xlsm", "xlsm"),
                            ("xlsx", "xlsx"),
                            ("zip", "zip"),
                        ],
                        db_index=True,
                        editable=False,
                        max_length=10,
                    ),
                ),
                (
                    "media_type",
                    models.CharField(
                        choices=[
                            ("archive", "archive"),
                            ("audio", "audio"),
                            ("document", "document"),
                            ("image", "image"),
                            ("miscellaneous", "miscellaneous"),
                            ("presentation", "presentation"),
                            ("spreadsheet", "spreadsheet"),
                            ("video", "video"),
                        ],
                        db_index=True,
                        editable=False,
                        max_length=100,
                    ),
                ),
                ("size", models.PositiveIntegerField(editable=False)),
                (
                    "height",
                    models.PositiveIntegerField(blank=True, editable=False, null=True),
                ),
                (
                    "width",
                    models.PositiveIntegerField(blank=True, editable=False, null=True),
                ),
                (
                    "dpi",
                    models.PositiveIntegerField(blank=True, editable=False, null=True),
                ),
                ("checksum", models.CharField(max_length=32)),
                ("weight", models.SmallIntegerField(db_index=True, default=0)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
            ],
            options={
                "ordering": ["weight", "created"],
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
    ]
