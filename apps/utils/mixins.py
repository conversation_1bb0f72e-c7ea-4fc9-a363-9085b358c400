from typing import List

from django.conf import settings
from django.contrib.auth.mixins import LoginRequiredMixin as OldLoginRequiredMixin
from django.contrib.auth.mixins import UserPassesTestMixin
from django.core.exceptions import PermissionDenied
from django.shortcuts import get_object_or_404, redirect
from django.template.response import TemplateResponse
from django.urls import reverse

from apps.organizations.models import CompanyAdmin
from apps.users.models import User


class AccessMixin(object):
    def handle_no_permission(self):
        if self.request.user.is_anonymous:
            return super(AccessMixin, self).handle_no_permission()
        raise PermissionDenied


class LoginRequiredMixin(AccessMixin, OldLoginRequiredMixin):
    pass


class CompanyAdminRequiredMixin(AccessMixin, UserPassesTestMixin):
    def test_func(self):
        return self.request.user.is_company_admin


class CompanyCompanyAdminRequiredMixin:
    """
    View mixin that ensures the logged-in user is an active admin
    of the company associated with the current request.
    """

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            raise PermissionDenied("You must be logged in.")

        if not CompanyAdmin.objects.filter(user=request.user).exists():
            raise PermissionDenied("You must be an admin of this company.")

        return super().dispatch(request, *args, **kwargs)


class ProjectDeactivatedMixin(AccessMixin, UserPassesTestMixin):
    def test_func(self):
        if hasattr(self, "project"):
            project = self.project
        elif hasattr(self, "get_object") and callable(getattr(self, "get_object")):
            project = self.get_object()
        else:
            raise Exception(
                f"{self.__class__.__name__} does not have a property named 'project' or a method named 'get_object()'."
            )
        return project.active

    def handle_no_permission(self):
        project = self.project if hasattr(self, "project") else self.get_object()
        return TemplateResponse(
            request=self.request,
            template="projects/project_detail_deactivated.html",
            context={"project": project},
        )


class SuccessUrlNextMixin:
    def get_success_url(self):
        next_url = self.request.GET.get("next")
        if next_url:
            return next_url
        return super().get_success_url()


class FormWizardMixin:
    steps = 1
    step = 1
    completed_step_list = []

    def get_completed_step_list(self) -> List[int]:
        return self.completed_step_list

    def get_context_data(self, **kwargs) -> dict:
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "steps": self.steps,
                "step": self.step,
                "first_step": 1,
                "last_step": self.steps,
                "has_next_step": self.step < self.steps,
                "has_previous_step": self.step > 1,
                "next_step": self.step + 1 if self.step < self.steps else None,
                "previous_step": self.step - 1 if self.step > 1 else None,
                "step_list": list(range(1, self.steps + 1)),
                "completed_step_list": self.get_completed_step_list(),
            }
        )
        return context
