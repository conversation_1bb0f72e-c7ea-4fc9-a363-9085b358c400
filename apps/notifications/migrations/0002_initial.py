# Generated by Django 5.2.4 on 2025-10-28 00:03

import auto_prefetch
import django.db.models.deletion
from django.conf import settings
from django.db import migrations


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("notifications", "0001_initial"),
        ("organizations", "0001_initial"),
        ("projects", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="message",
            name="creator",
            field=auto_prefetch.ForeignKey(
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="message",
            name="project",
            field=auto_prefetch.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="projects.project",
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="company",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="organizations.company"
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="message",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="notifications.message"
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="recipient",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="subscription",
            name="subscriber",
            field=auto_prefetch.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AlterUniqueTogether(
            name="subscription",
            unique_together={("subscriber", "message_type")},
        ),
    ]
