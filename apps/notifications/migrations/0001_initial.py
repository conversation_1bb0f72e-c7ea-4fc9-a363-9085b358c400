# Generated by Django 5.2.4 on 2025-10-28 00:03

import apps.notifications.models
import apps.utils.models
import auto_prefetch
import django.db.models.deletion
import django.db.models.manager
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "priority",
                    apps.utils.models.EnumField(
                        choices=[
                            ("low", "low"),
                            ("medium", "medium"),
                            ("high", "high"),
                        ],
                        default="medium",
                        enum=apps.notifications.models.Priority,
                        max_length=50,
                    ),
                ),
                ("is_viewed", models.BooleanField(default=False)),
                ("is_emailed", models.BooleanField(default=False)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("modified", models.DateTimeField(auto_now=True)),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="Subscription",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "message_type",
                    apps.utils.models.EnumField(
                        choices=[
                            ("New Project or Program", "New Project or Program"),
                            ("Role Assigned to Me", "Role Assigned to Me"),
                            ("Comment Added", "Comment Added"),
                            (
                                "Project Marked with Tech Components",
                                "Project Marked with Tech Components",
                            ),
                            ("Project State Change", "Project State Change"),
                            ("Project Phase Change", "Project Phase Change"),
                            ("Overall Health Change", "Overall Health Change"),
                            ("Status Update", "Status Update"),
                            (
                                "Project CapEx or OpEx Update",
                                "Project CapEx or OpEx Update",
                            ),
                            (
                                "Project Benefits Tracking Update",
                                "Project Benefits Tracking Update",
                            ),
                            ("Deleted Project", "Deleted Project"),
                            (
                                "Corporate Communication Needed",
                                "Corporate Communication Needed",
                            ),
                            ("New Purchase", "New Purchase"),
                            (
                                "Product Segment Legal Accepted",
                                "Product Segment Legal Accepted",
                            ),
                            (
                                "Product Segment Legal Rejected",
                                "Product Segment Legal Rejected",
                            ),
                            (
                                "Product Submitted for Reevaluation",
                                "Product Submitted for Reevaluation",
                            ),
                            (
                                "Product Submitted to Corporate Legal",
                                "Product Submitted to Corporate Legal",
                            ),
                            (
                                "Product Corporate Legal Approved",
                                "Product Corporate Legal Approved",
                            ),
                            (
                                "Product Corporate Legal Rejected",
                                "Product Corporate Legal Rejected",
                            ),
                            (
                                "Product Corporate Legal Revision Requested",
                                "Product Corporate Legal Revision Requested",
                            ),
                            ("Product Comment", "Product Comment"),
                            ("Document Needs Review", "Document Needs Review"),
                            ("Tollgate Held", "Tollgate Held"),
                            ("Added to Agenda", "Added to Agenda"),
                            ("Removed from Agenda", "Removed from Agenda"),
                            ("Tollgate Agenda Finalized", "Tollgate Agenda Finalized"),
                            ("New Idea", "New Idea"),
                            ("Ready to Convert", "Ready to Convert"),
                            ("Converted Idea", "Converted Idea"),
                            ("Deleted Idea", "Deleted Idea"),
                            ("Workflow Dates Set", "Workflow Dates Set"),
                            ("Workflow Step Start", "Workflow Step Start"),
                            ("3 Day Warning", "3 Day Warning"),
                            ("24 Hour Warning", "24 Hour Warning"),
                            ("Workflow Ended Alert", "Workflow Ended Alert"),
                            ("Allocation Completed", "Allocation Completed"),
                        ],
                        enum=apps.notifications.models.MessageTypes,
                        max_length=50,
                    ),
                ),
                (
                    "frequency",
                    models.CharField(
                        choices=[
                            ("instant", "Instant"),
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("never", "Never"),
                        ],
                        default="weekly",
                        max_length=50,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="Message",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("object_id", models.PositiveIntegerField(null=True)),
                (
                    "message_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("New Project or Program", "New Project or Program"),
                            ("Role Assigned to Me", "Role Assigned to Me"),
                            ("Comment Added", "Comment Added"),
                            (
                                "Project Marked with Tech Components",
                                "Project Marked with Tech Components",
                            ),
                            ("Project State Change", "Project State Change"),
                            ("Project Phase Change", "Project Phase Change"),
                            ("Overall Health Change", "Overall Health Change"),
                            ("Status Update", "Status Update"),
                            (
                                "Project CapEx or OpEx Update",
                                "Project CapEx or OpEx Update",
                            ),
                            (
                                "Project Benefits Tracking Update",
                                "Project Benefits Tracking Update",
                            ),
                            ("Deleted Project", "Deleted Project"),
                            (
                                "Corporate Communication Needed",
                                "Corporate Communication Needed",
                            ),
                            ("New Purchase", "New Purchase"),
                            (
                                "Product Segment Legal Accepted",
                                "Product Segment Legal Accepted",
                            ),
                            (
                                "Product Segment Legal Rejected",
                                "Product Segment Legal Rejected",
                            ),
                            (
                                "Product Submitted for Reevaluation",
                                "Product Submitted for Reevaluation",
                            ),
                            (
                                "Product Submitted to Corporate Legal",
                                "Product Submitted to Corporate Legal",
                            ),
                            (
                                "Product Corporate Legal Approved",
                                "Product Corporate Legal Approved",
                            ),
                            (
                                "Product Corporate Legal Rejected",
                                "Product Corporate Legal Rejected",
                            ),
                            (
                                "Product Corporate Legal Revision Requested",
                                "Product Corporate Legal Revision Requested",
                            ),
                            ("Product Comment", "Product Comment"),
                            ("Document Needs Review", "Document Needs Review"),
                            ("Tollgate Held", "Tollgate Held"),
                            ("Added to Agenda", "Added to Agenda"),
                            ("Removed from Agenda", "Removed from Agenda"),
                            ("Tollgate Agenda Finalized", "Tollgate Agenda Finalized"),
                            ("New Idea", "New Idea"),
                            ("Ready to Convert", "Ready to Convert"),
                            ("Converted Idea", "Converted Idea"),
                            ("Deleted Idea", "Deleted Idea"),
                            ("Workflow Dates Set", "Workflow Dates Set"),
                            ("Workflow Step Start", "Workflow Step Start"),
                            ("3 Day Warning", "3 Day Warning"),
                            ("24 Hour Warning", "24 Hour Warning"),
                            ("Workflow Ended Alert", "Workflow Ended Alert"),
                            ("Allocation Completed", "Allocation Completed"),
                        ],
                        max_length=255,
                        null=True,
                    ),
                ),
                ("text", models.TextField(max_length=1000)),
                ("created", models.DateTimeField(auto_now_add=True)),
                (
                    "content_type",
                    auto_prefetch.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "base_manager_name": "prefetch_manager",
            },
            managers=[
                ("objects", django.db.models.manager.Manager()),
                ("prefetch_manager", django.db.models.manager.Manager()),
            ],
        ),
    ]
