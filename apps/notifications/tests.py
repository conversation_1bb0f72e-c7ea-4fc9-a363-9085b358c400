import random
from datetime import datetime, timedelta

from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from freezegun import freeze_time
from pytz import UTC

from apps.programs.models import Program
from apps.projects.forms import ProjectForm
from apps.projects.models import Project
from apps.users.models import User
from apps.projects.tests.data import generate_project_data
from apps.projects.tests.factories import ProjectFactory
from apps.users.tests import UserFactory
from apps.users.tests import UserFactory
from tests.base import BaseTestCase

from ..documents.tests import DocumentFactory, generate_document_data
from .models import Message, MessageTypes, ReceiverRule, Subscription, notify
from .utils import get_email_recipients_for_rollup, get_weekly_digest_date_range


class ReceiverRuleModelProjectTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.anna: User = UserFactory(first_name="<PERSON>", last_name="<PERSON>")
        self.bob: User = UserFactory(first_name="<PERSON>", last_name="<PERSON>")
        self.jake: User = UserFactory(first_name="<PERSON>", last_name="<PERSON>")
        self.jenna: User = UserFactory(first_name="<PERSON>", last_name="<PERSON>")
        self.mary: User = UserFactory(first_name="Mary", last_name="Smith")
        self.mike: User = UserFactory(first_name="Mike", last_name="Smith")
        self.sarah: User = UserFactory(first_name="Sarah", last_name="Smith")
        self.steve: User = UserFactory(first_name="Steve", last_name="Smith")
        self.tanya: User = UserFactory(
            first_name="Tanya", last_name="Smith", is_compliance=True
        )
        self.tim: User = UserFactory(
            first_name="Tim", last_name="Smith", is_finance=True, is_active=True
        )
        self.wayne: User = UserFactory(
            first_name="Wayne",
            last_name="Smith",
            is_solutions_architecture=True,
            is_active=True,
        )
        self.project = Project.objects.create(
            name="My Project",
            summary="A description of my project.",
            created_by=UserFactory(),
        )
        self.project.executive_owners.add(self.anna)
        self.project.business_leads.add(self.bob)
        self.project.business_analysts.add(self.jake, self.jenna)
        self.project.finance_leads.add(self.mary)
        self.project.other_stakeholders.add(self.mike)

        self.project.project_managers.add(self.steve)

    def test_get_recipients_new_project(self):
        message_type = MessageTypes.PROJECT_NEW
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.project
        )
        expected_recipients = [
            self.anna,
            self.bob,
            self.jake,
            self.jenna,
            self.mary,
            self.mike,
            self.sarah,
            self.steve,
            self.tim,
        ]
        self.assertCountEqual(list(recipients), expected_recipients)

    def test_get_recipients_comment(self):
        message_type = MessageTypes.COMMENT_ADDED
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.project
        )
        expected_recipients = [self.bob, self.jake, self.jenna, self.steve]
        self.assertCountEqual(list(recipients), expected_recipients)

    def test_get_recipients_tech_component(self):
        message_type = MessageTypes.PROJECT_TECH_COMPONENTS
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.project
        )
        expected_recipients = [self.sarah, self.steve, self.tanya, self.wayne]
        self.assertCountEqual(list(recipients), expected_recipients)

    def test_get_recipients_state_change(self):
        message_type = MessageTypes.PROJECT_STATE_CHANGE
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.project
        )
        expected_recipients = [
            self.anna,
            self.bob,
            self.jake,
            self.jenna,
            self.sarah,
            self.steve,
        ]
        self.assertCountEqual(list(recipients), expected_recipients)

    def test_get_recipients_phase_change(self):
        message_type = MessageTypes.PROJECT_PHASE_CHANGE
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.project
        )
        expected_recipients = [
            self.bob,
            self.jake,
            self.jenna,
            self.sarah,
            self.steve,
            self.tanya,
            self.tim,
            self.wayne,
        ]
        self.assertCountEqual(list(recipients), expected_recipients)

    def test_get_recipients_overall_health_change(self):
        message_type = MessageTypes.HEALTH_CHANGE
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.project
        )
        expected_recipients = [
            self.anna,
            self.bob,
            self.jake,
            self.jenna,
            self.mary,
            self.mike,
            self.sarah,
            self.steve,
        ]
        self.assertCountEqual(list(recipients), expected_recipients)

    def test_get_recipients_status_update(self):
        message_type = MessageTypes.STATUS_UPDATE
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.project
        )
        expected_recipients = [
            self.anna,
            self.bob,
            self.jake,
            self.jenna,
            self.sarah,
            self.steve,
        ]
        self.assertCountEqual(list(recipients), expected_recipients)

    def test_get_recipients_new_spend_data(self):
        message_type = MessageTypes.NEW_SPEND_DATA
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.project
        )
        expected_recipients = [self.bob, self.steve, self.tim]
        self.assertCountEqual(list(recipients), expected_recipients)

    def test_get_recipients_new_benefit_tracking_data(self):
        message_type = MessageTypes.NEW_BENEFIT_TRACKING_DATA
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.project
        )
        expected_recipients = [self.bob, self.steve]
        self.assertCountEqual(list(recipients), expected_recipients)

    def test_get_recipients_project_deleted(self):
        message_type = MessageTypes.PROJECT_DELETED
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.project
        )
        expected_recipients = [self.bob, self.jake, self.jenna, self.sarah, self.steve]
        self.assertCountEqual(list(recipients), expected_recipients)


class ReceiverRuleModelProgramTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.anna: User = UserFactory(
            first_name="Anna", last_name="Smith", is_active=True
        )
        self.bob: User = UserFactory(
            first_name="Bob", last_name="Smith", is_active=True
        )
        self.jake: User = UserFactory(
            first_name="Jake", last_name="Smith", is_active=True
        )
        self.jenna: User = UserFactory(
            first_name="Jenna", last_name="Smith", is_active=True
        )
        self.program = Program.objects.create(
            name="My Program",
            summary="A description of my program.",
            created_by=UserFactory(),
        )
        self.program.program_managers.add(self.anna)
        self.program.executive_sponsors.add(self.bob)
        self.program.shared_with.add(self.jake, self.jenna)

    def test_get_recipients_new_program(self):
        message_type = MessageTypes.PROJECT_NEW
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.program
        )
        expected_recipients = [self.anna, self.bob, self.jake, self.jenna]
        self.assertCountEqual(list(recipients), expected_recipients)

    def test_get_recipients_comment(self):
        message_type = MessageTypes.COMMENT_ADDED
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.program
        )
        expected_recipients = [self.anna, self.bob]
        self.assertCountEqual(list(recipients), expected_recipients)


class ReceiverRuleModelDocumentTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()

        self.andrew = User.objects.get(email=self.admin_user.email)
        self.document = DocumentFactory()

    def test_get_recipients_document_review_needed(self):
        message_type = MessageTypes.DOCUMENT_REVIEW_NEEDED
        recipients = ReceiverRule.objects.get_recipients(
            message_type=message_type, content_object=self.document
        )
        expected_recipients = [self.andrew]
        self.assertCountEqual(list(recipients), expected_recipients)


class NotifyTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.anna: User = UserFactory(
            first_name="Anna", last_name="Smith", is_active=True
        )
        self.bob: User = UserFactory(
            first_name="Bob", last_name="Smith", is_active=True
        )
        self.jake: User = UserFactory(
            first_name="Jake", last_name="Smith", is_active=True
        )
        self.jenna: User = UserFactory(
            first_name="Jenna", last_name="Smith", is_active=True
        )
        self.mary: User = UserFactory(
            first_name="Mary", last_name="Smith", is_active=True
        )
        self.mike: User = UserFactory(
            first_name="Mike", last_name="Smith", is_active=True
        )
        self.sarah: User = UserFactory(
            first_name="Sarah", last_name="Smith", is_active=True
        )
        self.steve: User = UserFactory(
            first_name="Steve", last_name="Smith", is_active=True
        )
        self.tanya: User = UserFactory(
            first_name="Tanya", last_name="Smith", is_compliance=True
        )
        self.tim: User = UserFactory(
            first_name="Tim", last_name="Smith", is_finance=True, is_active=True
        )
        self.wayne: User = UserFactory(
            first_name="Wayne",
            last_name="Smith",
            is_solutions_architecture=True,
            is_active=True,
        )
        self.yolanda: User = UserFactory(first_name="Yolanda", last_name="Smith")
        self.project = Project.objects.create(
            name="My Project",
            summary="A description of my project.",
            created_by=UserFactory(),
        )
        self.project.executive_owners.add(self.anna)
        self.project.business_leads.add(self.bob)
        self.project.business_analysts.add(self.jake, self.jenna)
        self.project.finance_leads.add(self.mary)
        self.project.other_stakeholders.add(self.mike)

        self.project.project_managers.add(self.steve)

    def test_notify(self):
        notify(
            message_type=MessageTypes.PROJECT_NEW,
            message="A new project was created.",
            creator=UserFactory(),
            content_object=self.project,
        )
        expected_recipients = [
            self.anna,
            self.bob,
            self.jake,
            self.jenna,
            self.mary,
            self.mike,
            self.sarah,
            self.steve,
            self.tim,
        ]
        project_type = ContentType.objects.get_for_model(Project)
        recipients = User.objects.filter(
            notification__message__content_type=project_type,
            notification__message__object_id=self.project.pk,
        )
        self.assertCountEqual(list(recipients), expected_recipients)

    def test_notify_with_additional_recipients(self):
        notify(
            message_type=MessageTypes.PROJECT_NEW,
            message="A new project was created.",
            creator=UserFactory(),
            content_object=self.project,
            recipients=[self.yolanda],
        )
        expected_recipients = [
            self.anna,
            self.bob,
            self.jake,
            self.jenna,
            self.mary,
            self.mike,
            self.sarah,
            self.steve,
            self.tim,
            self.yolanda,
        ]
        project_type = ContentType.objects.get_for_model(Project)
        recipients = User.objects.filter(
            notification__message__content_type=project_type,
            notification__message__object_id=self.project.pk,
        )
        self.assertCountEqual(list(recipients), expected_recipients)

    def test_notify_with_only_recipients(self):
        notify(
            message_type=MessageTypes.ROLE_ASSIGNED,
            message="You were added to a project.",
            creator=UserFactory(),
            content_object=self.project,
            recipients=[self.jake],
        )
        expected_recipients = [self.jake]
        project_type = ContentType.objects.get_for_model(Project)
        recipients = User.objects.filter(
            notification__message__content_type=project_type,
            notification__message__object_id=self.project.pk,
        )
        self.assertCountEqual(list(recipients), expected_recipients)


class ProjectNotificationTestCase(BaseTestCase):
    fixtures = ["divisions"]

    def test_create_view(self):
        self.client.force_login(self.admin_user)
        url = reverse("project_create")
        data = generate_project_data()
        response = self.client.post(url, data=data)
        if response.status_code == 200:
            form = response.context["form"]
            self.assertFalse(form.errors)
            formsets = response.context["inlines"]
            for formset in formsets:
                self.assertFalse(formset.errors)
        self.assertEqual(response.status_code, 302)
        message_type = MessageTypes.PROJECT_NEW
        message_exists = Message.objects.filter(message_type=message_type).exists()
        self.assertTrue(message_exists)

    def test_update_view(self):
        project: Project = ProjectFactory()

        self.client.force_login(self.admin_user)
        url = reverse("project_update", args=[project.pk])
        form = ProjectForm(instance=project, user=self.admin_user)
        data = generate_project_data()
        # data.update(form.initial)
        response = self.client.post(url, data=data)
        if response.status_code == 200:
            form = response.context["form"]
            self.assertFalse(form.errors)
            formsets = response.context["inlines"]
            for formset in formsets:
                self.assertFalse(formset.errors)
        else:
            self.assertRedirects(
                response, f"{reverse('project_detail', args=[project.pk])}"
            )


class DocumentNotificationTestCase(BaseTestCase):
    def test_create_view(self):
        self.client.force_login(self.regular_user)
        url = reverse("document_create")
        data = generate_document_data()
        response = self.client.post(url, data=data)
        if response.status_code == 200:
            form = response.context["form"]
            self.assertFalse(form.errors)
        self.assertEqual(response.status_code, 302)
        message_type = MessageTypes.DOCUMENT_REVIEW_NEEDED
        message_exists = Message.objects.filter(message_type=message_type).exists()
        self.assertTrue(message_exists)


class UtilsTestCase(BaseTestCase):
    def test_get_weekly_digest_date_range(self):
        with freeze_time("2016-10-16"):
            start_date, end_date = get_weekly_digest_date_range()
            self.assertAlmostEquals(
                start_date,
                datetime(2016, 10, 7, 8, 0, tzinfo=UTC),
                delta=timedelta(seconds=1),
            )
            self.assertAlmostEquals(
                end_date,
                datetime(2016, 10, 14, 8, 0, tzinfo=UTC),
                delta=timedelta(seconds=1),
            )

    def test_get_email_recipients(self):
        user = UserFactory()
        anna: Person = PersonFactory(first_name="Anna", last_name="Smith", active=True)
        bob: Person = PersonFactory(first_name="Bob", last_name="Smith", active=True)
        jake: Person = PersonFactory(first_name="Jake", last_name="Smith", active=True)
        with freeze_time("2014-01-08"):
            notify(
                message_type=MessageTypes.PROJECT_NEW,
                message="A new project was created.",
                creator=user,
                recipients=[anna, bob],
            )
            notify(
                message_type=MessageTypes.PROJECT_TECH_COMPONENTS,
                message="A technical component.",
                creator=user,
                recipients=[bob],
            )
        with freeze_time("2014-02-08"):
            recipients = list(get_email_recipients_for_rollup())
            self.assertIn(anna, recipients)
            self.assertIn(bob, recipients)
            self.assertNotIn(jake, recipients)


class NotificationListViewTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.project = Project.objects.create(
            name="My Project",
            summary="A description of my project.",
            created_by=UserFactory(),
        )
        for i in range(10):
            notify(
                message_type=MessageTypes.PROJECT_NEW,
                message="A new project was created.",
                creator=UserFactory(),
                content_object=self.project,
            )

    def test_admin_view(self):
        self.client.force_login(self.admin_user)
        url = reverse("notification_list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_authenticated_view(self):
        self.client.force_login(self.regular_user)
        url = reverse("notification_list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_anonymous_view(self):
        url = reverse("notification_list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_query_count(self):
        self.client.force_login(self.admin_user)
        url = reverse("notification_list")
        with self.assertNumQueries(73):
            self.client.get(url)

    def test_post(self):
        self.client.force_login(self.admin_user)
        person = User.objects.get(email=self.admin_user.email)
        data = {
            "form-TOTAL_FORMS": 30,
            "form-INITIAL_FORMS": 30,
            "form-MIN_NUM_FORMS": 0,
            "form-MAX_NUM_FORMS": 1000,
        }
        for index, message_type in MessageTypes:
            subscription = Subscription.objects.create(
                subscriber=person, message_type=message_type
            )
            data.update(
                {
                    f"form-{index}-id": subscription.id,
                    f"form-{index}-frequency": random.choice(
                        [
                            Subscription.FREQUENCY_INSTANT,
                            Subscription.FREQUENCY_DAILY,
                            Subscription.FREQUENCY_DAILY,
                            Subscription.FREQUENCY_NEVER,
                        ]
                    ),
                }
            )
        url = reverse("notification_list")
        response = self.client.post(url, data=data)
        if response.status_code == 200:
            formset = response.context["formset"]
            self.assertFalse(formset.errors)
        else:
            self.assertRedirects(response, reverse("notification_list"))
