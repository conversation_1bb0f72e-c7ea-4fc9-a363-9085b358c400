import logging
from datetime import datetime, timedelta
from itertools import groupby
from typing import Iterable, <PERSON>, Tuple, Union

from croniter import croniter
from django.contrib.auth import get_user_model
from django.utils import timezone

from apps.documents.models import Document
from apps.meetings.models import Meeting, MeetingItem
from apps.notifications.models import (
    MessageTypes,
    Notification,
    Priority,
    ReceiverRule,
    Role,
    Scope,
)
from apps.notifications.view_models import (
    EmailViewMeetingItem,
    EmailViewNotification,
    EmailViewProgram,
    EmailViewProject,
)
from apps.programs.models import Program
from apps.projects.models import Project
from apps.users.models import User
from apps.purchases.models import Purchase

from .data import receiver_rules
from .models import Subscription

User = get_user_model()


logger = logging.getLogger("site")


def get_weekly_digest_date_range() -> Tuple[datetime, datetime]:
    date_iter = croniter(
        Subscription.FREQUENCY_CRON[Subscription.FREQUENCY_WEEKLY],
        start_time=timezone.now(),
        ret_type=datetime,
    )
    end_date = date_iter.get_prev()
    start_date = end_date - timedelta(weeks=1)
    return start_date, end_date


def get_object_for_email_view_notification(
    content_object,
) -> Union[EmailViewMeetingItem, EmailViewProject, EmailViewProgram, None]:
    if isinstance(content_object, Project):
        return EmailViewProject(
            id=content_object.id,
            name=content_object.name,
            phase=content_object.phase_display,
            overall_health=(
                content_object.current_health.health_display
                if content_object.current_health
                else "N/A"
            ),
            progress=(
                content_object.percent_complete.percentage
                if content_object.percent_complete
                else 0
            ),
        )
    elif isinstance(content_object, MeetingItem):
        return EmailViewMeetingItem(
            id=content_object.project.id,
            name=content_object.project.name,
            description="{result} at the {phase} Tollgate".format(
                result=content_object.get_result(),
                phase=content_object.project.phase_display.title(),
            ),
            phase=content_object.project.phase_display,
            overall_health=content_object.project.current_health.health_display,
            progress=(
                content_object.project.percent_complete.percentage
                if content_object.project.percent_complete
                else 0
            ),
        )
    elif isinstance(content_object, Program):
        return EmailViewProgram(id=content_object.id, name=content_object.name)
    return None


def group_notifications_by_message_type(
    notifications: Iterable[Notification],
) -> Iterable[Tuple[str, Iterable[EmailViewNotification]]]:
    notifications = (
        EmailViewNotification(
            message_type=notification.message.message_type.name,
            icon_url=notification.message.notification_email_icon,
            creator=notification.message.creator.full_name,
            created=notification.created,
            object=get_object_for_email_view_notification(
                notification.message.content_object
            ),
        )
        for notification in notifications
    )
    return [
        (grouper, list(values))
        for grouper, values in groupby(notifications, key=lambda x: x.message_type)
    ]


def get_email_recipients_for_rollup() -> Iterable[User]:
    recipients = User.objects.filter(
        is_active=True,
        notification__is_emailed=False,
        notification__priority=Priority.MEDIUM,
    ).distinct()
    return (
        recipient
        for recipient in recipients
        if Notification.objects.has_ready_to_email_for_rollup(recipient)
    )


def get_email_recipients_for_individual() -> Iterable[User]:
    recipients = User.objects.filter(
        is_active=True,
        notification__is_emailed=False,
        notification__priority=Priority.HIGH,
    ).distinct()
    return (
        recipient
        for recipient in recipients
        if Notification.objects.has_ready_to_email_for_individual(recipient)
    )


def get_default_subscription_frequency(message_type: MessageTypes) -> str:
    if message_type == MessageTypes.DOCUMENT_REVIEW_NEEDED:
        return Subscription.FREQUENCY_INSTANT
    else:
        return Subscription.FREQUENCY_WEEKLY
