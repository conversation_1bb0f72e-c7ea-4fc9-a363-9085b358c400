import logging
from datetime import date
from email.mime.image import MIMEImage
from typing import Any, Dict, List

from django.conf import settings
from django.contrib.staticfiles.storage import staticfiles_storage
from django.core.mail.message import EmailMultiAlternatives
from django.db.models import QuerySet
from django.http.request import HttpRequest
from django.http.response import HttpResponse, JsonResponse
from django.shortcuts import redirect
from django.template.loader import render_to_string
from django.urls import reverse
from django.views.generic.detail import DetailView
from django.views.generic.list import ListView
from rest_framework import viewsets

from apps.documents.view_models import EmailDocument
from apps.meetings.models import Meeting
from apps.projects.models import Project
from apps.users.models import User
from apps.utils.dates import format_date_range
from apps.utils.mixins import CompanyAdminRequiredMixin, LoginRequiredMixin
from apps.organizations.mixins import CompanyQuerysetMixin

from .forms import NotificationFilterForm
from .models import MessageTypes, Notification, Subscription
from .serializers import SubscriptionSerializer
from .utils import (
    get_default_subscription_frequency,
    get_email_recipients_for_individual,
    get_email_recipients_for_rollup,
    get_weekly_digest_date_range,
    group_notifications_by_message_type,
)
from .view_models import EmailMeetingItem, EmailPlanner, EmailProduct, EmailProject

logger = logging.getLogger("site")


class NotificationListView(LoginRequiredMixin, ListView):
    queryset = Notification.objects.select_related("message", "recipient").order_by(
        "-created"
    )
    paginate_by = 50

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        queryset = queryset.filter(recipient__email=self.request.user.email)
        NotificationFilterForm(data=self.request.GET, user=self.request.user)
        return queryset

    def mark_notifications_viewed(self):
        notifications = self.queryset.filter(is_viewed=False)
        for notification in notifications:
            notification.mark_viewed()

    def get_serializer(self) -> SubscriptionSerializer:
        person, _ = User.objects.get_or_create(email=self.request.user.email)
        for message_type in MessageTypes:
            if not Subscription.objects.filter(
                subscriber=person, message_type=message_type
            ).exists():
                frequency = get_default_subscription_frequency(message_type)
                Subscription.objects.create(
                    subscriber=person, message_type=message_type, frequency=frequency
                )
        return SubscriptionSerializer(
            Subscription.objects.filter(subscriber=person), many=True
        )

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["filter_form"] = NotificationFilterForm(
            data=self.request.GET, user=self.request.user
        )
        context["subscriptions_json"] = self.get_serializer().data
        return context

    def get(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse:
        response = super().get(request, *args, **kwargs)
        if response.status_code == 200:
            self.mark_notifications_viewed()
        return response

    def post(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse:
        formset = self.get_form()
        if formset.is_valid():
            formset.save()
            return redirect("notification_list")
        self.object_list = self.get_queryset()
        context = self.get_context_data(formset=formset)
        return self.render_to_response(context=context)


class SubscriptionViewSet(viewsets.ModelViewSet):
    """
    Edit subscriptions associated with the user.
    """

    serializer_class = SubscriptionSerializer

    def get_queryset(self):
        return Subscription.objects.filter(subscriber__email=self.request.user.email)

    def put(self, request, *args, **kwargs):
        for obj in request.data:
            Subscription.objects.filter(
                id=obj["id"], subscriber__email=request.user.email
            ).update(frequency=obj["value"])
        subs = Subscription.objects.filter(
            id__in=[obj["id"] for obj in request.data],
            subscriber__email=request.user.email,
        )
        return JsonResponse(SubscriptionSerializer(subs, many=True).data, safe=False)


class NotificationRollUpEmailSender:
    def __init__(self, whitelist_domains: List[str] = None):
        if whitelist_domains is None:
            whitelist_domains = []
        self.whitelist_domains = whitelist_domains

    def get_context_data(self, recipient: User, frequency: str) -> Dict[str, Any]:
        context: Dict[str, Any] = {}
        context["site_url"] = getattr(settings, "SITE_URL", "")
        context["frequency"] = frequency
        if frequency == Subscription.FREQUENCY_INSTANT:
            context["digest_date"] = ""
            context["show_digest_date"] = False
            context["show_long_title"] = False
        elif frequency == Subscription.FREQUENCY_DAILY:
            context["digest_date"] = date.today().strftime("%b %d, %Y")
            context["show_digest_date"] = True
            context["show_long_title"] = True
        elif frequency == Subscription.FREQUENCY_WEEKLY:
            start_date, end_date = get_weekly_digest_date_range()
            context["digest_date"] = format_date_range(start_date, end_date)
            context["show_digest_date"] = True
            context["show_long_title"] = True
        notifications = Notification.objects.get_ready_to_email_for_rollup(
            recipient=recipient, frequency=frequency
        )
        context["notification_groups"] = group_notifications_by_message_type(
            notifications
        )
        return context

    def send(self):
        subject = "Project Tracker Notifications"
        recipients = get_email_recipients_for_rollup()
        if len(self.whitelist_domains):
            recipients = (
                recipient
                for recipient in recipients
                if any(
                    recipient.email.endswith(f"@{domain}")
                    for domain in self.whitelist_domains
                )
            )
        for recipient in recipients:
            for frequency in [
                Subscription.FREQUENCY_INSTANT,
                Subscription.FREQUENCY_DAILY,
                Subscription.FREQUENCY_WEEKLY,
            ]:
                notifications = Notification.objects.get_ready_to_email_for_rollup(
                    recipient=recipient, frequency=frequency
                )
                if len(notifications) == 0:
                    continue
                context = self.get_context_data(
                    recipient=recipient, frequency=frequency
                )
                text_content = render_to_string(
                    "notifications/emails/list_notifications.txt", context
                )
                html_content = render_to_string(
                    "notifications/emails/list_notifications.html", context
                )
                to = [f"{recipient.full_name} <{recipient.email}>"]
                message = EmailMultiAlternatives(
                    subject=subject,
                    body=text_content,
                    from_email=settings.SERVER_EMAIL,
                    to=to,
                )
                message.mixed_subtype = "related"
                image_files = [
                    "icon-digest.png",
                    "logo.png",
                    "icon-notification-new.png",
                    "icon-notification-person.png",
                    "icon-notification-gear.png",
                    "icon-notification-pencil.png",
                    "icon-notification-heart.png",
                    "icon-notification-money.png",
                    "icon-notification-trash.png",
                ]
                for notification in notifications:
                    if notification.message.notification_email_icon:
                        image_files.append(notification.message.notification_email_icon)
                for image_file in image_files:
                    with staticfiles_storage.open(
                        f"dist/images/emails/{image_file}"
                    ) as f:
                        header = MIMEImage(f.read())
                        header.add_header("Content-ID", f"<{image_file}>")
                        message.attach(header)
                message.attach_alternative(html_content, "text/html")
                message.send(fail_silently=False)
                for notification in notifications:
                    notification.mark_emailed()


class NotificationIndividualEmailSender:
    def __init__(self, whitelist_domains: List[str] = None):
        if whitelist_domains is None:
            whitelist_domains = []
        self.whitelist_domains = whitelist_domains

    def send(self):
        recipients = get_email_recipients_for_individual()
        if len(self.whitelist_domains):
            recipients = (
                recipient
                for recipient in recipients
                if any(
                    recipient.email.endswith(f"@{domain}")
                    for domain in self.whitelist_domains
                )
            )
        for recipient in recipients:
            notifications = Notification.objects.get_ready_to_email_for_individual(
                recipient=recipient
            )
            for notification in notifications:
                if (
                    notification.message.message_type
                    == MessageTypes.DOCUMENT_REVIEW_NEEDED
                ):
                    sender = NotificationDocumentEmailSender()
                elif notification.message.message_type in [
                    MessageTypes.PURCHASE_NEW,
                    MessageTypes.PURCHASE_REPROPOSED,
                    MessageTypes.PURCHASE_COMMENT,
                    MessageTypes.PURCHASE_APPROVED,
                    MessageTypes.PURCHASE_REJECTED,
                    MessageTypes.PURCHASE_DRAFTED,
                    MessageTypes.PURCHASE_SEGMENT_ACCEPTED,
                    MessageTypes.PURCHASE_SEGMENT_REJECTED,
                ]:
                    sender = NotificationPurchaseEmailSender()
                elif notification.message.message_type == MessageTypes.TOLLGATE_HELD:
                    sender = NotificationTollgateEmailSender()
                elif (
                    notification.message.message_type
                    == MessageTypes.TOLLGATE_AGENDA_FINALIZED
                ):
                    sender = NotificationTollgateAgendaEmailSender()
                elif notification.message.message_type in [
                    MessageTypes.PLANNER_WORKFLOW_DATES_SET,
                    MessageTypes.PLANNER_ALLOCATION_COMPLETED,
                    MessageTypes.PLANNER_ONE_DAY_WARNING,
                    MessageTypes.PLANNER_THREE_DAY_WARNING,
                    MessageTypes.PLANNER_WORKFLOW_ENDED,
                    MessageTypes.PLANNER_WORKFLOW_STEP_START,
                ]:
                    sender = NotificationPlannerSender()
                else:
                    continue
                sender.send(notification)


class NotificationDocumentEmailSender:
    def get_context_data(self, notification: Notification) -> Dict[str, Any]:
        context: Dict[str, Any] = {}
        context["site_url"] = getattr(settings, "SITE_URL", "")
        context["notification"] = notification
        document = notification.message.content_object
        context["document"] = EmailDocument(
            name=document.name,
            document_type=document.document_type,
            owner=document.owner.full_name,
            url=document.url,
            phase=[phase.name for phase in document.phase.all()],
            segment=[segment.abbreviation for segment in document.segment.all()],
            rigor=[rigor.name for rigor in document.rigor.all()],
            area=[area.name for area in document.area.all()],
            purpose=document.purpose.name,
            edit_url=reverse("document_update", args=[document.pk]),
        )
        return context

    def send(self, notification: Notification):
        subject = "Project Tracker Notification"
        context = self.get_context_data(notification)
        text_content = render_to_string(
            "notifications/emails/document_notification.txt", context
        )
        html_content = render_to_string(
            "notifications/emails/document_notification.html", context
        )
        to = [f"{notification.recipient.full_name} <{notification.recipient.email}>"]
        message = EmailMultiAlternatives(
            subject=subject, body=text_content, from_email=settings.SERVER_EMAIL, to=to
        )
        message.mixed_subtype = "related"
        image_files = ["logo.png", "document.png"]
        for image_file in image_files:
            with staticfiles_storage.open(f"dist/images/emails/{image_file}") as f:
                header = MIMEImage(f.read())
                header.add_header("Content-ID", f"<{image_file}>")
                message.attach(header)
        message.attach_alternative(html_content, "text/html")
        message.send(fail_silently=False)
        notification.mark_emailed()


class NotificationPlannerSender:
    def get_context_data(self, notification: Notification) -> Dict[str, Any]:
        timeline_list = notification.message.content_object.plantimeline_set.with_phase_order().order_by(
            "phase_order"
        )
        context: Dict[str, Any] = {
            "site_url": getattr(settings, "SITE_URL", ""),
            "notification": notification,
        }
        context["planner"] = EmailPlanner(
            year=notification.message.content_object.year,
            headline="",
            timeline_list=timeline_list,
        )
        if notification.message.message_type == MessageTypes.PLANNER_WORKFLOW_DATES_SET:
            context["planner"].headline = "Workflow Dates Set"
        elif (
            notification.message.message_type
            == MessageTypes.PLANNER_WORKFLOW_STEP_START
        ):
            context[
                "planner"
            ].headline = f"{notification.message.content_object.get_phase().label} Workflow Started"
            context["planner"].timeline_highlight = timeline_list.get(
                phase=notification.message.content_object.phase
            )
        elif notification.message.message_type == MessageTypes.PLANNER_ONE_DAY_WARNING:
            context[
                "planner"
            ].headline = f"{notification.message.content_object.get_phase().label} Workflow Ends in 24 Hours"
            context["planner"].timeline_alert = timeline_list.get(
                phase=notification.message.content_object.phase
            )
            context["button_url"] = "/planner"
            context["button_text"] = "Update Risk Factor"
        elif (
            notification.message.message_type == MessageTypes.PLANNER_THREE_DAY_WARNING
        ):
            context[
                "planner"
            ].headline = f"{notification.message.content_object.get_phase().label} Workflow Ends in 3 Days"
            context["planner"].timeline_alert = timeline_list.get(
                phase=notification.message.content_object.phase
            )
            context["planner"].button_url = "/planner"
            context["planner"].button_text = "View Plan"
        elif notification.message.message_type == MessageTypes.PLANNER_WORKFLOW_ENDED:
            context[
                "planner"
            ].headline = f"{notification.message.content_object.get_phase().label} Workflow Ended"
            context["planner"].timeline_alert = timeline_list.get(
                phase=notification.message.content_object.phase
            )
            context["planner"].button_url = "/planner"
            context["planner"].button_text = "View Plan"
        elif (
            notification.message.message_type
            == MessageTypes.PLANNER_ALLOCATION_COMPLETED
        ):
            context["planner"].headline = "Allocation Complete!"
            context["planner"].button_url = "/planner"
            context["planner"].button_text = "View Capital Plan"
        return context

    def get_template_name(self, notification: Notification) -> str:
        if (
            notification.message.message_type
            == MessageTypes.PLANNER_ALLOCATION_COMPLETED
        ):
            return "planner_allocated"
        return "planner_workflow_dates"

    def send(self, notification: Notification):
        subject = "Financial Planning Notification"
        context = self.get_context_data(notification)
        text_content = render_to_string(
            f"notifications/emails/{self.get_template_name(notification)}.txt", context
        )
        html_content = render_to_string(
            f"notifications/emails/{self.get_template_name(notification)}.html", context
        )
        to = [f"{notification.recipient.full_name} <{notification.recipient.email}>"]
        message = EmailMultiAlternatives(
            subject=subject, body=text_content, from_email=settings.SERVER_EMAIL, to=to
        )
        message.mixed_subtype = "related"
        image_files = ["logo.png", "document.png"]
        for image_file in image_files:
            with staticfiles_storage.open(f"dist/images/emails/{image_file}") as f:
                header = MIMEImage(f.read())
                header.add_header("Content-ID", f"<{image_file}>")
                message.attach(header)
        message.attach_alternative(html_content, "text/html")
        message.send(fail_silently=False)
        notification.mark_emailed()


class NotificationTitle:
    def get_notification_title(self, notification_action: MessageTypes) -> str:
        if notification_action in [
            MessageTypes.PURCHASE_NEW,
            MessageTypes.PURCHASE_REPROPOSED,
        ]:
            title = "The following Project has a Product that needs Legal Evaluation"
        elif notification_action == MessageTypes.PURCHASE_DRAFTED:
            title = "The following Project has a Product that was Submitted to Corporate Legal for Review"
        elif notification_action == MessageTypes.PURCHASE_REJECTED:
            title = "The following Project has a Product that was Rejected by Corporate Legal"
        elif notification_action == MessageTypes.PURCHASE_APPROVED:
            title = "The following Project has a Product that was Approved by Corporate Legal"
        elif notification_action == MessageTypes.PURCHASE_SEGMENT_ACCEPTED:
            title = "The following Project has a Product that has been Accepted During Segment Legal Evaluation"
        elif notification_action == MessageTypes.PURCHASE_SEGMENT_REJECTED:
            title = "The following Project has a Product that has been Rejected During Segment Legal Evaluation"
        elif notification_action == MessageTypes.PURCHASE_COMMENT:
            title = "A Product Comment has been added to the following Project"
        elif notification_action == MessageTypes.PURCHASE_NEEDS_REVISION:
            title = "The following Project has a Product that needs Revision per Corporate Legal Review"
        else:
            title = ""
        return title


class NotificationPurchaseEmailSender(NotificationTitle):
    def get_context_data(self, notification: Notification) -> Dict[str, Any]:
        context: Dict[str, Any] = {}
        context["site_url"] = getattr(settings, "SITE_URL", "")
        context["notification"] = notification
        product = notification.message.content_object
        project = product.project if product else None
        notification_action = notification.message.message_type
        context["title"] = self.get_notification_title(notification_action)

        if notification_action not in ["new_purchase", "purchase_reproposed"]:
            if product.display_approval:
                legal_decision = "Yes" if product.is_approved else "No"
            else:
                legal_decision = ""
            validated = "Yes" if product.is_validated else "No"
        else:
            legal_decision = ""
            validated = ""

        context["product"] = EmailProduct(
            name=product.name,
            owner=product.creator.full_name,
            validated=validated,
            decision=legal_decision,
            review_note=product.review_note,
            validation_note=product.validation_note,
            is_comment=notification_action == MessageTypes.PURCHASE_COMMENT,
            is_resubmitted=notification_action == MessageTypes.PURCHASE_REPROPOSED,
        )
        context["project"] = EmailProject(
            name=project.name if project else "",
            project_id=project.pk,
            phase=project.phase_display,
            overall_health=project.current_health.health_display,
            progress=project.percent_complete.percentage,
        )
        return context

    def send(self, notification: Notification):
        subject = "Legal Impact Review"
        context = self.get_context_data(notification)
        text_content = render_to_string(
            "notifications/emails/purchase_notification.txt", context
        )
        html_content = render_to_string(
            "notifications/emails/purchase_notification.html", context
        )
        to = [f"{notification.recipient.full_name} <{notification.recipient.email}>"]
        message = EmailMultiAlternatives(
            subject=subject, body=text_content, from_email=settings.SERVER_EMAIL, to=to
        )
        message.mixed_subtype = "related"
        image_files = ["logo.png", "legal.png"]
        for image_file in image_files:
            with staticfiles_storage.open(f"dist/images/emails/{image_file}") as f:
                header = MIMEImage(f.read())
                header.add_header("Content-ID", f"<{image_file}>")
                message.attach(header)
        message.attach_alternative(html_content, "text/html")
        message.send(fail_silently=False)
        notification.mark_emailed()


class NotificationEmailPreviewView(
    CompanyAdminRequiredMixin, CompanyQuerysetMixin, NotificationTitle, DetailView
):
    queryset = Notification.objects.all()

    def get_context_data(self) -> Dict[str, Any]:
        notification: Notification = self.object
        context: Dict[str, Any] = {}
        context["site_url"] = getattr(settings, "SITE_URL", "")
        context["notification"] = notification
        product = notification.message.content_object
        project = product.project if product else None
        notification_action = notification.message.message_type

        context["title"] = self.get_notification_title(notification_action)

        if notification_action not in [
            MessageTypes.PURCHASE_NEW,
            MessageTypes.PURCHASE_REPROPOSED,
        ]:
            if product.display_approval:
                legal_decision = "Yes" if product.is_approved else "No"
            else:
                legal_decision = ""
            validated = "Yes" if product.is_validated else "No"
        else:
            legal_decision = ""
            validated = ""

        context["product"] = EmailProduct(
            name=product.name,
            owner=product.creator.full_name,
            validated=validated,
            decision=legal_decision,
            review_note=product.review_note,
            validation_note=product.validation_note,
            is_comment=notification_action == MessageTypes.PURCHASE_COMMENT,
            is_resubmitted=notification_action == MessageTypes.PURCHASE_REPROPOSED,
        )
        context["project"] = EmailProject(
            name=project.name if project else "",
            project_id=project.pk,
            phase=project.phase_display,
            overall_health=project.current_health.health_display,
            progress=project.percent_complete.percentage,
        )
        return context

    def get(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse:
        self.object = self.get_object()
        context = self.get_context_data()
        content = render_to_string(
            "notifications/emails/purchase_notification.html", context
        )
        content = content.replace("cid:", "/files/dist/images/emails/")
        return HttpResponse(content)


class NotificationTollgateEmailSender:
    def get_context_data(self, notification: Notification) -> Dict[str, Any]:
        context: Dict[str, Any] = {}
        context["site_url"] = getattr(settings, "SITE_URL", "")
        context["notification"] = notification
        meeting_item = notification.message.content_object
        project = meeting_item.project
        context["tollgate"] = EmailMeetingItem(
            vote_result=meeting_item.get_result(), phase=meeting_item.phase
        )
        context["project"] = EmailProject(
            name=project.name,
            project_id=project.pk,
            phase=project.phase_display,
            overall_health=project.current_health.health_display,
            progress=project.percent_complete.percentage,
        )
        return context

    def send(self, notification: Notification):
        meeting_item = notification.message.content_object
        project = meeting_item.project
        subject = (
            "{project_name} has been {vote_result} at the {phase} Tollgate".format(
                project_name=project.name,
                phase=project.phase_display,
                vote_result=meeting_item.get_result(),
            )
        )
        context = self.get_context_data(notification)
        text_content = render_to_string(
            "notifications/emails/tollgate_notification.txt",
            {"subject": subject, **context},
        )
        html_content = render_to_string(
            "notifications/emails/tollgate_notification.html", context
        )
        to = [f"{notification.recipient.full_name} <{notification.recipient.email}>"]
        message = EmailMultiAlternatives(
            subject=subject, body=text_content, from_email=settings.SERVER_EMAIL, to=to
        )
        message.mixed_subtype = "related"
        image_files = ["logo.png", "legal.png"]
        if "Accepted" in meeting_item.get_result():
            image_files.append("icon_accepted_tollgate.png")
        else:
            image_files.append("icon_rejected_tollgate.png")
        for image_file in image_files:
            with staticfiles_storage.open(f"dist/images/emails/{image_file}") as f:
                header = MIMEImage(f.read())
                header.add_header("Content-ID", f"<{image_file}>")
                message.attach(header)
        message.attach_alternative(html_content, "text/html")
        message.send(fail_silently=False)
        notification.mark_emailed()


class NotificationTollgateAgendaEmailSender:
    def get_context_data(self):
        context: Dict[str, Any] = {}
        meeting = Meeting.objects.get_current()
        meeting_items = meeting.items.all().with_latest_health().order_by("order")
        context.update(
            {
                "site_url": getattr(settings, "SITE_URL", ""),
                "meeting": meeting,
                "meeting_items": meeting_items,
                "initiation_count": meeting_items.filter(
                    project__phase=Project.PHASE_INITIATION
                ).count(),
                "planning_count": meeting_items.filter(
                    project__phase=Project.PHASE_PLANNING
                ).count(),
                "execution_count": meeting_items.filter(
                    project__phase=Project.PHASE_EXECUTION
                ).count(),
                "close_count": meeting_items.filter(
                    project__phase=Project.PHASE_CONTROL
                ).count(),
            }
        )
        return context

    def send(self, notification: Notification):
        subject = "Tollgate Agenda"
        context = self.get_context_data()
        if not context["meeting"].locked:
            logger.info("Agenda email was not delivered due to not being finalized")
            return
        text_content = render_to_string(
            "notifications/emails/meeting_agenda.txt", context
        )
        html_content = render_to_string(
            "notifications/emails/meeting_agenda.html", context
        )
        to = [f"{notification.recipient.full_name} <{notification.recipient.email}>"]
        message = EmailMultiAlternatives(
            subject=subject, body=text_content, from_email=settings.SERVER_EMAIL, to=to
        )
        message.mixed_subtype = "related"
        image_files = ["logo.png"]
        for image_file in image_files:
            with staticfiles_storage.open(f"dist/images/emails/{image_file}") as f:
                header = MIMEImage(f.read())
                header.add_header("Content-ID", f"<{image_file}>")
                message.attach(header)
        message.attach_alternative(html_content, "text/html")
        message.send(fail_silently=False)
        notification.mark_emailed()


class PlannerEmailSender:
    def __init__(self, whitelist_domains: List[str] = None):
        if whitelist_domains is None:
            whitelist_domains = []
        self.whitelist_domains = whitelist_domains

    def get_context_data(self):
        context: Dict[str, Any] = {}
        context["locations_rank"] = "09/01/2020"
        context["operations_need"] = "10/01/2020"
        context["risk_factor"] = "11/01/2020"
        context["vp_factor"] = "12/01/2020"
        context["executive_adjustment"] = "01/01/2020"
        context["site_url"] = getattr(settings, "SITE_URL", "")

        return context

    def send(self, notification: Notification):
        subject = "Planner Notification"
        context = self.get_context_data()

        text_content = render_to_string(
            "notifications/emails/planner_notification.txt", context
        )
        html_content = render_to_string(
            "notifications/emails/planner_notification.html", context
        )
        to = [f"{notification.recipient.full_name} <{notification.recipient.email}>"]
        message = EmailMultiAlternatives(
            subject=subject, body=text_content, from_email=settings.SERVER_EMAIL, to=to
        )
        message.mixed_subtype = "related"
        image_files = ["logo.png"]
        for image_file in image_files:
            with staticfiles_storage.open(f"dist/images/emails/{image_file}") as f:
                header = MIMEImage(f.read())
                header.add_header("Content-ID", f"<{image_file}>")
                message.attach(header)
        message.attach_alternative(html_content, "text/html")
        message.send(fail_silently=False)
        notification.mark_emailed()
