from typing import FrozenSet

from apps.notifications.models import MessageTypes, ReceiverRule, Role, Scope

receiver_rules: FrozenSet[ReceiverRule] = frozenset(
    [
        ReceiverRule(
            message_type=MessageTypes.PROJECT_NEW,
            role=Role.EXECUTIVE_OWNER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_NEW,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_NEW,
            role=Role.FINANCE_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_NEW,
            role=Role.BUSINESS_ANALYST,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_NEW,
            role=Role.ADDITIONAL_STAKEHOLDER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_NEW,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_NEW, role=Role.FINANCE, scope=Scope.GLOBAL
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_NEW,
            role=Role.PROGRAM_MANAGER,
            scope=Scope.PROGRAM,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_NEW,
            role=Role.EXECUTIVE_SPONSOR,
            scope=Scope.PROGRAM,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_NEW,
            role=Role.SHARE_RECIPIENT,
            scope=Scope.PROGRAM,
        ),
        ReceiverRule(
            message_type=MessageTypes.COMMENT_ADDED,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.COMMENT_ADDED,
            role=Role.BUSINESS_ANALYST,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.COMMENT_ADDED,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.COMMENT_ADDED,
            role=Role.PROGRAM_MANAGER,
            scope=Scope.PROGRAM,
        ),
        ReceiverRule(
            message_type=MessageTypes.COMMENT_ADDED,
            role=Role.EXECUTIVE_SPONSOR,
            scope=Scope.PROGRAM,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_TECH_COMPONENTS,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_TECH_COMPONENTS,
            role=Role.SOLUTIONS_ARCHITECTURE,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_TECH_COMPONENTS,
            role=Role.COMPLIANCE,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_STATE_CHANGE,
            role=Role.EXECUTIVE_OWNER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_STATE_CHANGE,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_STATE_CHANGE,
            role=Role.BUSINESS_ANALYST,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_STATE_CHANGE,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_PHASE_CHANGE,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_PHASE_CHANGE,
            role=Role.BUSINESS_ANALYST,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_PHASE_CHANGE,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_PHASE_CHANGE,
            role=Role.FINANCE,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_PHASE_CHANGE,
            role=Role.SOLUTIONS_ARCHITECTURE,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_PHASE_CHANGE,
            role=Role.COMPLIANCE,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.HEALTH_CHANGE,
            role=Role.EXECUTIVE_OWNER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.HEALTH_CHANGE,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.HEALTH_CHANGE,
            role=Role.FINANCE_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.HEALTH_CHANGE,
            role=Role.BUSINESS_ANALYST,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.HEALTH_CHANGE,
            role=Role.ADDITIONAL_STAKEHOLDER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.HEALTH_CHANGE,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.STATUS_UPDATE,
            role=Role.EXECUTIVE_OWNER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.STATUS_UPDATE,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.STATUS_UPDATE,
            role=Role.BUSINESS_ANALYST,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.STATUS_UPDATE,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.NEW_SPEND_DATA,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.NEW_SPEND_DATA,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.NEW_SPEND_DATA,
            role=Role.FINANCE,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.NEW_BENEFIT_TRACKING_DATA,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.NEW_BENEFIT_TRACKING_DATA,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_DELETED,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_DELETED,
            role=Role.BUSINESS_ANALYST,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.PROJECT_DELETED,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.CORPORATE_COMMUNICATION_NEEDED,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.CORPORATE_COMMUNICATION_NEEDED,
            role=Role.CORPORATE_COMMUNICATIONS,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.DOCUMENT_REVIEW_NEEDED,
            role=Role.ADMIN,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_NEW,
            role=Role.SEGMENT_LEGAL_REVIEWER,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_NEW,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_SEGMENT_ACCEPTED,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_SEGMENT_REJECTED,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_REPROPOSED,
            role=Role.SEGMENT_LEGAL_REVIEWER,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_REPROPOSED,
            role=Role.CORPORATE_LEGAL_REVIEWER,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_REPROPOSED,
            role=Role.EXECUTIVE_OWNER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_REPROPOSED,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_REPROPOSED,
            role=Role.FINANCE_LEAD,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_REPROPOSED,
            role=Role.BUSINESS_ANALYST,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_REPROPOSED,
            role=Role.ADDITIONAL_STAKEHOLDER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_REPROPOSED,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_DRAFTED,
            role=Role.CORPORATE_LEGAL_REVIEWER,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_DRAFTED,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_APPROVED,
            role=Role.EXECUTIVE_OWNER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_APPROVED,
            role=Role.CORPORATE_LEGAL_REVIEWER,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_APPROVED,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_APPROVED,
            role=Role.FINANCE_LEAD,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_APPROVED,
            role=Role.BUSINESS_ANALYST,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_APPROVED,
            role=Role.ADDITIONAL_STAKEHOLDER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_APPROVED,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_REJECTED,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_NEEDS_REVISION,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_COMMENT,
            role=Role.SEGMENT_LEGAL_REVIEWER,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_COMMENT,
            role=Role.CORPORATE_LEGAL_REVIEWER,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.PURCHASE_COMMENT,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PURCHASE,
        ),
        ReceiverRule(
            message_type=MessageTypes.TOLLGATE_HELD,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.TOLLGATE_HELD,
            role=Role.BUSINESS_ANALYST,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.TOLLGATE_HELD,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_NEW,
            role=Role.EXECUTIVE_OWNER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.TOLLGATE_AGENDA_ADD,
            role=Role.PROJECT_MANAGER,
            scope=Scope.AGENDA,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_NEW,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_NEW,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_NEW,
            role=Role.FINANCE_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_NEW,
            role=Role.BUSINESS_ANALYST,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_NEW,
            role=Role.ADDITIONAL_STAKEHOLDER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.TOLLGATE_AGENDA_REMOVE,
            role=Role.PROJECT_MANAGER,
            scope=Scope.AGENDA,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_NEW, role=Role.COMPLIANCE, scope=Scope.GLOBAL
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_NEW, role=Role.FINANCE, scope=Scope.GLOBAL
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_NEW,
            role=Role.SOLUTIONS_ARCHITECTURE,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_CONVERTED,
            role=Role.EXECUTIVE_OWNER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.TOLLGATE_AGENDA_FINALIZED,
            role=Role.PROJECT_MANAGER,
            scope=Scope.AGENDA,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_CONVERTED,
            role=Role.PROJECT_MANAGER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_CONVERTED,
            role=Role.BUSINESS_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_CONVERTED,
            role=Role.FINANCE_LEAD,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_CONVERTED,
            role=Role.BUSINESS_ANALYST,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_CONVERTED,
            role=Role.ADDITIONAL_STAKEHOLDER,
            scope=Scope.PROJECT,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_CONVERTED,
            role=Role.COMPLIANCE,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_CONVERTED,
            role=Role.FINANCE,
            scope=Scope.GLOBAL,
        ),
        ReceiverRule(
            message_type=MessageTypes.IDEA_CONVERTED,
            role=Role.SOLUTIONS_ARCHITECTURE,
            scope=Scope.GLOBAL,
        ),
    ]
)
