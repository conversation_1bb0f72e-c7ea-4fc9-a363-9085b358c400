[{"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_NEW", "role": "executive_owner", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_NEW", "role": "business_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_NEW", "role": "finance_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_NEW", "role": "business_analyst", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_NEW", "role": "additional_stakeholder", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_NEW", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_NEW", "role": "finance", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_NEW", "role": "program_manager", "scope": "program"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_NEW", "role": "executive_sponsor", "scope": "program"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_NEW", "role": "share_recipient", "scope": "program"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.COMMENT_ADDED", "role": "business_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.COMMENT_ADDED", "role": "business_analyst", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.COMMENT_ADDED", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.COMMENT_ADDED", "role": "program_manager", "scope": "program"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.COMMENT_ADDED", "role": "executive_sponsor", "scope": "program"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_TECH_COMPONENTS", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_TECH_COMPONENTS", "role": "solutions_architecture", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_TECH_COMPONENTS", "role": "compliance", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_STATE_CHANGE", "role": "executive_owner", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_STATE_CHANGE", "role": "business_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_STATE_CHANGE", "role": "business_analyst", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_STATE_CHANGE", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_PHASE_CHANGE", "role": "business_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_PHASE_CHANGE", "role": "business_analyst", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_PHASE_CHANGE", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_PHASE_CHANGE", "role": "finance", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_PHASE_CHANGE", "role": "solutions_architecture", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_PHASE_CHANGE", "role": "compliance", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.HEALTH_CHANGE", "role": "executive_owner", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.HEALTH_CHANGE", "role": "business_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.HEALTH_CHANGE", "role": "finance_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.HEALTH_CHANGE", "role": "business_analyst", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.HEALTH_CHANGE", "role": "additional_stakeholder", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.HEALTH_CHANGE", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.STATUS_UPDATE", "role": "executive_owner", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.STATUS_UPDATE", "role": "business_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.STATUS_UPDATE", "role": "business_analyst", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.STATUS_UPDATE", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.NEW_SPEND_DATA", "role": "business_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.NEW_SPEND_DATA", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.NEW_SPEND_DATA", "role": "finance", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.NEW_BENEFIT_TRACKING_DATA", "role": "business_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.NEW_BENEFIT_TRACKING_DATA", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_DELETED", "role": "business_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_DELETED", "role": "business_analyst", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PROJECT_DELETED", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.CORPORATE_COMMUNICATION_NEEDED", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.CORPORATE_COMMUNICATION_NEEDED", "role": "corporate_communications", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.DOCUMENT_REVIEW_NEEDED", "role": "admin", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_NEW", "role": "segment_legal_reviewer", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_NEW", "role": "project_manager", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_SEGMENT_ACCEPTED", "role": "project_manager", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_SEGMENT_REJECTED", "role": "project_manager", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_REPROPOSED", "role": "segment_legal_reviewer", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_REPROPOSED", "role": "corporate_legal_reviewer", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_REPROPOSED", "role": "executive_owner", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_REPROPOSED", "role": "business_lead", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_REPROPOSED", "role": "finance_lead", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_REPROPOSED", "role": "business_analyst", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_REPROPOSED", "role": "additional_stakeholder", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_REPROPOSED", "role": "project_manager", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_DRAFTED", "role": "corporate_legal_reviewer", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_DRAFTED", "role": "project_manager", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_APPROVED", "role": "executive_owner", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_APPROVED", "role": "corporate_legal_reviewer", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_APPROVED", "role": "business_lead", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_APPROVED", "role": "finance_lead", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_APPROVED", "role": "business_analyst", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_APPROVED", "role": "additional_stakeholder", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_APPROVED", "role": "project_manager", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_REJECTED", "role": "project_manager", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_NEEDS_REVISION", "role": "project_manager", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_COMMENT", "role": "segment_legal_reviewer", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_COMMENT", "role": "corporate_legal_reviewer", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.PURCHASE_COMMENT", "role": "project_manager", "scope": "purchase"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.TOLLGATE_HELD", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.TOLLGATE_HELD", "role": "business_analyst", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.TOLLGATE_HELD", "role": "business_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_NEW", "role": "executive_owner", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.TOLLGATE_AGENDA_ADD", "role": "project_manager", "scope": "agenda"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_NEW", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_NEW", "role": "business_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_NEW", "role": "finance_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_NEW", "role": "business_analyst", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_NEW", "role": "additional_stakeholder", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.TOLLGATE_AGENDA_REMOVE", "role": "project_manager", "scope": "agenda"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_NEW", "role": "compliance", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_NEW", "role": "finance", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_NEW", "role": "solutions_architecture", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_CONVERTED", "role": "executive_owner", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.TOLLGATE_AGENDA_FINALIZED", "role": "project_manager", "scope": "agenda"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_CONVERTED", "role": "project_manager", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_CONVERTED", "role": "business_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_CONVERTED", "role": "finance_lead", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_CONVERTED", "role": "business_analyst", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_CONVERTED", "role": "additional_stakeholder", "scope": "project"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_CONVERTED", "role": "compliance", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_CONVERTED", "role": "finance", "scope": "global"}}, {"model": "notifications.receiver<PERSON>le", "pk": null, "fields": {"message_type": "MessageTypes.IDEA_CONVERTED", "role": "solutions_architecture", "scope": "global"}}]