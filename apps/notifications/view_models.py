from dataclasses import asdict, dataclass, field
from datetime import datetime
from typing import List, Optional, Union

from django.contrib.auth import get_user_model

from apps.projects.models import Project

User = get_user_model()


@dataclass
class EmailViewProject:
    id: int
    name: str
    phase: Optional[str] = None
    overall_health: Optional[str] = None
    progress: int = 0
    type: str = "project"


@dataclass
class EmailViewProgram:
    id: int
    name: str
    type: str = "program"


@dataclass
class EmailViewMeetingItem:
    id: int
    name: str
    description: str
    phase: Optional[str] = None
    overall_health: Optional[str] = None
    progress: int = 0
    type: str = "meetingitem"


@dataclass
class EmailViewNotification:
    message_type: str
    icon_url: str
    creator: str
    created: datetime
    object: Union[None, EmailViewMeetingItem, EmailViewProject, EmailViewProgram] = None


@dataclass
class EmailViewPlanTimeline:
    get_phase_display: str
    pretty_date_range: str


@dataclass
class EmailProduct:
    name: str
    owner: str
    validated: str
    decision: str
    review_note: str
    validation_note: str
    is_comment: bool
    is_resubmitted: bool


@dataclass
class EmailProject:
    name: str
    project_id: str
    phase: str
    overall_health: str
    progress: str


@dataclass
class EmailMeetingItem:
    vote_result: str
    phase: str


@dataclass
class EmailPlanner:
    year: int
    headline: str
    timeline_list: List[EmailViewPlanTimeline] = field(default_factory=list)
    timeline_alert: EmailViewPlanTimeline = None
    timeline_highlight: EmailViewPlanTimeline = None
    button_text: str = None
    button_url: str = None

    def highlight_mode(self) -> bool:
        return self.timeline_alert is not None or self.timeline_highlight is not None

    def has_button(self) -> bool:
        return self.button_text is not None and self.button_url is not None


@dataclass
class IdeaState:
    id: int = ""
    executive_owners: List[User] = field(default_factory=list)
    project_managers: List[User] = field(default_factory=list)
    business_leads: List[User] = field(default_factory=list)
    finance_leads: List[User] = field(default_factory=list)
    business_analysts: List[User] = field(default_factory=list)
    additional_stakeholders: List[User] = field(default_factory=list)

    technology_components: bool = False
    ready_to_convert: bool = False
    converted: bool = False

    @staticmethod
    def from_idea(idea: Project):
        return IdeaState(
            id=idea.id,
            executive_owners=list(idea.executive_owners.all()),
            project_managers=list(idea.project_managers.all()),
            business_leads=list(idea.business_leads.all()),
            finance_leads=list(idea.finance_leads.all()),
            business_analysts=list(idea.business_analysts.all()),
            additional_stakeholders=list(idea.other_stakeholders.all()),
            technology_components=idea.has_technology_components == "yes",
            ready_to_convert=idea.ready_to_convert,
        )

    def get_changed_fields(self, idea_state) -> List[str]:
        dict1 = asdict(self)
        dict2 = asdict(idea_state)
        return [k for k in dict1 if k not in dict2 or dict1[k] != dict2[k]]
