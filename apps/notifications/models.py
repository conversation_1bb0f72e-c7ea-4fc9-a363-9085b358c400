from __future__ import annotations
import auto_prefetch

import logging
from dataclasses import dataclass
from datetime import datetime
from typing import TYPE_CHECKING, Iterable, List, Optional, Union

from croniter import croniter
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import <PERSON>ric<PERSON><PERSON><PERSON><PERSON><PERSON>
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils import timezone
from django.utils.functional import cached_property
from pytz import UTC

from apps.projects.models import Project
from apps.users.models import User
from apps.utils.models import EnumField
from django.utils.translation import gettext_lazy as _

if TYPE_CHECKING:
    from apps.documents.models import Document
    from apps.meetings.models import MeetingItem
    from apps.programs.models import Program
    from apps.purchases.models import Purchase

User = get_user_model()


logger = logging.getLogger("site")


class Priority(models.TextChoices):
    LOW = "low", _("Do not send email")  # Do not send email
    MEDIUM = "medium", _("Include in a roll-up email")  # Include in a roll-up email
    HIGH = "high", _("Send individual email")  # Send individual email


class MessageTypes(models.TextChoices):
    PROJECT_NEW = "New Project or Program", _("New Project or Program")
    ROLE_ASSIGNED = "Role Assigned to Me", _("Role Assigned to Me")
    COMMENT_ADDED = "Comment Added", _("Comment Added")
    PROJECT_TECH_COMPONENTS = (
        "Project Marked with Tech Components",
        _("Project Marked with Tech Components"),
    )
    PROJECT_STATE_CHANGE = "Project State Change", _("Project State Change")
    PROJECT_PHASE_CHANGE = "Project Phase Change", _("Project Phase Change")
    HEALTH_CHANGE = "Overall Health Change", _("Overall Health Change")
    STATUS_UPDATE = "Status Update", _("Status Update")
    NEW_SPEND_DATA = "Project CapEx or OpEx Update", _("Project CapEx or OpEx Update")
    NEW_BENEFIT_TRACKING_DATA = (
        "Project Benefits Tracking Update",
        _("Project Benefits Tracking Update"),
    )
    PROJECT_DELETED = "Deleted Project", _("Deleted Project")
    CORPORATE_COMMUNICATION_NEEDED = (
        "Corporate Communication Needed",
        _("Corporate Communication Needed"),
    )
    PURCHASE_NEW = "New Purchase", _("New Purchase")
    PURCHASE_SEGMENT_ACCEPTED = (
        "Product Segment Legal Accepted",
        _("Product Segment Legal Accepted"),
    )
    PURCHASE_SEGMENT_REJECTED = (
        "Product Segment Legal Rejected",
        _("Product Segment Legal Rejected"),
    )
    PURCHASE_REPROPOSED = (
        "Product Submitted for Reevaluation",
        _("Product Submitted for Reevaluation"),
    )
    PURCHASE_DRAFTED = (
        "Product Submitted to Corporate Legal",
        _("Product Submitted to Corporate Legal"),
    )
    PURCHASE_APPROVED = (
        "Product Corporate Legal Approved",
        _("Product Corporate Legal Approved"),
    )
    PURCHASE_REJECTED = (
        "Product Corporate Legal Rejected",
        _("Product Corporate Legal Rejected"),
    )
    PURCHASE_NEEDS_REVISION = (
        "Product Corporate Legal Revision Requested",
        _("Product Corporate Legal Revision Requested"),
    )
    PURCHASE_COMMENT = "Product Comment", _("Product Comment")
    DOCUMENT_REVIEW_NEEDED = "Document Needs Review", _("Document Needs Review")
    TOLLGATE_HELD = "Tollgate Held", _("Tollgate Held")
    TOLLGATE_AGENDA_ADD = "Added to Agenda", _("Added to Agenda")
    TOLLGATE_AGENDA_REMOVE = "Removed from Agenda", _("Removed from Agenda")
    TOLLGATE_AGENDA_FINALIZED = (
        "Tollgate Agenda Finalized",
        _("Tollgate Agenda Finalized"),
    )
    IDEA_NEW = "New Idea", _("New Idea")
    IDEA_READY_TO_CONVERT = "Ready to Convert", _("Ready to Convert")
    IDEA_CONVERTED = "Converted Idea", _("Converted Idea")
    IDEA_DELETED = "Deleted Idea", _("Deleted Idea")
    PLANNER_WORKFLOW_DATES_SET = "Workflow Dates Set", _("Workflow Dates Set")
    PLANNER_WORKFLOW_STEP_START = "Workflow Step Start", _("Workflow Step Start")
    PLANNER_THREE_DAY_WARNING = "3 Day Warning", _("3 Day Warning")
    PLANNER_ONE_DAY_WARNING = "24 Hour Warning", _("24 Hour Warning")
    PLANNER_WORKFLOW_ENDED = "Workflow Ended Alert", _("Workflow Ended Alert")
    PLANNER_ALLOCATION_COMPLETED = "Allocation Completed", _("Allocation Completed")


class Scope(models.TextChoices):
    AGENDA = "AGENDA", "Agenda"
    DOCUMENT = "DOCUMENT", "Document"
    GLOBAL = "GLOBAL", "Global"
    PROGRAM = "PROGRAM", "Program"
    PROJECT = "PROJECT", "Project"
    PURCHASE = "PURCHASE", "Purchase"


class Role(models.TextChoices):
    ADDITIONAL_STAKEHOLDER = "ADDITIONAL_STAKEHOLDER", "Additional Stakeholder"
    ADMIN = "ADMIN", "Admin"
    BUSINESS_ANALYST = "BUSINESS_ANALYST", "Business Analyst"
    BUSINESS_LEAD = "BUSINESS_LEAD", "Business Lead"
    COMPLIANCE = "COMPLIANCE", "Compliance"
    CORPORATE_COMMUNICATIONS = "CORPORATE_COMMUNICATIONS", "Corporate Communications"
    CORPORATE_LEGAL_REVIEWER = "CORPORATE_LEGAL_REVIEWER", "Corporate Legal Reviewer"
    EXECUTIVE_OWNER = "EXECUTIVE_OWNER", "Executive Owner"
    EXECUTIVE_SPONSOR = "EXECUTIVE_SPONSOR", "Executive Sponsor"
    FINANCE = "FINANCE", "Finance"
    FINANCE_LEAD = "FINANCE_LEAD", "Finance Lead"
    PROGRAM_MANAGER = "PROGRAM_MANAGER", "Program Manager"
    PROJECT_MANAGER = "PROJECT_MANAGER", "Project Manager"
    SEGMENT_LEGAL_REVIEWER = "SEGMENT_LEGAL_REVIEWER", "Segment Legal Reviewer"
    SHARE_RECIPIENT = "SHARE_RECIPIENT", "Share Recipient"
    SOLUTIONS_ARCHITECTURE = "SOLUTIONS_ARCHITECTURE", "Solutions Architecture"


@dataclass(frozen=True)
class ReceiverRule:
    message_type: MessageTypes
    role: Role
    scope: Scope


def get_default_send_cutoff_date() -> datetime:
    date_iter = croniter(
        Subscription.FREQUENCY_CRON[Subscription.FREQUENCY_WEEKLY],
        start_time=timezone.now(),
        ret_type=datetime,
    )
    return date_iter.get_prev()


def notify(
    message_type: MessageTypes,
    message: Union[str, Message],
    creator,
    priority: Optional[Priority] = Priority.MEDIUM,
    content_object: Union[
        None, MeetingItem, Project, Program, Document, Purchase
    ] = None,
    recipients: Iterable[Person] = None,
) -> List[Notification]:
    """
    notify will create notifications for all recipients supplied as well as any recipients returned from running the
    receiver rules for the message type.
    """
    from apps.meetings.models import MeetingItem

    if recipients is None:
        recipients = []
    if isinstance(message, str):
        message = Message.objects.create(
            content_object=content_object,
            message_type=message_type,
            text=message,
            creator=creator,
        )

    recipients = set(recipients)
    notifications = []
    for recipient in recipients:
        notification = Notification.objects.create(
            recipient=recipient,
            message=message,
            priority=priority,
            company=creator.company,
        )
        notifications.append(notification)
    return notifications


class Message(auto_prefetch.Model):
    content_type = auto_prefetch.ForeignKey(
        ContentType, null=True, on_delete=models.CASCADE
    )
    object_id = models.PositiveIntegerField(null=True)
    content_object = GenericForeignKey("content_type", "object_id")
    project = auto_prefetch.ForeignKey(
        Project, blank=True, null=True, on_delete=models.SET_NULL
    )  # deprecated: use content_object
    message_type = models.CharField(
        max_length=255, choices=MessageTypes.choices, null=True, blank=True
    )
    text = models.TextField(max_length=1000)
    creator = auto_prefetch.ForeignKey(
        "users.User", null=True, editable=False, on_delete=models.SET_NULL
    )
    created = models.DateTimeField(auto_now_add=True, editable=False)

    def __str__(self):
        return self.text

    @cached_property
    def notification_icon_slug(self):
        message_key = self.message_type
        if message_key == MessageTypes.PROJECT_NEW:
            return "icon-notification-new"
        elif message_key == MessageTypes.IDEA_NEW:
            return "icon-notification-idea-new"
        elif message_key == MessageTypes.ROLE_ASSIGNED:
            return "icon-notification-person"
        elif message_key == MessageTypes.COMMENT_ADDED:
            return "icon-notification-comment"
        elif message_key == MessageTypes.PROJECT_TECH_COMPONENTS:
            return "icon-notification-gear"
        elif message_key == MessageTypes.PROJECT_STATE_CHANGE:
            return "icon-notification-pencil"
        elif message_key == MessageTypes.PROJECT_PHASE_CHANGE:
            return "icon-notification-pencil"
        elif message_key == MessageTypes.HEALTH_CHANGE:
            return "icon-notification-heart"
        elif message_key == MessageTypes.STATUS_UPDATE:
            return "icon-notification-pencil"
        elif message_key == MessageTypes.NEW_SPEND_DATA:
            return "icon-notification-money"
        elif message_key == MessageTypes.NEW_BENEFIT_TRACKING_DATA:
            return "icon-notification-money"
        elif message_key == MessageTypes.PROJECT_DELETED:
            return "icon-notification-trash"
        elif message_key == MessageTypes.CORPORATE_COMMUNICATION_NEEDED:
            return "icon-notification-comment"
        elif message_key == MessageTypes.PURCHASE_NEW:
            return "icon-notification-gear"
        elif message_key == MessageTypes.PURCHASE_REPROPOSED:
            return "icon-notification-gear"
        elif message_key == MessageTypes.PURCHASE_DRAFTED:
            return "icon-notification-gear"
        elif message_key == MessageTypes.PURCHASE_REJECTED:
            return "icon-notification-gear"
        elif message_key == MessageTypes.PURCHASE_APPROVED:
            return "icon-notification-gear"
        elif message_key == MessageTypes.PURCHASE_NEEDS_REVISION:
            return "icon-notification-gear"
        elif message_key == MessageTypes.PURCHASE_SEGMENT_ACCEPTED:
            return "icon-notification-gear"
        elif message_key == MessageTypes.PURCHASE_SEGMENT_REJECTED:
            return "icon-notification-gear"
        elif message_key == MessageTypes.PURCHASE_COMMENT:
            return "icon-notification-comment"
        elif message_key == MessageTypes.DOCUMENT_REVIEW_NEEDED:
            return "icon-notification-document"
        elif message_key == MessageTypes.TOLLGATE_AGENDA_ADD:
            return "icon-notification-agenda-add"
        elif message_key == MessageTypes.TOLLGATE_AGENDA_REMOVE:
            return "icon-notification-agenda-remove"
        elif message_key == MessageTypes.TOLLGATE_AGENDA_FINALIZED:
            return "icon-notification-agenda"
        elif message_key == MessageTypes.TOLLGATE_HELD:
            if "Accept" in self.text:
                return "icon-accepted-tollgate"
            else:
                return "icon-rejected-tollgate"
        return ""

    @property
    def notification_icon_path(self):
        return "dist/images/icons/{}.svg".format(self.notification_icon_slug)

    @property
    def notification_email_icon(self):
        return "{}.png".format(self.notification_icon_slug)


class NotificationManager(auto_prefetch.Manager):
    def get_ready_to_email_for_rollup(
        self, recipient: Union[str, Person], frequency: str
    ) -> Iterable[Notification]:
        if isinstance(recipient, str):
            recipient = User.objects.get(email=recipient)
        notifications = (
            self.select_related("message")
            .filter(recipient=recipient)
            .filter(is_emailed=False)
            .filter(priority=Priority.MEDIUM)
            .order_by("message__message_type", "created")
        )
        return [
            notification
            for notification in notifications
            if notification.can_email(frequency)
            and notification.message.content_object is not None
        ]

    def has_ready_to_email_for_rollup(self, recipient: Union[str, User]) -> bool:
        if isinstance(recipient, str):
            recipient = User.objects.get(email=recipient)
        notifications = (
            self.select_related("message")
            .filter(recipient=recipient)
            .filter(is_emailed=False)
            .filter(priority=Priority.MEDIUM)
            .filter(message__object_id__isnull=False)
        )
        return any(notification.can_email() for notification in notifications)

    def get_ready_to_email_for_individual(
        self, recipient: Union[str, Person]
    ) -> Iterable[Notification]:
        if isinstance(recipient, str):
            recipient = User.objects.get(email=recipient)
        notifications = (
            self.select_related("message")
            .filter(recipient=recipient)
            .filter(is_emailed=False)
            .filter(priority=Priority.HIGH)
            .filter(message__object_id__isnull=False)
            .order_by("message__message_type", "created")
        )
        return [
            notification
            for notification in notifications
            if notification.can_email(frequency=Subscription.FREQUENCY_INSTANT)
            and notification.message.content_object is not None
        ]

    def has_ready_to_email_for_individual(self, recipient: Union[str, User]) -> bool:
        if isinstance(recipient, str):
            recipient = User.objects.get(email=recipient)
        notifications = (
            self.select_related("message")
            .filter(recipient=recipient)
            .filter(is_emailed=False)
            .filter(priority=Priority.HIGH)
            .filter(message__object_id__isnull=False)
        )
        return any(
            notification.can_email(frequency=Subscription.FREQUENCY_INSTANT)
            for notification in notifications
        )


class Notification(auto_prefetch.Model):
    company = auto_prefetch.ForeignKey(
        "organizations.Company", on_delete=models.PROTECT
    )
    recipient = auto_prefetch.ForeignKey("users.User", on_delete=models.CASCADE)
    message = auto_prefetch.ForeignKey(Message, on_delete=models.CASCADE)
    priority = EnumField(max_length=50, enum=Priority, default=Priority.MEDIUM)
    is_viewed = models.BooleanField(default=False)
    is_emailed = models.BooleanField(default=False)
    created = models.DateTimeField(auto_now_add=True, editable=False)
    modified = models.DateTimeField(auto_now=True, editable=False)

    objects = NotificationManager()

    def __str__(self):
        return f"Notify {self.recipient} that {self.message}"

    def can_email(self, frequency: Optional[str] = None):
        """
        Checks if the notification instance meets the preconditions to be sent in an email.
        Optionally the frequency of the email to be sent can be specified (instant, daily, or weekly).
        """
        if self.is_emailed:
            return False
        subscription: Optional[Subscription] = Subscription.objects.filter(
            subscriber=self.recipient, message_type=self.message.message_type
        ).first()
        if subscription is None:
            if frequency == Subscription.FREQUENCY_INSTANT:
                return True
            send_cutoff_date = get_default_send_cutoff_date()
            return self.created <= send_cutoff_date
        if frequency is not None and subscription.frequency != frequency:
            return False
        return self.created <= subscription.send_cutoff_date

    def mark_viewed(self):
        self.is_viewed = True
        self.save()

    def mark_emailed(self):
        self.is_emailed = True
        self.save()


class Subscription(auto_prefetch.Model):
    FREQUENCY_INSTANT = "instant"
    FREQUENCY_DAILY = "daily"
    FREQUENCY_WEEKLY = "weekly"
    FREQUENCY_NEVER = "never"
    FREQUENCY_CHOICES = (
        (FREQUENCY_INSTANT, "Instant"),
        (FREQUENCY_DAILY, "Daily"),
        (FREQUENCY_WEEKLY, "Weekly"),
        (FREQUENCY_NEVER, "Never"),
    )
    FREQUENCY_CRON = {
        FREQUENCY_INSTANT: "* * * * *",
        FREQUENCY_DAILY: "0 8 * * *",
        FREQUENCY_WEEKLY: "0 8 * * 5",
    }

    subscriber = auto_prefetch.ForeignKey("users.User", on_delete=models.CASCADE)
    message_type = EnumField(max_length=50, enum=MessageTypes)
    frequency = models.CharField(
        max_length=50, choices=FREQUENCY_CHOICES, default=FREQUENCY_WEEKLY
    )

    class Meta(auto_prefetch.Model.Meta):
        unique_together = ["subscriber", "message_type"]

    def __str__(self):
        return f"{self.subscriber}'s subscription to {self.message_type} with {self.frequency} delivery"

    @property
    def send_cutoff_date(self) -> datetime:
        if self.frequency == self.FREQUENCY_NEVER:
            return datetime.min.replace(tzinfo=UTC)
        date_iter = croniter(
            self.FREQUENCY_CRON[self.frequency],
            start_time=timezone.now(),
            ret_type=datetime,
        )
        return date_iter.get_prev()
