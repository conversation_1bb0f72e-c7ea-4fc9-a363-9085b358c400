from typing import List

import auto_prefetch

from django.db import models
from django.db.models import QuerySet
from django.db.models.query_utils import Q
from django.utils.translation import gettext_lazy as _

from django_lifecycle import LifecycleModel, hook, AFTER_SAVE


class LocationQueryset(QuerySet):
    """
    Custom queryset for the `Location` model that provides additional
    filtering logic, particularly user-specific filtering based on
    assigned roles and permissions.
    """

    def filter_for_user(self, user) -> QuerySet:
        """
        Filters the queryset based on the user's role and permissions.

        Superusers and company admins will receive all results, while non-superusers
        will only receive locations related to their assigned roles.

        Args:
            user (User): The Django user requesting data.

        Returns:
            QuerySet: A filtered queryset containing only the locations
            accessible to the provided user.
        """
        if user.is_superuser:
            return self

        # Company admins have access to all locations
        if getattr(user, "is_company_admin", False):
            return self

        # Filter by location-specific roles
        return self.filter(
            Q(location_engineer=user)
            | Q(location_manager=user)
            | Q(operations_director=user)
            | Q(engineering_director=user)
            | Q(vp_operations=user)
            | Q(svp_operations=user)
            | Q(coo=user)
        ).distinct()


class SizeChoices(models.TextChoices):
    """
    Enum-like representation of possible location size categories.

    Attributes:
        SMALL: Represents a small location.
        MEDIUM: Represents a medium-sized location.
        LARGE: Represents a large location.
        XLARGE: Represents an extra-large location.
    """

    SMALL = "small", _("Small")
    MEDIUM = "medium", _("Medium")
    LARGE = "large", _("Large")
    XLARGE = "xlarge", _("XLarge")


class Location(auto_prefetch.Model, LifecycleModel):
    """
    Represents a physical or organizational location within a company.
    Each location can have multiple assigned personnel roles, such as
    engineers, managers, and directors.

    Lifecycle hooks ensure synchronization of related project personnel
    whenever a location is updated.

    Attributes:
        company (Company): The organization this location belongs to.
        name (str): The human-readable name of the location.
        code (str): A short alphanumeric identifier for the location.
        segment (ManyToManyField): Divisions this location is associated with.
        business_segment (BusinessSegment): Related business segment.
        location_size (str): Indicates the size of the location.
        location_engineer (ManyToManyField): Assigned engineers for the location.
        location_manager (User): Manager assigned to the location.
        operations_director (User): Operations director assigned.
        engineering_director (User): Engineering director assigned.
        vp_operations (ManyToManyField): Assigned Vice Presidents of Operations.
        svp_operations (ManyToManyField): Assigned Senior Vice Presidents of Operations.
        coo (ManyToManyField): Assigned Chief Operating Officers.
        active (bool): Indicates whether the location is currently active.
    """

    company = auto_prefetch.ForeignKey(
        "organizations.Company", on_delete=models.PROTECT
    )
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=3, blank=True, null=True)

    segment = models.ManyToManyField("projects.Division")
    business_segment = auto_prefetch.ForeignKey(
        "projects.BusinessSegment", blank=True, null=True, on_delete=models.PROTECT
    )
    location_size = models.CharField(
        choices=SizeChoices.choices, default=SizeChoices.SMALL, max_length=120
    )
    location_engineer = models.ManyToManyField(
        "users.User", blank=True, related_name="location_engineer"
    )
    location_manager = auto_prefetch.ForeignKey(
        "users.User",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name="location_manager",
    )
    operations_director = auto_prefetch.ForeignKey(
        "users.User",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name="operations_director",
    )
    engineering_director = auto_prefetch.ForeignKey(
        "users.User",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name="location_engineering_director",
    )
    vp_operations = models.ManyToManyField(
        "users.User", blank=True, related_name="vp_operations"
    )
    svp_operations = models.ManyToManyField(
        "users.User", blank=True, related_name="svp_operations"
    )
    coo = models.ManyToManyField("users.User", blank=True, related_name="coo")
    active = models.BooleanField(null=False, blank=False, default=True)

    objects = LocationQueryset.as_manager()

    def activate(self) -> None:
        """
        Activates the current location.

        Updates the `active` flag to True and persists the change.
        """
        self.active = True
        self.save(update_fields=["active"])

    def deactivate(self) -> None:
        """
        Deactivates the current location.

        Updates the `active` flag to False and persists the change.
        """
        self.active = False
        self.save(update_fields=["active"])

    @property
    def all_roles_assigned(self) -> bool:
        """
        Checks if all primary personnel roles are assigned for the location.

        Returns:
            bool: True if all key roles are filled, False otherwise.
        """
        return all(
            [
                self.location_engineer
                and self.location_manager
                and self.operations_director
                and self.engineering_director
                and self.vp_operations
                and self.svp_operations
                and self.coo
            ]
        )

    def __str__(self) -> str:
        """
        Returns a human-readable representation of the location.

        Returns:
            str: The name of the location.
        """
        return self.name

    @hook(AFTER_SAVE)
    def set_project_people(self) -> None:
        """
        Lifecycle hook that synchronizes personnel assignments between
        the `Location` and all associated active `Project` instances.

        This method runs automatically after saving the location and
        updates project personnel to reflect the current location's
        assigned roles.

        The following relationships are updated:
            - Location engineers → Project engineers
            - Location manager → Project managers
            - Operations director → Project operations directors
            - Engineering director → Project engineer directors
            - VP, SVP, COO roles

        Note:
            This method only affects active projects that are in one
            of the early-to-mid project phases (Idea → Execution).
        """
        from apps.projects.models import Project  # pylint: disable=import-outside-toplevel

        projects = (
            Project.objects.filter(active=True)
            .filter(location=self)
            .filter(
                Q(phase__isnull=True)
                | Q(
                    phase__in=[
                        Project.PHASE_IDEA,
                        Project.PHASE_PROPOSE,
                        Project.PHASE_INITIATION,
                        Project.PHASE_PLANNING,
                        Project.PHASE_EXECUTION,
                    ]
                )
            )
        )

        location_engineers = list(self.location_engineer.all())
        vp_operations = list(self.vp_operations.all())
        svp_operations = list(self.svp_operations.all())
        coos = list(self.coo.all())

        for project in projects:
            project: Project
            project.location_engineers.set(location_engineers)
            if self.location_manager:
                project.location_managers.set([self.location_manager])
            if self.operations_director:
                project.operations_directors.set([self.operations_director])
            if self.engineering_director:
                project.engineer_directors.set([self.engineering_director])
            project.vps.set(vp_operations)
            project.svps.set(svp_operations)
            project.coos.set(coos)

    def get_location_personnel(self, include_coo_svp=True) -> List["User"]:
        """
        Retrieves all personnel associated with the location, including
        engineers, managers, directors, and executives.

        Args:
            include_coo_svp (bool): Whether to include COO and SVP roles.

        Returns:
            List[User]: A list of all personnel associated with the location.
        """
        people = []
        people += list(self.location_engineer.all())
        people += list(self.vp_operations.all())
        if self.location_manager:
            people.append(self.location_manager)
        if self.operations_director:
            people.append(self.operations_director)
        if self.engineering_director:
            people.append(self.engineering_director)
        if include_coo_svp:
            people += list(self.svp_operations.all())
            people += list(self.coo.all())
        return people
