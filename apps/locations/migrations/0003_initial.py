# Generated by Django 5.2.4 on 2025-10-28 00:03

import auto_prefetch
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("locations", "0002_initial"),
        ("projects", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="location",
            name="coo",
            field=models.ManyToManyField(
                blank=True, related_name="coo", to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="location",
            name="engineering_director",
            field=auto_prefetch.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="location_engineering_director",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="location",
            name="location_engineer",
            field=models.ManyToMany<PERSON>ield(
                blank=True,
                related_name="location_engineer",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="location",
            name="location_manager",
            field=auto_prefetch.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="location_manager",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="location",
            name="operations_director",
            field=auto_prefetch.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="operations_director",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="location",
            name="segment",
            field=models.ManyToManyField(to="projects.division"),
        ),
        migrations.AddField(
            model_name="location",
            name="svp_operations",
            field=models.ManyToManyField(
                blank=True, related_name="svp_operations", to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="location",
            name="vp_operations",
            field=models.ManyToManyField(
                blank=True, related_name="vp_operations", to=settings.AUTH_USER_MODEL
            ),
        ),
    ]
