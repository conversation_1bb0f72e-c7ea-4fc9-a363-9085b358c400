[{"model": "users.role", "pk": 1, "fields": {"company": 1, "name": "Project Manager", "description": "Manages projects and coordinates team activities"}}, {"model": "users.role", "pk": 2, "fields": {"company": 1, "name": "Location Manager", "description": "Oversees location-specific operations"}}, {"model": "users.role", "pk": 3, "fields": {"company": 1, "name": "Engineering Director", "description": "Leads engineering initiatives"}}, {"model": "users.role", "pk": 4, "fields": {"company": 1, "name": "Operations Engineer", "description": "Handles operational engineering tasks"}}, {"model": "users.role", "pk": 5, "fields": {"company": 2, "name": "Technical Lead", "description": "Leads technical development and architecture"}}, {"model": "users.role", "pk": 6, "fields": {"company": 2, "name": "Senior Developer", "description": "Experienced developer with mentoring responsibilities"}}, {"model": "users.role", "pk": 7, "fields": {"company": 2, "name": "Product Manager", "description": "Manages product roadmap and priorities"}}, {"model": "users.role", "pk": 8, "fields": {"company": 3, "name": "System Administrator", "description": "Maintains system infrastructure and security"}}, {"model": "users.role", "pk": 9, "fields": {"company": 3, "name": "Data Analyst", "description": "Analyzes data and generates insights"}}, {"model": "users.role", "pk": 10, "fields": {"company": 3, "name": "Security Specialist", "description": "Ensures system security and compliance"}}, {"model": "users.roleassignment", "pk": 1, "fields": {"role": 1, "user": 1, "assigned_date": "2025-01-01T10:00:00Z"}}, {"model": "users.roleassignment", "pk": 2, "fields": {"role": 2, "user": 2, "assigned_date": "2025-01-02T09:00:00Z"}}, {"model": "users.roleassignment", "pk": 3, "fields": {"role": 3, "user": 3, "assigned_date": "2025-01-03T08:00:00Z"}}, {"model": "users.roleassignment", "pk": 4, "fields": {"role": 4, "user": 4, "assigned_date": "2025-01-04T11:00:00Z"}}, {"model": "users.roleassignment", "pk": 5, "fields": {"role": 1, "user": 5, "assigned_date": "2025-01-05T12:00:00Z"}}, {"model": "users.roleassignment", "pk": 6, "fields": {"role": 5, "user": 6, "assigned_date": "2025-01-06T08:00:00Z"}}, {"model": "users.roleassignment", "pk": 7, "fields": {"role": 6, "user": 7, "assigned_date": "2025-01-07T09:00:00Z"}}, {"model": "users.roleassignment", "pk": 8, "fields": {"role": 7, "user": 8, "assigned_date": "2025-01-08T10:00:00Z"}}, {"model": "users.roleassignment", "pk": 9, "fields": {"role": 6, "user": 9, "assigned_date": "2025-01-09T11:00:00Z"}}, {"model": "users.roleassignment", "pk": 10, "fields": {"role": 6, "user": 10, "assigned_date": "2025-01-10T12:00:00Z"}}, {"model": "users.roleassignment", "pk": 11, "fields": {"role": 8, "user": 11, "assigned_date": "2025-01-11T08:00:00Z"}}, {"model": "users.roleassignment", "pk": 12, "fields": {"role": 9, "user": 12, "assigned_date": "2025-01-12T09:00:00Z"}}, {"model": "users.roleassignment", "pk": 13, "fields": {"role": 10, "user": 13, "assigned_date": "2025-01-13T10:00:00Z"}}, {"model": "users.roleassignment", "pk": 14, "fields": {"role": 10, "user": 14, "assigned_date": "2025-01-14T11:00:00Z"}}, {"model": "users.roleassignment", "pk": 15, "fields": {"role": 9, "user": 15, "assigned_date": "2025-01-15T12:00:00Z"}}, {"model": "users.roleassignment", "pk": 16, "fields": {"role": 4, "user": 16, "assigned_date": "2025-01-16T08:00:00Z"}}, {"model": "users.roleassignment", "pk": 17, "fields": {"role": 6, "user": 17, "assigned_date": "2025-01-17T09:00:00Z"}}, {"model": "users.roleassignment", "pk": 18, "fields": {"role": 9, "user": 18, "assigned_date": "2025-01-18T10:00:00Z"}}, {"model": "users.roleassignment", "pk": 19, "fields": {"role": 7, "user": 19, "assigned_date": "2025-01-19T11:00:00Z"}}, {"model": "users.roleassignment", "pk": 20, "fields": {"role": 2, "user": 20, "assigned_date": "2025-01-20T12:00:00Z"}}, {"model": "users.roleassignment", "pk": 21, "fields": {"role": 1, "user": 22, "assigned_date": "2025-01-22T09:00:00Z"}}, {"model": "users.roleassignment", "pk": 22, "fields": {"role": 3, "user": 22, "assigned_date": "2025-01-22T09:00:00Z"}}]