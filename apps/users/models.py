import auto_prefetch
import operator
import re
from base64 import urlsafe_b64encode
from functools import reduce
from hashlib import sha1
from functools import lru_cache

from typing import List, Optional

import reversion

from django.contrib.auth.models import (
    AbstractBaseUser,
    BaseUserManager,
    PermissionsMixin,
)

from django.conf import settings
from django.contrib.contenttypes.models import ContentType


from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import F, Q, Value
from django.db.models.functions import Concat, Coalesce
from django.utils import timezone
from django_lifecycle import LifecycleModel, hook, AFTER_CREATE, BEFORE_SAVE


from apps.organizations.models import AuthenticatedDomains
from apps.utils.tasks import send_email_task


class BaseUserQuerySet(models.QuerySet):
    def search(self, query: str) -> models.QuerySet:
        """Case-insensitive search across multiple user fields."""
        quoted_re = re.compile(r'"(.*?)"')
        keywords = re.findall(quoted_re, query)
        keywords += re.sub(quoted_re, "", query).split()
        params = []
        fields = ("username", "first_name", "last_name", "email")
        for keyword in keywords:
            for field in fields:
                params.append(Q(**{f"{field}__icontains": keyword}))
        return self.filter(reduce(operator.or_, params))


class UserManager(BaseUserManager):
    """Custom user manager for passwordless authentication."""

    def get_queryset(self) -> BaseUserQuerySet:
        return BaseUserQuerySet(self.model, using=self._db)

    def search(self, query: str) -> models.QuerySet:
        return self.get_queryset().search(query)

    def create_user(self, username: str, email: str, **extra_fields) -> "User":
        """Creates a regular user without a password."""
        return self._create_user(
            username, email, is_staff=False, is_superuser=False, **extra_fields
        )

    def create_superuser(self, username: str, email: str, **extra_fields) -> "User":
        """Creates a superuser with admin privileges."""
        return self._create_user(
            username, email, is_staff=True, is_superuser=True, **extra_fields
        )

    def _create_user(
        self,
        username: str,
        email: str,
        is_staff: bool,
        is_superuser: bool,
        **extra_fields,
    ) -> "User":
        """Internal helper for user creation."""
        if not username:
            raise ValueError("The given username must be set.")
        email = self.normalize_email(email)
        now = timezone.now()
        user = self.model(
            username=username,
            email=email,
            is_staff=is_staff,
            is_superuser=is_superuser,
            last_login=now,
            date_joined=now,
            **extra_fields,
        )
        user.save(using=self._db)
        return user


@reversion.register()
class User(AbstractBaseUser, PermissionsMixin, auto_prefetch.Model, LifecycleModel):
    """
    Represents an authenticated user in the system.

    Supports:
    - Passwordless authentication
    - Company-based access control
    - Dynamic role assignment
    - Native database-level full_name field via GeneratedField
    """

    USERNAME_FIELD = "username"
    REQUIRED_FIELDS = ["email"]

    username: str = models.CharField(max_length=100, unique=True, db_index=True)
    first_name: Optional[str] = models.CharField(max_length=100, blank=True, null=True)
    last_name: Optional[str] = models.CharField(max_length=100, blank=True, null=True)
    email: str = models.EmailField(max_length=255, unique=True, db_index=True)
    is_staff: bool = models.BooleanField(default=False)
    is_active: bool = models.BooleanField(default=True)
    date_joined: timezone.datetime = models.DateTimeField(auto_now_add=True)

    company = auto_prefetch.ForeignKey(
        "organizations.Company",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="users",
    )

    # ✅ Generated Field (PostgreSQL 12+)
    full_name: Optional[str] = models.GeneratedField(
        expression=Coalesce(
            Concat(F("first_name"), Value(" "), F("last_name")),
            F("email"),
        ),
        output_field=models.CharField(max_length=255),
        db_persist=True,  # Persist computed value in DB for efficient lookups
    )

    # Remove old boolean role flags
    password = None  # Disable password field

    objects = UserManager()

    class Meta(auto_prefetch.Model.Meta):
        indexes = [
            models.Index(fields=["last_name", "first_name"]),
            models.Index(fields=["full_name"]),
        ]

    def __str__(self) -> str:
        return self.full_name or self.email

    # -----------------------------
    # Role Methods
    # -----------------------------
    @property
    def role_list(self) -> List[str]:
        """
        Return a list of the user's role names within their company.
        Roles are determined dynamically from RoleAssignment.
        """
        from apps.users.models import (
            RoleAssignment,
        )  # lazy import to avoid circular import

        role_names = (
            RoleAssignment.objects.filter(user=self, role__company=self.company)
            .select_related("role")
            .values_list("role__name", flat=True)
        )
        return list(role_names)

    @property
    def roles(self) -> str:
        """Return all roles as a comma-separated string."""
        return ", ".join(self.role_list)

    # -----------------------------
    # Utility Methods
    # -----------------------------
    @staticmethod
    def username_generator(email: str) -> str:
        """Generate a unique username based on email hash."""
        return (
            urlsafe_b64encode(sha1(email.encode("utf-8")).digest())
            .rstrip(b"=")
            .decode("ascii")
        )

    def set_username(self) -> None:
        """Assign a username if not already set."""
        if not self.username:
            self.username = self.username_generator(self.email)

    def validate_email_domain(self):
        """Ensure the user's email domain matches an authenticated domain."""
        if not self.email:
            return None
        domain = self.email.split("@")[1]
        try:
            authenticated_domain = AuthenticatedDomains.objects.get(domain=domain)
            return authenticated_domain.company
        except AuthenticatedDomains.DoesNotExist:
            raise ValidationError(f"The domain '{domain}' is not authenticated.")

    @property
    def is_company_admin(self) -> bool:
        """Check if the user is an admin of their company."""
        return self.company and self.company.admins.filter(user=self).exists()

    # -----------------------------
    # Lifecycle Hooks
    # -----------------------------
    @hook(AFTER_CREATE)
    def send_confirmation_email(self) -> None:
        """Send an onboarding email after user creation."""
        send_email_task.defer(
            subject="Welcome to Project Tracker",
            message="Your account has been successfully created.",
            from_email=settings.SYSTEM_ADMINISTRATOR_EMAIL,
            recipient_list=[self.email],
        )

    def save(self, *args, **kwargs) -> None:
        """Custom save to auto-assign company and username."""
        creating = not self.pk
        self.set_username()
        if creating and not self.is_superuser:
            company = self.validate_email_domain()
            if company:
                self.company = company
        super().save(*args, **kwargs)

    @lru_cache(maxsize=1)
    def get_modifiable_phases_for_user(self) -> List:
        phases = []
        # TODO: Create model to replicate the logic below
        # if self.is_location_manager or self.is_location_engineer:
        #     phases.append(Plan.Phase.NOT_STARTED)
        #     phases.append(Plan.Phase.LOCATIONS_RANK)
        # if self.is_operations_engineer or self.is_engineering_director:
        #     phases.append(Plan.Phase.OPERATIONS_NEED_AND_RISK_FACTOR)
        # if self.is_vice_president:
        #     phases.append(Plan.Phase.VP_RANK)
        # if self.is_senior_vice_president:
        #     phases.append(Plan.Phase.SVP_RANK)
        # if self.is_coo or self.is_financial_planning_administrator:
        #     phases.append(Plan.Phase.EXECUTIVE_ADJUSTMENT)
        return phases


class Role(models.Model):
    """
    Defines a named role with an optional description.
    Corporate customers can create and manage roles dynamically.
    """

    company = models.ForeignKey(
        "organizations.Company", on_delete=models.CASCADE, related_name="roles"
    )

    name: str = models.CharField(max_length=150, unique=True)
    description: str = models.TextField(blank=True, null=True)

    def __str__(self) -> str:
        return self.name


class ModelPermission(models.Model):
    """
    Defines CRUD permissions for a given role and model.
    Uses Django's ContentType system so it applies to *any* model.
    """

    role: Role = models.ForeignKey(
        Role, on_delete=models.CASCADE, related_name="permissions"
    )
    content_type: ContentType = models.ForeignKey(ContentType, on_delete=models.CASCADE)

    can_create: bool = models.BooleanField(default=False)
    can_read: bool = models.BooleanField(default=False)
    can_update: bool = models.BooleanField(default=False)
    can_delete: bool = models.BooleanField(default=False)

    class Meta:
        unique_together = ("role", "content_type")

    def __str__(self) -> str:
        return f"{self.role.name} → {self.content_type.app_label}.{self.content_type.model}"


class RoleAssignment(LifecycleModel, models.Model):
    """
    Assigns an organizational role to a user within their company.
    These are company-wide roles that determine general permissions.

    Example:
    - Alice has the "Project Manager" role in the company
    - Bob has the "Finance Lead" role in the company

    This determines what permissions they have across the system.
    """

    role = models.ForeignKey(
        "users.Role",
        on_delete=models.CASCADE,
        related_name="organizational_assignments",
    )

    user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="organizational_role_assignments",
    )

    assigned_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("role", "user")
        verbose_name = "Organizational Role Assignment"
        verbose_name_plural = "Organizational Role Assignments"

    def __str__(self) -> str:
        return f"{self.user} → {self.role.name}"

    @hook(BEFORE_SAVE)
    def validate_user_belongs_to_company_role(self):
        if self.user.company != self.role.company:
            raise ValidationError("User and role must belong to the same company.")
