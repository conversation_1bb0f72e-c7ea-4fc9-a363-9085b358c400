from django.db.models import Max
from lxml.html import fromstring
from rest_framework import fields, serializers

from apps.projects.models import Division, Project, ProjectType, SubPillar
from apps.users.models import User
from apps.users.serializers import UserSerializer
from apps.utils.html import bleach

from .forms import EXPENDITURE_TYPE_CHOICES
from .utils import calculate_total_strategic_value_score

BOOLEAN_CHOICES = [(0, "No"), (1, "Yes")]


class NestedProjectTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProjectType
        fields = ["name"]


class NestedStrategicSubPillarSerializer(serializers.ModelSerializer):
    pillar = serializers.SerializerMethodField()

    class Meta:
        model = SubPillar
        fields = ["name", "pillar"]

    @staticmethod
    def get_pillar(data):
        return data.pillar.name


class IdeaConvertSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()
    executive_owners = UserSerializer(many=True)
    business_leads = UserSerializer(many=True)
    finance_leads = UserSerializer(many=True)
    business_analysts = UserSerializer(many=True)
    other_stakeholders = UserSerializer(many=True)
    project_managers = UserSerializer(many=True)
    capital_expenditure = serializers.SerializerMethodField()
    opex_expenditure = serializers.SerializerMethodField()
    tags = serializers.SerializerMethodField()
    primary_division = serializers.SerializerMethodField()
    business_case = serializers.SerializerMethodField()
    has_technology_components = serializers.SerializerMethodField()
    current_environment = serializers.SerializerMethodField()
    failure_severity = serializers.SerializerMethodField()
    response_to_audit = serializers.SerializerMethodField()
    estimation_confidence = serializers.SerializerMethodField()
    location = serializers.SerializerMethodField()
    project_types = NestedProjectTypeSerializer(many=True)
    strategic_subpillars = NestedStrategicSubPillarSerializer(many=True)

    class Meta:
        model = Project
        fields = (
            "id",
            "private",
            "name",
            "summary",
            "primary_division",
            "start_date",
            "internal_savings_initiative",
            "committed_to_spend",
            "created_by",
            "executive_owners",
            "business_leads",
            "finance_leads",
            "business_analysts",
            "other_stakeholders",
            "technology_components",
            "capital_expenditure",
            "opex_expenditure",
            "annualized_savings",
            "funding_size",
            "funding_source",
            "payback_period",
            "has_technology_components",
            "complexity",
            "priority",
            "tags",
            "current_environment",
            "failure_severity",
            "response_to_audit",
            "estimation_confidence",
            "project_managers",
            "business_case",
            "has_technology_components",
            "current_environment",
            "failure_severity",
            "response_to_audit",
            "estimation_confidence",
            "location",
            "project_types",
            "strategic_subpillars",
        )

    @staticmethod
    def get_location(data):
        if data.location:
            return data.location.name

    @staticmethod
    def get_has_technology_components(data):
        return data.has_technology_components

    @staticmethod
    def get_estimation_confidence(data):
        # TODO: need to update Project Tracker to use sensible variable values.
        estimation_confidence = {
            Project.ESTIMATION_CONFIDENCE_NONE: 0,
            Project.ESTIMATION_CONFIDENCE_VERY_LOW: 0,
            Project.ESTIMATION_CONFIDENCE_LOW: 1,
            Project.ESTIMATION_CONFIDENCE_MEDIUM: 2,
            Project.ESTIMATION_CONFIDENCE_HIGH: 3,
            Project.ESTIMATION_CONFIDENCE_VERY_HIGH: 4,
        }

        return estimation_confidence.get(data.estimation_confidence)

    @staticmethod
    def get_response_to_audit(data):
        # TODO: need to update Project Tracker to use sensible variable values.
        response_to_audit = {
            Project.RESPONSE_TO_AUDIT_VIOLATION: 2,
            Project.RESPONSE_TO_AUDIT_YES: 1,
            Project.RESPONSE_TO_AUDIT_NO: 0,
            Project.RESPONSE_TO_AUDIT_NONE: 0,
        }
        return response_to_audit.get(data.response_to_audit)

    @staticmethod
    def get_failure_severity(data):
        # TODO: need to update Project Tracker to use sensible variable values.
        failure_severity = {
            Project.FAILURE_SEVERITY_NONE: -1,
            Project.FAILURE_SEVERITY_LOW: 0,
            Project.FAILURE_SEVERITY_MODERATE: 1,
            Project.FAILURE_SEVERITY_HIGH: 2,
        }
        return failure_severity.get(data.failure_severity)

    @staticmethod
    def get_current_environment(data):
        # TODO: need to update Project Tracker to use sensible variable values.
        current_environment = {
            Project.CURRENT_ENVIRONMENT_HIGH: 2,
            Project.CURRENT_ENVIRONMENT_MODERATE: 1,
            Project.CURRENT_ENVIRONMENT_LOW: 0,
            Project.CURRENT_ENVIRONMENT_NONE: -1,
        }

        return current_environment.get(data.current_environment)

    @staticmethod
    def get_primary_division(obj):
        return obj.primary_division.name

    @staticmethod
    def get_financial_numbers(idx):
        financials = [
            "none",
            "less than 25k",
            "25k to 250k",
            "250k to 1m",
            "1m to 5m",
            "greater than 5m",
            None,
        ]
        if idx:
            return financials[int(idx)]
        return None

    @staticmethod
    def get_capital_expenditure(obj):
        return obj.capital_expenditure

    @staticmethod
    def get_opex_expenditure(obj):
        return obj.opex_expenditure

    @staticmethod
    def get_business_case(obj):
        if obj.business_case:
            return obj.business_case
        return " "

    @staticmethod
    def get_tags(obj):
        return [tag.name for tag in obj.tags.all()]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["created_by"] = data.pop("created_by")
        data["other_stakeholders"] = data.pop("stakeholders")
        return data


class ProjectTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProjectType
        fields = ["id", "name"]

    def to_representation(self, instance):
        return {"label": instance.name, "value": instance.id}


class IdeaSerializer(serializers.ModelSerializer):
    created_by = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        default=serializers.CreateOnlyDefault(serializers.CurrentUserDefault()),
    )
    last_edited = serializers.HiddenField(default=serializers.CurrentUserDefault())
    start_date = serializers.DateField(
        required=False,
        allow_null=True,
        input_formats=["%Y-%m-%dT%H:%M:%S.%fZ", "%Y-%m-%d"],
    )
    strategic_value = serializers.SerializerMethodField()
    primary_division = serializers.IntegerField(source="primary_division_id")

    class Meta:
        model = Project
        fields = (
            "id",
            "location",
            "private",
            "name",
            "summary",
            "primary_division",
            "start_date",
            "ready_to_begin",
            "executive_owners",
            "business_leads",
            "finance_leads",
            "business_analysts",
            "other_stakeholders",
            "internal_savings_initiative",
            "committed_to_spend",
            "annualized_savings",
            "funding_size",
            "funding_source",
            "payback_period",
            "has_technology_components",
            "technology_components",
            "priority",
            "complexity",
            "created_by",
            "last_edited",
            "created",
            "modified",
            "strategic_value",
            "project_state",
            "tags",
            "project_managers",
            "current_environment",
            "failure_severity",
            "response_to_audit",
            "estimation_confidence",
            "business_case",
            "project_types",
            "strategic_subpillars",
        )

    def get_strategic_value(self, instance):
        return calculate_total_strategic_value_score(instance)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if "tags" in representation.keys():
            representation["tags"] = [tag.name for tag in representation["tags"].all()]
        else:
            representation["tags"] = []
        return representation

    def run_validation(self, data=fields.empty):
        summary = data.get("summary", None)
        data["summary"] = bleach(summary)
        business_case = data.get("business_case", None)
        data["business_case"] = bleach(business_case)
        return super().run_validation(data)


class IdeaListFilterSerializer(serializers.Serializer):
    filter_elements = serializers.SerializerMethodField()
    home_url = serializers.CharField(max_length=50)
    url_querystring = serializers.CharField(max_length=255)
    show_my_targets = serializers.SerializerMethodField()
    show_private = serializers.SerializerMethodField()

    @staticmethod
    def build_filter_element(name, api_name, options, input_type, is_multi=False):
        return {
            "name": name,
            "inputType": input_type,
            "apiName": api_name,
            "heading": name,
            "options": options,
            "hasSelectedOptions": any(elem["selected"] for elem in options),
            "isMulti": is_multi,
        }

    def get_associated_users(
        self, obj, field_name, title, related_field_name, input_type="select"
    ):
        selected_user_ids = getattr(obj, field_name)

        user_options = [
            {
                "value": user.id,
                "label": user.full_name,
                "selected": str(user.id) in selected_user_ids,
                "initially_selected": str(user.id) in selected_user_ids,
                "isMulti": True,
            }
            for user in User.objects.exclude(
                **{related_field_name + "__isnull": True}
            ).order_by("first_name", "last_name")
        ]
        opts = self.build_filter_element(
            title, field_name, user_options, input_type, True
        )
        return opts

    def get_choice_field(
        self, obj, field_name, title, choices, input_type="select", is_multi=False
    ):
        attr = getattr(obj, field_name)

        if attr:
            selected_opts = attr
        elif field_name == "state":
            selected_opts = [None]
        else:
            selected_opts = []

        choice_options = [
            {
                "value": str(opt[0]),
                "label": opt[1],
                "selected": str(opt[0]) in selected_opts,
                "initially_selected": str(opt[0]) in selected_opts,
            }
            for opt in choices
        ]

        return self.build_filter_element(
            title, field_name, choice_options, input_type, is_multi
        )

    def get_range_field(self, obj, field_name, title, range_values, input_type="range"):
        min_attr = getattr(obj, field_name + "_min")
        max_attr = getattr(obj, field_name + "_max")

        if min_attr:
            min_attr = int(min_attr)
        else:
            min_attr = int(range_values[0])
        if max_attr:
            max_attr = int(max_attr)
        else:
            max_attr = int(range_values[1])

        if min_attr or max_attr:
            ranges = [min_attr, max_attr]
            options = [
                {
                    "value": opt,
                    "selected": opt not in range_values,
                    "initially_selected": opt not in range_values,
                }
                for opt in ranges
            ]
        else:
            options = [
                {"value": opt, "selected": False, "initially_selected": True}
                for opt in range_values
            ]

        return {
            "name": title,
            "inputType": input_type,
            "apiName": field_name,
            "min": range_values[0],
            "max": range_values[1],
            "options": options,
        }

    def get_filter_elements(self, obj):
        self.users = User.objects.all()
        division_choices = [
            (division.id, division.name)
            for division in Division.objects.order_by("name")
        ]

        state = self.get_choice_field(
            obj, "state", "Idea State", Project.STATE_CHOICES[:-1], is_multi=True
        )

        division = self.get_choice_field(
            obj, "primary_division", "Primary Segment", division_choices, is_multi=True
        )

        executive_owners = self.get_associated_users(
            obj, "executive_owners", "Executive Owner", "idea_executive_owners"
        )
        finance_leads = self.get_associated_users(
            obj, "finance_leads", "Finance Lead", "idea_finance_leads"
        )
        business_leads = self.get_associated_users(
            obj, "business_leads", "Business Lead", "idea_business_leads"
        )
        business_analysts = self.get_associated_users(
            obj, "business_analysts", "Business Analyst", "idea_business_analysts"
        )
        other_stakeholders = self.get_associated_users(
            obj, "other_stakeholders", "Stakeholder", "idea_stakeholder"
        )

        committed_to_spend = self.get_choice_field(
            obj, "committed_to_spend", "Committed to Spend", [(0, "No"), (1, "Yes")]
        )
        internal_savings_initiative = self.get_choice_field(
            obj,
            "internal_savings_initiative",
            "Internal Savings Initiative",
            [(0, "No"), (1, "Yes")],
        )
        has_technology_components = self.get_choice_field(
            obj, "has_technology_components", "Technology Components", BOOLEAN_CHOICES
        )

        expenditure_type = self.get_choice_field(
            obj, "expenditure_type", "Expenditure Type", EXPENDITURE_TYPE_CHOICES
        )

        funding_size = self.get_choice_field(
            obj,
            "funding_size",
            "Funding Size",
            Project.FUNDING_SIZE_CHOICES[1:],
            is_multi=True,
        )
        annualized_savings = self.get_choice_field(
            obj,
            "annualized_savings",
            "Annualized Savings",
            Project.FUNDING_SIZE_CHOICES[1:],
            is_multi=True,
        )

        funding_source = self.get_choice_field(
            obj,
            "funding_source",
            "Funding Source",
            Project.FUNDING_SOURCE_CHOICES,
            is_multi=True,
        )

        payback_period = self.get_choice_field(
            obj,
            "payback_period",
            "Payback Period",
            Project.PAYBACK_PERIOD_CHOICES,
            is_multi=True,
        )

        priority = self.get_choice_field(
            obj, "priority", "Priority", Project.PRIORITY_CHOICES, is_multi=True
        )
        complexity = self.get_choice_field(
            obj, "complexity", "Complexity", Project.COMPLEXITY_CHOICES, is_multi=True
        )

        max_impact = (
            Project.objects.all()
            .with_strategic_value_score()
            .aggregate(Max("strategic_value"))["strategic_value__max"]
        )
        try:
            max_impact = min(int(max_impact), 40)
        except TypeError:
            max_impact = 0

        strategic_value = self.get_range_field(
            obj, "strategic_value", "Strategic Value", [0, max_impact]
        )

        project_managers = self.get_associated_users(
            obj, "project_managers", "Project Manager", "idea_project_managers"
        )

        people_fields = [
            executive_owners,
            project_managers,
            business_leads,
            finance_leads,
            business_analysts,
            other_stakeholders,
        ]

        selected_people_options = any(
            [
                any(elem["selected"] for elem in field["options"])
                for field in people_fields
            ]
        )

        finance_fields = [
            internal_savings_initiative,
            committed_to_spend,
            expenditure_type,
            funding_size,
            annualized_savings,
            funding_source,
            payback_period,
        ]
        selected_finance_options = any(
            [
                any(elem["selected"] for elem in field["options"])
                for field in finance_fields
            ]
        )

        impact_fields = [
            has_technology_components,
            priority,
            complexity,
            strategic_value,
        ]

        selected_impact_options = any(
            [
                any(elem["selected"] for elem in field["options"])
                for field in impact_fields
            ]
        )

        return [
            {
                "heading": "State",
                "fields": [state],
                "hasSelectedOptions": any(
                    elem["selected"] for elem in state["options"]
                ),
            },
            {
                "heading": "Segment",
                "fields": [division],
                "hasSelectedOptions": any(
                    elem["selected"] for elem in division["options"]
                ),
            },
            {
                "heading": "People",
                "fields": people_fields,
                "hasSelectedOptions": selected_people_options,
            },
            {
                "heading": "Finance",
                "fields": finance_fields,
                "hasSelectedOptions": selected_finance_options,
            },
            {
                "heading": "Impact",
                "fields": impact_fields,
                "hasSelectedOptions": selected_impact_options,
            },
        ]

    @staticmethod
    def get_show_my_targets(obj):
        if obj.show_my_targets and int(obj.show_my_targets):
            return True
        return False

    @staticmethod
    def get_show_private(obj):
        if obj.show_private:
            return True
        return False


def get_user_name(user):
    if user:
        if user.first_name and user.last_name:
            return user.first_name + " " + user.last_name
        return user.email


class ExportIdeaSerializer(serializers.ModelSerializer):
    start_date = serializers.SerializerMethodField()
    primary_division = serializers.SerializerMethodField()
    other_stakeholders = serializers.SerializerMethodField()
    ready_to_begin = serializers.SerializerMethodField()
    state = serializers.SerializerMethodField()
    executive_owner = serializers.SerializerMethodField()
    finance_lead = serializers.SerializerMethodField()
    business_lead = serializers.SerializerMethodField()
    business_analyst = serializers.SerializerMethodField()
    itt_business_relationship_manager = serializers.SerializerMethodField()
    internal_savings_initiative = serializers.SerializerMethodField()
    committed_to_spend = serializers.SerializerMethodField()
    annualized_savings = serializers.SerializerMethodField()
    funding_size = serializers.SerializerMethodField()
    priority = serializers.SerializerMethodField()
    complexity = serializers.SerializerMethodField()
    creator = serializers.SerializerMethodField()
    created = serializers.SerializerMethodField()
    modified = serializers.SerializerMethodField()
    expenditure_type = serializers.SerializerMethodField()
    funding_source = serializers.SerializerMethodField()
    payback_period = serializers.SerializerMethodField()
    technology_components = serializers.SerializerMethodField()
    summary = serializers.SerializerMethodField()
    tags = serializers.SerializerMethodField()
    project_manager = serializers.SerializerMethodField()
    comments = serializers.SerializerMethodField()
    visibility = serializers.SerializerMethodField()
    business_case = serializers.SerializerMethodField()
    strategic_value = serializers.SerializerMethodField()
    location = serializers.SerializerMethodField()
    project_types = serializers.SerializerMethodField()
    strategic_pillars = serializers.SerializerMethodField()
    audit_finding_response = serializers.SerializerMethodField()
    current_environment = serializers.SerializerMethodField()
    failure_severity = serializers.SerializerMethodField()
    estimation_confidence = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = (
            "id",
            "visibility",
            "name",
            "summary",
            "tags",
            "primary_division",
            "start_date",
            "ready_to_begin",
            "state",
            "executive_owner",
            "finance_lead",
            "business_lead",
            "business_analyst",
            "other_stakeholders",
            "itt_business_relationship_manager",
            "internal_savings_initiative",
            "committed_to_spend",
            "expenditure_type",
            "annualized_savings",
            "failure_severity",
            "funding_size",
            "funding_source",
            "payback_period",
            "technology_components",
            "priority",
            "complexity",
            "creator",
            "strategic_value",
            "created",
            "modified",
            "project_manager",
            "comments",
            "business_case",
            "location",
            "project_types",
            "strategic_pillars",
            "audit_finding_response",
            "current_environment",
            "estimation_confidence",
        )
        pandas_index = ["name"]

    @staticmethod
    def get_audit_finding_response(data):
        return data.get_response_to_audit_display()

    @staticmethod
    def get_current_environment(data):
        return data.get_current_environment_display()

    @staticmethod
    def get_estimation_confidence(data):
        return data.get_estimation_confidence_display()

    @staticmethod
    def get_failure_severity(data):
        return data.get_failure_severity_display()

    @staticmethod
    def get_project_types(data):
        return "; ".join([projecttype.name for projecttype in data.project_types.all()])

    @staticmethod
    def get_location(data):
        if data.location:
            return data.location.name
        return ""

    @staticmethod
    def get_strategic_pillars(data):
        if data.strategic_pillars.count():
            return "; ".join([pillar.name for pillar in data.strategic_pillars.all()])
        return ""

    @staticmethod
    def get_strategic_value(data):
        if not data.strategic_value:
            return 0
        return float(data.strategic_value)

    @staticmethod
    def get_primary_division(data):
        return data.primary_division.name if data.primary_division else ""

    @staticmethod
    def get_summary(data):
        try:
            doc = fromstring(data.summary)
            return doc.text_content()
        except ValueError:
            return ""

    @staticmethod
    def get_tags(data):
        if data.tags.count():
            return "; ".join(tag.name for tag in data.tags.all())
        return ""

    @staticmethod
    def get_business_case(data):
        try:
            doc = fromstring(data.business_case)
            return doc.text_content()
        except ValueError:
            return ""

    @staticmethod
    def get_start_date(data):
        if data.start_date:
            return data.start_date.strftime("%-m/%-d/%Y")

    @staticmethod
    def get_technology_components(data):
        return data.technology_components

    @staticmethod
    def get_created(data):
        return data.created.strftime("%-m/%-d/%Y")

    @staticmethod
    def get_modified(data):
        return data.created.strftime("%-m/%-d/%Y")

    @staticmethod
    def get_other_stakeholders(data):
        return ", ".join(
            [
                get_user_name(stakeholder)
                for stakeholder in data.other_stakeholders.all()
            ]
        )

    @staticmethod
    def get_ready_to_begin(data):
        if data.ready_to_begin:
            return "Yes"
        return "No"

    @staticmethod
    def get_state(data):
        return data.project_state

    @staticmethod
    def get_annualized_savings(data):
        if data.annualized_savings:
            return Project.MONEY_AMOUNT_DICT.get(data.annualized_savings, None)

    @staticmethod
    def get_expenditure_type(data):
        if data.capital_expenditure:
            return "Capital"
        elif data.opex_expenditure:
            return "OpEx"
        return ""

    @staticmethod
    def get_payback_period(data):
        if data.payback_period is not None:
            return Project.PAYBACK_PERIOD_DICT.get(data.payback_period, None)

    @staticmethod
    def get_funding_source(data):
        if data.funding_source is not None:
            return Project.FUNDING_SOURCE_DICT.get(data.funding_source, None)

    @staticmethod
    def get_funding_size(data):
        if data.funding_size:
            return Project.MONEY_AMOUNT_DICT.get(data.funding_size, None)

    @staticmethod
    def get_priority(data):
        if data.priority:
            return Project.PRIORITY_DICT.get(data.priority, None)

    @staticmethod
    def get_complexity(data):
        if data.complexity:
            return Project.COMPLEXITY_DICT.get(data.complexity, None)

    @staticmethod
    def get_executive_owner(data):
        return ", ".join(
            [
                get_user_name(executive_owner)
                for executive_owner in data.executive_owners.all()
            ]
        )

    @staticmethod
    def get_creator(data):
        return get_user_name(data.created_by)

    @staticmethod
    def get_finance_lead(data):
        return ", ".join(
            [get_user_name(finance_lead) for finance_lead in data.finance_leads.all()]
        )

    @staticmethod
    def get_business_lead(data):
        return ", ".join(
            [
                get_user_name(business_lead)
                for business_lead in data.business_leads.all()
            ]
        )

    @staticmethod
    def get_business_analyst(data):
        return ", ".join(
            [
                get_user_name(business_analyst)
                for business_analyst in data.business_analysts.all()
            ]
        )

    @staticmethod
    def get_internal_savings_initiative(data):
        if data.internal_savings_initiative:
            return "Yes"
        return "No"

    @staticmethod
    def get_committed_to_spend(data):
        if data.committed_to_spend:
            return "Yes"
        return "No"

    @staticmethod
    def get_project_manager(data):
        return ", ".join(
            [
                get_user_name(project_manager)
                for project_manager in data.project_managers.all()
            ]
        )

    @staticmethod
    def get_comments(data: Project) -> str:
        comments = data.comments.all().order_by("-created")
        comment_lines = [
            f"{comment.creator.full_name} ({comment.created.strftime('%b %d %Y %H:%M:%S')}): {comment.text}"
            for comment in comments[:3]
        ]
        return ", ".join(comment_lines)

    @staticmethod
    def get_visibility(data):
        if data.private:
            return "Project Team"
        return "All Members"
