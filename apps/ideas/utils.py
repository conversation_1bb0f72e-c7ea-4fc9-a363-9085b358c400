from django.urls import reverse

from apps.notifications.models import MessageTypes, notify
from apps.notifications.view_models import IdeaState
from apps.projects.models import Project
from apps.users.models import User


class ListFilter(object):
    def __init__(
        self,
        state=None,
        department=None,
        location=None,
        project_type=None,
        executive_owners=None,
        project_managers=None,
        finance_leads=None,
        business_leads=None,
        business_analysts=None,
        other_stakeholders=None,
        committed_to_spend=None,
        internal_savings_initiative=None,
        has_technology_components=None,
        expenditure_type=None,
        funding_size=None,
        annualized_savings=None,
        funding_source=None,
        payback_period=None,
        priority=None,
        complexity=None,
        strategic_value=None,
        strategic_value_min=None,
        strategic_value_max=None,
        url_querystring=None,
        show_my_targets=None,
        show_private=None,
        home_url="/ideas/",
    ):
        self.state = state
        self.department = department
        self.location = location
        self.project_type = project_type
        self.executive_owners = executive_owners
        self.business_leads = business_leads
        self.project_managers = project_managers
        self.finance_leads = finance_leads
        self.business_analysts = business_analysts
        # self.other_stakeholders = stakeholders

        self.committed_to_spend = committed_to_spend
        self.internal_savings_initiative = internal_savings_initiative
        self.has_technology_components = has_technology_components
        self.expenditure_type = expenditure_type
        self.funding_size = funding_size
        self.annualized_savings = annualized_savings
        self.funding_source = funding_source
        self.payback_period = payback_period
        self.priority = priority
        self.complexity = complexity
        self.strategic_value = strategic_value
        self.strategic_value_min = strategic_value_min
        self.strategic_value_max = strategic_value_max
        self.url_querystring = url_querystring
        self.home_url = home_url
        self.show_my_targets = show_my_targets
        self.show_private = show_private


def has_edit_delete_perms(idea, user):
    return (
        user.is_superuser
        or idea.created_by == user
        or any(
            str.lower(person.email) == str.lower(user.email)
            for person in idea.executive_owners.all()
        )
        or any(
            str.lower(person.email) == str.lower(user.email)
            for person in idea.project_managers.all()
        )
        or any(
            str.lower(person.email) == str.lower(user.email)
            for person in idea.business_leads.all()
        )
        or any(
            str.lower(person.email) == str.lower(user.email)
            for person in idea.business_analysts.all()
        )
        or any(
            str.lower(person.email) == str.lower(user.email)
            for person in idea.finance_leads.all()
        )
    )


def has_view_perms(idea, user):
    if not idea.private:
        return True

    users_with_access = [idea.created_by.id]

    for usr in idea.executive_owners.all():
        users_with_access.append(usr.id)

    for usr in idea.project_managers.all():
        users_with_access.append(usr.id)

    for usr in idea.business_leads.all():
        users_with_access.append(usr.id)

    for usr in idea.business_analysts.all():
        users_with_access.append(usr.id)

    for usr in idea.other_stakeholders.all():
        users_with_access.append(usr.id)

    for usr in idea.finance_leads.all():
        users_with_access.append(usr.id)

    return user.is_superuser or user.id in users_with_access


def calculate_total_strategic_value_score(idea):
    if hasattr(idea, "current_environment"):
        failure_risk = Project.CURRENT_ENVIRONMENT_VALUES[idea.current_environment or 0]
    else:
        failure_risk = 1

    if hasattr(idea, "failure_severity"):
        failure_severity = Project.FAILURE_SEVERITY_VALUES[idea.failure_severity or 0]
    else:
        failure_severity = 1

    risk_rating = failure_risk * failure_severity

    priority = Project.PRIORITY_VALUES[idea.priority or Project.PRIORITY_LOW]
    complexity = Project.COMPLEXITY_VALUES[idea.complexity or Project.COMPLEXITY_LOW]

    impact_score = priority + complexity + risk_rating

    if not impact_score:
        impact_score = 1

    response_to_audit_score = Project.RESPONSE_TO_AUDIT_VALUES[
        idea.response_to_audit or 1
    ]

    strategic_score = impact_score * response_to_audit_score

    funding_size = Project.MONEY_AMOUNT_VALUES[
        idea.funding_size or Project.MONEY_AMOUNT_NONE
    ]
    annualized_savings = Project.MONEY_AMOUNT_VALUES[
        idea.annualized_savings or Project.MONEY_AMOUNT_NONE
    ]
    payback_score = Project.PAYBACK_PERIOD_VALUES[
        idea.payback_period or Project.PAYBACK_PERIOD_NONE
    ]

    financial_score = funding_size + annualized_savings + payback_score

    total_score = strategic_score + financial_score

    if total_score >= 40:
        return 40
    return total_score


def notify_on_changes(
    idea: Project, user: User, previous_state: IdeaState, current_state: IdeaState
):
    changed_fields = current_state.get_changed_fields(previous_state)
    for field in changed_fields:
        previous_value = getattr(previous_state, field)
        current_value = getattr(current_state, field)
        if field == "id":
            message = "{user} added <a href='{url}'>{name}</a>.".format(
                user=user.full_name,
                url=reverse("idea-detail", args=[idea.pk]),
                name=idea.name,
            )
            notify(
                message_type=MessageTypes.IDEA_NEW,
                message=message,
                creator=user,
                content_object=idea,
            )
        elif field == "executive_owners":
            added_users = set(current_value) - set(previous_value)
            message = "{user} added You as an Executive Owner to <a href='{url}'>{name}</a>.".format(
                user=user.full_name,
                url=reverse("idea-detail", args=[idea.pk]),
                name=idea.name,
            )
            notify(
                message_type=MessageTypes.ROLE_ASSIGNED,
                message=message,
                creator=user,
                content_object=idea,
                recipients=added_users,
            )
        elif field == "project_managers":
            added_users = set(current_value) - set(previous_value)
            message = "{user} added You as a Project Manager to <a href='{url}'>{name}</a>.".format(
                user=user.full_name,
                url=reverse("idea-detail", args=[idea.pk]),
                name=idea.name,
            )
            notify(
                message_type=MessageTypes.ROLE_ASSIGNED,
                message=message,
                creator=user,
                content_object=idea,
                recipients=added_users,
            )
        elif field == "business_leads":
            added_users = set(current_value) - set(previous_value)
            message = "{user} added You as a Business Lead to <a href='{url}'>{name}</a>.".format(
                user=user.full_name,
                url=reverse("idea-detail", args=[idea.pk]),
                name=idea.name,
            )
            notify(
                message_type=MessageTypes.ROLE_ASSIGNED,
                message=message,
                creator=user,
                content_object=idea,
                recipients=added_users,
            )
        elif field == "finance_leads":
            added_users = set(current_value) - set(previous_value)
            message = "{user} added You as a Finance Lead to <a href='{url}'>{name}</a>.".format(
                user=user.full_name,
                url=reverse("idea-detail", args=[idea.pk]),
                name=idea.name,
            )
            notify(
                message_type=MessageTypes.ROLE_ASSIGNED,
                message=message,
                creator=user,
                content_object=idea,
                recipients=added_users,
            )
        elif field == "business_analysts":
            added_users = set(current_value) - set(previous_value)
            message = "{user} added You as a Business Analyst to <a href='{url}'>{name}</a>.".format(
                user=user.full_name,
                url=reverse("idea-detail", args=[idea.pk]),
                name=idea.name,
            )
            notify(
                message_type=MessageTypes.ROLE_ASSIGNED,
                message=message,
                creator=user,
                content_object=idea,
                recipients=added_users,
            )
        elif field == "additional_stakeholders":
            added_users = set(current_value) - set(previous_value)
            message = "{user} added You as a Stakeholder to <a href='{url}'>{name}</a>.".format(
                user=user.full_name,
                url=reverse("idea-detail", args=[idea.pk]),
                name=idea.name,
            )
            notify(
                message_type=MessageTypes.ROLE_ASSIGNED,
                message=message,
                creator=user,
                content_object=idea,
                recipients=added_users,
            )
        elif field == "technology_components" and current_value:
            message = "{user} marked <a href='{url}'>{name}</a> as an idea with Technical Components.".format(
                user=user.full_name,
                url=reverse("idea-detail", args=[idea.pk]),
                name=idea.name,
            )
            notify(
                message_type=MessageTypes.PROJECT_TECH_COMPONENTS,
                message=message,
                creator=user,
                content_object=idea,
            )
        elif field == "ready_to_convert" and current_value:
            message = "{user} indicated <a href='{url}'>{name}</a> is an idea that is Ready to Convert.".format(
                user=user.full_name,
                url=reverse("idea-detail", args=[idea.pk]),
                name=idea.name,
            )
            notify(
                message_type=MessageTypes.IDEA_READY_TO_CONVERT,
                message=message,
                creator=user,
                content_object=idea,
            )
        elif field == "converted" and current_value:
            message = "{user} converted <a href='{url}'>{name}</a> to a <a href='{project_url}'>Project</a>.".format(
                user=user.full_name,
                url=reverse("idea-detail", args=[idea.pk]),
                name=idea.name,
                project_url=idea.project_url,
            )
            notify(
                message_type=MessageTypes.IDEA_CONVERTED,
                message=message,
                creator=user,
                content_object=idea,
            )
