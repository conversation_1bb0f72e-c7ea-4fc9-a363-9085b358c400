import operator
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from functools import reduce

import django_filters as filters
from django.db.models.query import Q, QuerySet
from django.utils import timezone

from apps.locations.models import Location
from apps.projects.models import Division, Project, ProjectType
from apps.users.models import User


BOOLEAN_CHOICES = [(0, False), (1, True)]


class NumberInFilter(filters.BaseInFilter, filters.NumberFilter):
    pass


class IdeaFilter(filters.FilterSet):
    committed_to_spend = filters.ChoiceFilter(choices=BOOLEAN_CHOICES)
    modified_within = filters.NumberFilter(method="filter_modified_within")
    internal_savings_initiative = filters.ChoiceFilter(choices=BOOLEAN_CHOICES)
    has_technology_components = filters.ChoiceFilter(choices=BOOLEAN_CHOICES)
    state = filters.MultipleChoiceFilter(
        choices=[
            (Project.PROJECT_STATE_ACTIVE, "Captured"),
            (Project.PROJECT_STATE_ON_HOLD, "On Hold"),
            (Project.PROJECT_STATE_CANCELLED, "Archived"),
            ("Converted", "Converted"),
        ],
        method="filter_state",
    )
    priority = filters.MultipleChoiceFilter(choices=Project.PRIORITY_CHOICES)
    strategic_value = filters.RangeFilter()
    search = filters.CharFilter(method="filter_search")
    department = filters.MultipleChoiceFilter(
        method="filter_primary_division",
        choices=[],  # Initialized empty, set in __init__
    )
    location = filters.MultipleChoiceFilter(method="filter_location")
    project_type = filters.MultipleChoiceFilter(method="filter_project_type")
    person = filters.ModelChoiceFilter(
        queryset=User.objects.all(), method="filter_person"
    )
    my_targets = filters.NumberFilter(method="filter_my_targets")
    executive_owners = filters.ModelMultipleChoiceFilter(
        method="filter_executive_owners",
        queryset=User.objects.all(),
        field_name="attr__id",
    )
    business_leads = filters.ModelMultipleChoiceFilter(
        method="filter_business_leads",
        queryset=User.objects.all(),
        field_name="attr__id",
    )
    business_analysts = filters.ModelMultipleChoiceFilter(
        method="filter_business_analysts",
        queryset=User.objects.all(),
        field_name="attr__id",
    )
    other_stakeholders = filters.ModelMultipleChoiceFilter(
        method="filter_other_stakeholders",
        queryset=User.objects.all(),
        field_name="attr__id",
    )
    project_managers = filters.ModelMultipleChoiceFilter(
        method="filter_project_managers",
        queryset=User.objects.all(),
        field_name="attr__id",
    )
    funding_size = filters.MultipleChoiceFilter(
        method="filter_funding_size", choices=Project.MONEY_AMOUNT_CHOICES
    )
    annualized_savings = filters.MultipleChoiceFilter(
        method="filter_annualized_savings", choices=Project.MONEY_AMOUNT_CHOICES
    )
    funding_source = filters.MultipleChoiceFilter(
        method="filter_funding_source", choices=Project.FUNDING_SOURCE_CHOICES
    )
    payback_period = filters.MultipleChoiceFilter(
        method="filter_payback_period", choices=Project.PAYBACK_PERIOD_CHOICES
    )

    class Meta:
        model = Project
        fields = [
            "state",
            "primary_division",  # multi
            "location",  # multi
            "project_type",  # multi
            "executive_owners",  # multi
            "project_managers",  # multi
            "finance_leads",  # multi
            "business_leads",  # multi
            "business_analysts",  # multi
            "other_stakeholders",  # multi
            "committed_to_spend",
            "internal_savings_initiative",
            # "expenditure_type",
            "funding_size",  # multi
            "annualized_savings",  # multi
            "funding_source",  # multi
            "payback_period",  # multi
            "priority",
            "has_technology_components",
            "strategic_value",
            "priority",  # multi
            "complexity",  # multi
            "my_targets",
        ]

    def __init__(self, data, *args, **kwargs):
        super().__init__(data, *args, **kwargs)
        self.filters["location"].extra["choices"] = (
            *[(location.id, location.name) for location in Location.objects.all()],
        )

        self.filters["project_type"].extra["choices"] = (
            *[
                (project_type.id, project_type.name)
                for project_type in ProjectType.objects.all()
            ],
        )
        self.fields["department"].extra["choices"] = [("0", "Unassigned Segment")] + [
            (division.pk, division.name)
            for division in Division.objects.all().order_by("name")
        ]

    @staticmethod
    def filter_search(queryset: QuerySet, name: str, value: str) -> QuerySet:
        return queryset.search(value)

    def filter_my_targets(self, queryset: QuerySet, name: str, value: int) -> QuerySet:
        if value == 1:
            return queryset.filter(
                Q(created_by__email=self.request.user.email)
                | Q(executive_owners__email=self.request.user.email)
                | Q(business_leads__email=self.request.user.email)
                | Q(business_analysts__email=self.request.user.email)
                | Q(project_managers__email=self.request.user.email)
                | Q(other_stakeholders__email=self.request.user.email)
            ).distinct()
        return queryset

    @staticmethod
    def filter_payback_period(queryset, name: str, value: str) -> QuerySet:
        if not value:
            return queryset

        return queryset.filter(payback_period__in=value)

    @staticmethod
    def filter_annualized_savings(queryset, name: str, value: str) -> QuerySet:
        if not value:
            return queryset

        return queryset.filter(annualized_savings__in=value)

    @staticmethod
    def filter_funding_size(queryset, name: str, value: str) -> QuerySet:
        if not value:
            return queryset

        return queryset.filter(funding_size__in=value)

    @staticmethod
    def filter_funding_source(queryset, name: str, value: str) -> QuerySet:
        if not value:
            return queryset

        return queryset.filter(funding_source__in=value)

    @staticmethod
    def filter_primary_division(queryset: QuerySet, name: str, value: list) -> QuerySet:
        try:
            division_ids = [int(val) for val in value]
        except ValueError:
            return queryset

        if 0 in division_ids:
            return queryset.filter(
                Q(primary_division__id__in=division_ids)
                | Q(primary_division__isnull=True)
            )
        return queryset.filter(primary_division__id__in=division_ids)

    @staticmethod
    def filter_location(queryset: QuerySet, name: str, value: list) -> QuerySet:
        try:
            location_ids = [int(val) for val in value]
        except ValueError:
            return queryset

        if 0 in location_ids:
            return queryset.filter(
                Q(location__id__in=location_ids) | Q(location__isnull=True)
            )
        return queryset.filter(location__id__in=location_ids)

    @staticmethod
    def filter_state(queryset: QuerySet, name: str, value: list) -> QuerySet:
        active = Project.PROJECT_STATE_ACTIVE in value or "Converted" in value
        on_hold = Project.PROJECT_STATE_ON_HOLD in value
        archived = Project.PROJECT_STATE_ARCHIVED in value
        include_idea_phase = len(list(filter(lambda x: x != "Converted", value))) > 0
        only_idea_phase = "Converted" not in value
        phases = [
            p[0]
            for p in filter(
                lambda p: p[0] != Project.PHASE_IDEA or include_idea_phase,
                Project.PHASE_CHOICES,
            )
        ]
        filters = []
        if active:
            filters.append(
                Q(project_state=Project.PROJECT_STATE_ACTIVE, phase__in=phases)
            )
        if on_hold:
            filters.append(Q(project_state=Project.PROJECT_STATE_ON_HOLD))
        if archived:
            filters.append(Q(project_state=Project.PROJECT_STATE_CANCELLED))
        query = Q()
        for f in filters:
            query |= f
        if only_idea_phase:
            queryset = queryset.filter(phase=Project.PHASE_IDEA)
        return queryset.filter(query)

    @staticmethod
    def filter_project_type(queryset: QuerySet, name: str, value: list) -> QuerySet:
        try:
            project_type_ids = [int(val) for val in value]
        except ValueError:
            return queryset

        if 0 in project_type_ids:
            return queryset.filter(
                Q(project_types__id__in=project_type_ids)
                | Q(project_types__isnull=True)
            )
        return queryset.filter(project_types__id__in=project_type_ids)

    @staticmethod
    def filter_executive_owners(queryset: QuerySet, name: str, value) -> QuerySet:
        return queryset.filter(executive_owners__in=value) if value else queryset

    @staticmethod
    def filter_business_leads(queryset: QuerySet, name: str, value) -> QuerySet:
        return queryset.filter(business_leads__in=value) if value else queryset

    @staticmethod
    def filter_business_analysts(queryset: QuerySet, name: str, value) -> QuerySet:
        return queryset.filter(business_analysts__in=value) if value else queryset

    @staticmethod
    def filter_other_stakeholders(queryset: QuerySet, name: str, value) -> QuerySet:
        return queryset.filter(other_stakeholders__in=value) if value else queryset

    @staticmethod
    def filter_project_managers(queryset: QuerySet, name: str, value) -> QuerySet:
        return queryset.filter(project_managers__in=value) if value else queryset

    def get_url_querystring(self):
        url_querystring = "?"
        data_dict = dict(self.data)
        for elem in data_dict.keys():
            for entry in data_dict[elem]:
                url_querystring += f"{elem}={entry}&"
        return url_querystring

    def filter_person(self, queryset: QuerySet, name: str, value: User) -> QuerySet:
        """
        Filter projects by person.
        If roles are also selected, only show projects where the person has those roles.
        Otherwise, show all projects where the person is a team member.
        """
        if not value:
            return queryset

        # Get selected roles if any
        selected_roles = None
        if "role" in self.data.keys():
            role_ids = self.data.getlist("role", None)
            if role_ids:
                selected_roles = Role.objects.filter(id__in=role_ids)

        # Filter by team membership
        queryset = queryset.filter(team_members=value)

        # If specific roles are selected, further filter by those roles
        if selected_roles:
            from apps.users.models import RoleAssignment

            # Check if the person actually has any of the selected roles
            person_has_roles = RoleAssignment.objects.filter(
                user=value, role__in=selected_roles
            ).exists()

            if not person_has_roles:
                # Person doesn't have any of the selected roles, return empty queryset
                return queryset.none()

            # Person has at least one of the selected roles
            # Filter projects where they have role assignments matching the selected roles
            queryset = queryset.filter(
                projectroleassignment__user=value,
                projectroleassignment__role__in=selected_roles,
            ).distinct()

        return queryset

    def filter_modified_within(self, queryset: QuerySet, name: str, value: Decimal):
        queryset = queryset.filter(
            modified__gte=timezone.now() - timedelta(days=int(value))
        )
        return queryset
