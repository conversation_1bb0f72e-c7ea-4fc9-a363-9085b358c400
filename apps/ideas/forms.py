from django import forms

from apps.attachments.models import Attachment
from apps.links.models import Link
from apps.projects.forms import ProjectForm
from apps.projects.models import Division, Project
from apps.users.models import User, Role
from apps.utils.fields import CheckboxSelect, HTMLField


EXPENDITURE_TYPE_OPEX = "opex"
EXPENDITURE_TYPE_CAPITAL = "capital"
EXPENDITURE_TYPE_CHOICES = (
    (EXPENDITURE_TYPE_CAPITAL, "Capital"),
    (EXPENDITURE_TYPE_OPEX, "OpEx"),
)
EXPENDITURE_TYPE_DICT = dict(EXPENDITURE_TYPE_CHOICES)


class IdeaForm(ProjectForm):
    PROJECT_STATE_CHOICES = (
        (Project.PROJECT_STATE_ACTIVE, "Captured"),
        (Project.PROJECT_STATE_ON_HOLD, "On Hold"),
        (Project.PROJECT_STATE_ARCHIVED, "Archived"),
    )
    PHASE_CHOICES = ((Project.PHASE_IDEA, "Idea"),)

    name = forms.CharField(label="Idea Name")
    summary = HTMLField(
        label="Summary", help_text=Project._meta.get_field("summary").help_text
    )
    ready_to_begin = forms.BooleanField(
        label="Ready to Begin the Project",
        required=False,
        widget=forms.CheckboxInput(attrs={"switch": True}),
    )
    expenditure_type = forms.ChoiceField(
        choices=(("Capital", "Capital"), ("OpEx", "OpEx")),
        label="Expenditure Type",
        required=False,
        widget=CheckboxSelect(attrs={"class": "SelectButtons"}),
        initial="Capital",
    )
    estimation_confidence = forms.ChoiceField(
        widget=CheckboxSelect(attrs={"class": "SelectButtons"}), required=False
    )
    funding_source = forms.ChoiceField(
        widget=CheckboxSelect(attrs={"class": "SelectButtons"}), required=False
    )
    payback_period = forms.ChoiceField(
        widget=CheckboxSelect(attrs={"class": "SelectButtons"}), required=False
    )
    has_technology_components = forms.ChoiceField(
        label="Technology Components",
        widget=CheckboxSelect(attrs={"class": "SelectButtons"}),
        required=False,
    )
    response_to_audit = forms.ChoiceField(
        label="Audit Finding Response",
        widget=CheckboxSelect(attrs={"class": "SelectButtons"}),
        required=False,
    )
    priority = forms.ChoiceField(
        widget=CheckboxSelect(attrs={"class": "SelectButtons"}), required=False
    )
    complexity = forms.ChoiceField(
        widget=CheckboxSelect(attrs={"class": "SelectButtons"}), required=False
    )
    current_environment = forms.ChoiceField(
        label="Failure Risk",
        widget=CheckboxSelect(attrs={"class": "SelectButtons"}),
        required=False,
    )
    failure_severity = forms.ChoiceField(
        widget=CheckboxSelect(attrs={"class": "SelectButtons"}), required=False
    )

    attachments = forms.ModelMultipleChoiceField(
        queryset=Attachment.objects.all(), required=False
    )
    links = forms.ModelMultipleChoiceField(queryset=Link.objects.all(), required=False)

    class Meta:
        model = Project
        fields = ProjectForm.Meta.fields + ["ready_to_begin", "expenditure_type"]
        labels = {
            "ready_to_begin": "Ready to Begin the Project",
            "start_date": "Desired Start Date",
            "internal_savings_initiative": "Hard Dollar Savings",
            "primary_division": "Primary Segment",
            "other_involved_divisions": "Other Involved Segments",
            "other_stakeholders": "Additional Stakeholders",
        }
        widgets = {
            "internal_savings_initiative": forms.CheckboxInput(attrs={"switch": True}),
            "committed_to_spend": forms.CheckboxInput(attrs={"switch": True}),
            "start_date": forms.DateInput(attrs={"type": "date"}),
        }

    def __init__(self, *args, phase=Project.PHASE_IDEA, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["name"].tooltip = "A short, descriptive title for the initiative"
        self.fields[
            "internal_savings_initiative"
        ].tooltip = (
            "Once complete, this project will realize a hard-dollar savings benefit."
        )
        self.fields[
            "estimation_confidence"
        ].choices = Project.ESTIMATION_CONFIDENCE_CHOICES
        self.fields["funding_size"].choices = (
            ("", "Select Amount"),
        ) + Project.MONEY_AMOUNT_CHOICES
        self.fields["annualized_savings"].choices = (
            ("", "Select Amount"),
        ) + Project.MONEY_AMOUNT_CHOICES
        self.fields["funding_source"].choices = Project.FUNDING_SOURCE_CHOICES
        self.fields["payback_period"].choices = Project.PAYBACK_PERIOD_CHOICES
        self.fields[
            "failure_severity"
        ].help_text = "What is the operational impact should the system fail?"
        del self.fields["phase"]

    def clean_current_environment(self):
        current_environment = self.cleaned_data["current_environment"]
        return current_environment or None

    def clean_failure_severity(self):
        failure_severity = self.cleaned_data["failure_severity"]
        return failure_severity or None

    def clean_response_to_audit(self):
        response_to_audit = self.cleaned_data["response_to_audit"]
        return response_to_audit or None

    def clean_estimation_confidence(self):
        estimation_confidence = self.cleaned_data["estimation_confidence"]
        return estimation_confidence or None

    def clean_capital_expenditure(self):
        return self.data.get("expenditure_type") == "Capital"

    def clean_opex_expenditure(self):
        return self.data.get("expenditure_type") == "OpEx"

    def save(self, *args, **kwargs):
        attachments = []
        links = []

        if kwargs.get("commit", True):
            attachments = self.cleaned_data.pop("attachments", [])
            links = self.cleaned_data.pop("links", [])

        instance: Project = super().save(*args, **kwargs)

        for attachment in attachments:
            attachment.content_object = self.instance
            attachment.save()

        for link in links:
            link.content_object = self.instance
            link.save()

        return instance


class FilterForm(forms.Form):
    BOOLEAN_CHOICES = [(0, "No"), (1, "Yes")]

    MODIFIED_WITHIN_CHOICES = (
        ("", "Any Time"),
        (1, "Last Day"),
        (7, "Last Week"),
        (30, "Last Month"),
        (365, "Last Year"),
    )

    person = forms.ModelChoiceField(
        queryset=User.objects.order_by("first_name", "last_name"),
        empty_label="Show All",
        required=False,
    )

    state = forms.MultipleChoiceField(
        choices=[
            (Project.PROJECT_STATE_ACTIVE, "Captured"),
            (Project.PROJECT_STATE_ON_HOLD, "On Hold"),
            (Project.PROJECT_STATE_CANCELLED, "Archived"),
            ("Converted", "Converted"),
        ],
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )

    department = forms.MultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        choices=[],  # Initialized empty, set in __init__
        label="Segment",
        required=False,
    )

    role = forms.ModelMultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        queryset=Role.objects.none(),
        required=False,
    )

    modified_within = forms.TypedChoiceField(
        label="Date Range",
        choices=MODIFIED_WITHIN_CHOICES,
        coerce=int,
        required=False,
        widget=forms.RadioSelect(),
    )

    # Finance filter fields
    internal_savings_initiative = forms.ChoiceField(
        choices=BOOLEAN_CHOICES,
        required=False,
        widget=forms.RadioSelect(attrs={"clearable": True}),
    )

    committed_to_spend = forms.ChoiceField(
        choices=BOOLEAN_CHOICES,
        required=False,
        widget=forms.RadioSelect(attrs={"clearable": True}),
    )

    expenditure_type = forms.MultipleChoiceField(
        choices=EXPENDITURE_TYPE_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )

    funding_size = forms.MultipleChoiceField(
        choices=Project.MONEY_AMOUNT_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )

    annualized_savings = forms.MultipleChoiceField(
        choices=Project.MONEY_AMOUNT_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )

    funding_source = forms.MultipleChoiceField(
        choices=Project.FUNDING_SOURCE_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )

    payback_period = forms.MultipleChoiceField(
        choices=Project.PAYBACK_PERIOD_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )

    # Impact filter fields
    has_technology_components = forms.ChoiceField(
        choices=BOOLEAN_CHOICES,
        required=False,
        widget=forms.RadioSelect(attrs={"clearable": True}),
    )

    priority = forms.MultipleChoiceField(
        choices=Project.PRIORITY_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )

    complexity = forms.MultipleChoiceField(
        choices=Project.COMPLEXITY_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )

    strategic_value_min = forms.IntegerField(
        min_value=0, max_value=100, initial=0, required=False
    )
    strategic_value_max = forms.IntegerField(
        min_value=0, max_value=40, initial=40, required=False
    )

    def __init__(self, *args, **kwargs):
        user = kwargs.pop("user")
        super().__init__(*args, **kwargs)
        self.fields["state"].widget.attrs.update({"clearable": True})
        self.fields["department"].widget.attrs.update({"clearable": True})
        # Filter divisions by user's company
        divisions = Division.objects.for_company(user.company).order_by("name")
        self.fields["department"].choices = [("0", "Unassigned Segment")] + [
            (division.pk, division.name)
            for division in divisions
        ]

        self.fields["role"].queryset = Role.objects.filter(company=user.company)
