from django.db.models import Q
from django.http import Http404, HttpResponse
from django.http.request import HttpRequest
from django.http.response import HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string
from django.urls import reverse
from django.views import View
from django.views.generic import ListView
from django.views.generic.detail import SingleObjectMixin
from weasyprint import HTML

from apps.projects.models import Project
from apps.utils.mixins import LoginRequiredMixin

from .filters import TeamFilter
from .models import Team
from .serializers import ProjectPDFSerializer


class TeamListView(LoginRequiredMixin, ListView):
    model = Team
    template_name = "teams/team_list.html"


class TeamDetailView(LoginRequiredMixin, SingleObjectMixin, ListView):
    paginate_by = 10
    template_name = "teams/team_detail.html"
    slug_field = "slug"

    def get(self, request, *args, **kwargs):
        try:
            self.object = self.get_object(queryset=Team.objects.all())
        except Http404:
            return HttpResponseRedirect(reverse("team-list"))
        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["team"] = self.object
        context["project_pks"] = [project.pk for project in self.object_list]
        return context

    def get_queryset(self):
        self.object: Team
        # get the projects where the executive sponsor is the team's executive sponsor *and* the
        # business lead is one or more of the members of the team.
        queryset = Project.objects.filter(project_state="active").filter(
            Q(executive_owners=self.object.executive_sponsor)
            & Q(finance_leads__in=self.object.members.all())
        )

        queryset = TeamFilter(self.request.GET, queryset=queryset).qs.distinct()
        return queryset


class PDFDeckCreateAndDownloadView(LoginRequiredMixin, View):
    def get(self, request: HttpRequest, slug: str, *args, **kwargs):
        team = get_object_or_404(Team, slug=slug)
        project_ids = request.GET.getlist("id")
        projects = Project.objects.filter(active=True).filter(pk__in=project_ids)

        project_data = ProjectPDFSerializer(
            projects, many=True, context={"team": team}
        ).data
        template_string = render_to_string(
            template_name="teams/pdf/pdf_template.html",
            context={"projects": project_data},
        )
        pdf_file = HTML(
            string=template_string, base_url=request.build_absolute_uri()
        ).write_pdf()

        filename = "project-list.pdf"

        response = HttpResponse(pdf_file, content_type="application/pdf")
        response["Content-Disposition"] = f'attachment; filename="{filename}"'

        return response
