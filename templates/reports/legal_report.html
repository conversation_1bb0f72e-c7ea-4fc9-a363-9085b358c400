{% extends "reports/reports_base.html" %}

{% load static %}
{% load humanize %}
{% load sitewide_tags %}
{% load project_tags %}
{% load report_tags %}

{% block css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'dist/css/legal-report.css' %}" />
{% endblock %}


{% block javascript %}
    {{ block.super }}
    <script type="text/javascript" src="{%  static 'dist/javascript/expenditures-report.js' %}"></script>
{% endblock %}

{% block main %}
    {{ block.super }}

    <div class="PageContent">
        <div class="u-container VerticalStack VerticalStack--large">
            <div class="BadgeList">
                <div class="Badge Badge--small Badge--blue">
                    <div class="Badge-count Badge-main">{{ submitted_count }}</div>
                    <div class="Badge-footer">Submitted</div>
                </div>
                <div class="Badge Badge--small Badge--blue">
                    <div class="Badge-count Badge-main">{{ initial_review_count }}</div>
                    <div class="Badge-footer">Initial Review</div>
                </div>
                <div class="Badge Badge--small Badge--blue">
                    <div class="Badge-count Badge-main">{{ submitted_legal_count }}</div>
                    <div class="Badge-footer">Submitted to Legal</div>
                </div>
                <div class="Badge Badge--small Badge--green">
                    <div class="Badge-count Badge-main">{{ approved_count }}</div>
                    <div class="Badge-footer">Approved</div>
                </div>

            </div>
            <div>
                {% legal_filter_bar %}
                <table class="LegalTable">
                    <thead>
                        <tr class="LegalTable-row">
                            <th class="LegalTable-header LegalTable-header--id" data-sort-key="id">
                                ID # {% sort_icon 'id' %}
                            </th>
                            <th class="LegalTable-header LegalTable-header--name" data-sort-key="project">
                                Project Name {% sort_icon 'project' %}
                            </th>
                            <th class="LegalTable-header LegalTable-header--name" data-sort-key="name">
                                Product Name {% sort_icon 'name' %}
                            </th>
                            <th class="LegalTable-header LegalTable-header--capital" data-sort-key="executive_owner"> Executive Owner {% sort_icon 'executive_owner' %}</th>
                            <th class="LegalTable-header LegalTable-header--operational" data-sort-key="primary_segment">Primary Segment {% sort_icon 'primary_segment' %}</th>

                            <th class="LegalTable-header LegalTable-header--operational" data-sort-key="legal_review">Legal Review Status {% sort_icon 'legal_review' %}</th>
                            <th class="LegalTable-header LegalTable-header--operational" data-sort-key="modified">Last Update {% sort_icon 'modified' %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in object_list %}
                            <tr class="LegalTable-row">
                                <td class="LegalTable-cell LegalTable-cell--id">
                                    {{ product.id }}
                                </td>
                                <td class="LegalTable-cell LegalTable-cell--name">
                                    <a class="Table-link" href="{% url 'project_detail' product.project.pk %}">
                                        <span>{{ product.project.name }}</span>
                                        <svg class="icon-link" width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                            <g transform="translate(-214.000000, -932.000000)" fill="#266287">
                                                <g transform="translate(72.000000, 923.000000)">
                                                    <g transform="translate(112.000000, 0.000000)">
                                                        <g transform="translate(27.000000, 6.000000)">
                                                            <path d="M16.5624677,9.78123383 C16.5624677,11.0025852 16.2571297,12.1327907 15.6464542,13.1718507 C15.0357787,14.2109108 14.2109108,15.0357787 13.1718507,15.6464542 C12.1327907,16.2571297 11.0025852,16.5624677 9.78123383,16.5624677 C8.55988244,16.5624677 7.42967694,16.2571297 6.39061692,15.6464542 C5.35155689,15.0357787 4.52668893,14.2109108 3.91601344,13.1718507 C3.30533795,12.1327907 3,11.0025852 3,9.78123383 C3,8.55988244 3.30533795,7.42967694 3.91601344,6.39061692 C4.52668893,5.35155689 5.35155689,4.52668893 6.39061692,3.91601344 C7.42967694,3.30533795 8.55988244,3 9.78123383,3 C11.0025852,3 12.1327907,3.30533795 13.1718507,3.91601344 C14.2109108,4.52668893 15.0357787,5.35155689 15.6464542,6.39061692 C16.2571297,7.42967694 16.5624677,8.55988244 16.5624677,9.78123383 Z M4.31249687,9.78123383 C4.31249687,10.7656065 4.55859003,11.6770625 5.05077636,12.5156023 C5.54296269,13.3541421 6.20832554,14.019505 7.04686535,14.5116913 C7.88540516,15.0038776 8.79686118,15.2499708 9.78123383,15.2499708 C10.7656065,15.2499708 11.6770625,15.0038776 12.5156023,14.5116913 C13.3541421,14.019505 14.019505,13.3541421 14.5116913,12.5156023 C15.0038776,11.6770625 15.2499708,10.7656065 15.2499708,9.78123383 C15.2499708,8.79686118 15.0038776,7.88540516 14.5116913,7.04686535 C14.019505,6.20832554 13.3541421,5.54296269 12.5156023,5.05077636 C11.6770625,4.55859003 10.7656065,4.31249687 9.78123383,4.31249687 C8.79686118,4.31249687 7.88540516,4.55859003 7.04686535,5.05077636 C6.20832554,5.54296269 5.54296269,6.20832554 5.05077636,7.04686535 C4.55859003,7.88540516 4.31249687,8.79686118 4.31249687,9.78123383 Z M6.28124218,9.23436014 C6.28124218,9.14321466 6.31314328,9.06574061 6.37694507,9.00193882 C6.44074687,8.93813702 6.51822092,8.90623592 6.60936639,8.90623592 L9.78123383,8.90623592 L9.78123383,7.07420904 C9.78123383,6.92837619 9.84959304,6.82811587 9.98631147,6.7734285 C10.1230299,6.71874113 10.2369621,6.7369704 10.3281075,6.82811587 L13.062476,9.53514067 C13.1171634,9.6080573 13.1445071,9.69008836 13.1445071,9.78123383 C13.1445071,9.87237931 13.1171634,9.95441036 13.062476,10.027327 L10.3554512,12.7070081 C10.2460765,12.8163828 10.1230299,12.8437265 9.98631147,12.7890392 C9.84959304,12.7343518 9.78123383,12.6340915 9.78123383,12.4882586 L9.78123383,10.6562317 L6.60936639,10.6562317 C6.51822092,10.6562317 6.44074687,10.6243306 6.37694507,10.5605288 C6.31314328,10.4967271 6.28124218,10.419253 6.28124218,10.3281075 L6.28124218,9.23436014 Z" id="icn-internallink"></path>
                                                        </g>
                                                    </g>
                                                </g>
                                            </g>
                                        </svg>
                                    </a>
                                </td>
                                <td class="LegalTable-cell LegalTable-cell--name">
                                    {{ product.name }}
                                </td>
                                <td class="LegalTable-cell ">
                                    {{ product.project.executive_owners.first }}
                                </td>
                                <td class="LegalTable-cell">
                                    {{ product.project.primary_division }}
                                </td>
                                <td class="LegalTable-cell">

                                </td>
                                <td class="LegalTable-cell LegalTable-cell-status{% if product.state.value == "Approved" %} approved{% endif %}">
                                    {{ product.display_status }}
                                </td>
                                <td class="LegalTable-cell">
                                    {{ product.modified|date:"n/j/Y" }}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>

                {% include "pagination.html" with label='Products'%}
            </div>
        </div>
    </div>
{% endblock %}
