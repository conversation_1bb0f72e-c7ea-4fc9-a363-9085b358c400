{% extends "reports/reports_base.html" %}

{% load static %}
{% load sitewide_tags %}
{% load project_tags %}
{% load report_tags %}

{% block css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'dist/css/compliance-report.css' %}" />
{% endblock %}

{% block javascript %}
    {{ block.super }}
    <script type="text/javascript" src="{%  static 'dist/javascript/compliance-report.js' %}"></script>
{% endblock %}

{% block main %}
    {{ block.super }}

    <div class="PageContent">
        <div class="u-container">
            {% compliance_filter_bar %}
            <table class="ComplianceTable">
                <thead>
                    <tr class="ComplianceTable-row">
                        <th class="ComplianceTable-header ComplianceTable-header--id" data-sort-key="id">
                            ID # {% sort_icon 'id' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--name" data-sort-key="name">
                            Project Name {% sort_icon 'name' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--phase" data-sort-key="phase">
                            Phase {% sort_icon 'phase' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--lastUpdate" data-sort-key="modified">
                            Last Update {% sort_icon 'modified' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--complete" data-sort-key="percent_complete_grade">
                            % Complete {% sort_icon 'percent_complete_grade' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--expenditure" data-sort-key="expenditure_grade">
                            Expenditure {% sort_icon 'expenditure_grade' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--health" data-sort-key="health_grade">
                            Health {% sort_icon 'health_grade' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--accomplishments" data-sort-key="recent_accomplishments_grade">
                            Accomp-lishments {% sort_icon 'recent_accomplishments_grade' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--activities" data-sort-key="planned_activities_grade">
                            Activities {% sort_icon 'planned_activities_grade' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--impact" data-sort-key="technology_components_grade">
                            Tech Impact {% sort_icon 'technology_components_grade' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--executiveOwner" data-sort-key="executive_owners_grade">
                            Exec. Owner {% sort_icon 'executive_owners_grade' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--pm" data-sort-key="project_managers_grade">
                            PM {% sort_icon 'project_managers_grade' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--endDate" data-sort-key="end_date">
                            End Date {% sort_icon 'end_date' %}
                        </th>
                        <th class="ComplianceTable-header ComplianceTable-header--grade"  data-sort-key="grade">
                            Grade {% sort_icon 'grade' %}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for project in project_list %}
                        <tr class="ComplianceTable-row">
                            <td class="ComplianceTable-cell ComplianceTable-cell--id">
                                {{ project.id }}
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--name">
                                <a class="Table-link" href="{% url 'project_detail' project.pk %}">
                                    <span>{{ project.name }}</span>
                                    <svg class="icon-link" width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <g transform="translate(-214.000000, -932.000000)" fill="#266287">
                                            <g transform="translate(72.000000, 923.000000)">
                                                <g transform="translate(112.000000, 0.000000)">
                                                    <g transform="translate(27.000000, 6.000000)">
                                                        <path d="M16.5624677,9.78123383 C16.5624677,11.0025852 16.2571297,12.1327907 15.6464542,13.1718507 C15.0357787,14.2109108 14.2109108,15.0357787 13.1718507,15.6464542 C12.1327907,16.2571297 11.0025852,16.5624677 9.78123383,16.5624677 C8.55988244,16.5624677 7.42967694,16.2571297 6.39061692,15.6464542 C5.35155689,15.0357787 4.52668893,14.2109108 3.91601344,13.1718507 C3.30533795,12.1327907 3,11.0025852 3,9.78123383 C3,8.55988244 3.30533795,7.42967694 3.91601344,6.39061692 C4.52668893,5.35155689 5.35155689,4.52668893 6.39061692,3.91601344 C7.42967694,3.30533795 8.55988244,3 9.78123383,3 C11.0025852,3 12.1327907,3.30533795 13.1718507,3.91601344 C14.2109108,4.52668893 15.0357787,5.35155689 15.6464542,6.39061692 C16.2571297,7.42967694 16.5624677,8.55988244 16.5624677,9.78123383 Z M4.31249687,9.78123383 C4.31249687,10.7656065 4.55859003,11.6770625 5.05077636,12.5156023 C5.54296269,13.3541421 6.20832554,14.019505 7.04686535,14.5116913 C7.88540516,15.0038776 8.79686118,15.2499708 9.78123383,15.2499708 C10.7656065,15.2499708 11.6770625,15.0038776 12.5156023,14.5116913 C13.3541421,14.019505 14.019505,13.3541421 14.5116913,12.5156023 C15.0038776,11.6770625 15.2499708,10.7656065 15.2499708,9.78123383 C15.2499708,8.79686118 15.0038776,7.88540516 14.5116913,7.04686535 C14.019505,6.20832554 13.3541421,5.54296269 12.5156023,5.05077636 C11.6770625,4.55859003 10.7656065,4.31249687 9.78123383,4.31249687 C8.79686118,4.31249687 7.88540516,4.55859003 7.04686535,5.05077636 C6.20832554,5.54296269 5.54296269,6.20832554 5.05077636,7.04686535 C4.55859003,7.88540516 4.31249687,8.79686118 4.31249687,9.78123383 Z M6.28124218,9.23436014 C6.28124218,9.14321466 6.31314328,9.06574061 6.37694507,9.00193882 C6.44074687,8.93813702 6.51822092,8.90623592 6.60936639,8.90623592 L9.78123383,8.90623592 L9.78123383,7.07420904 C9.78123383,6.92837619 9.84959304,6.82811587 9.98631147,6.7734285 C10.1230299,6.71874113 10.2369621,6.7369704 10.3281075,6.82811587 L13.062476,9.53514067 C13.1171634,9.6080573 13.1445071,9.69008836 13.1445071,9.78123383 C13.1445071,9.87237931 13.1171634,9.95441036 13.062476,10.027327 L10.3554512,12.7070081 C10.2460765,12.8163828 10.1230299,12.8437265 9.98631147,12.7890392 C9.84959304,12.7343518 9.78123383,12.6340915 9.78123383,12.4882586 L9.78123383,10.6562317 L6.60936639,10.6562317 C6.51822092,10.6562317 6.44074687,10.6243306 6.37694507,10.5605288 C6.31314328,10.4967271 6.28124218,10.419253 6.28124218,10.3281075 L6.28124218,9.23436014 Z" id="icn-internallink"></path>
                                                    </g>
                                                </g>
                                            </g>
                                        </g>
                                    </svg>
                                </a>
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--phase">
                                {{ project.phase_display|default:"None" }}
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--lastUpdate">
                                <span class="status status--{{ project.modified_status }}">{{ project.modified|date:"m/d/Y" }}</span>
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--complete">
                                {% icon_colored_check project.has_percent_complete title=project.latest_percentage %}
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--expenditure">
                                {% icon_colored_check project.expenditure_grade title=expenditure_grade %}
{#                                {{ project.capital_actuals }} {{ project.opex_actuals }}#}
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--health">
                                {% icon_colored_check project.has_health title=project.latest_health|capfirst %}
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--accomplishments">
                                {% icon_colored_check project.has_recent_accomplishments title=project.projectrecentaccomplishment_set.count|stringformat:"s Accomplishments" %}
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--activities">
                                {% icon_colored_check project.has_planned_activities title=project.projectplannedactivity_set.count|stringformat:"s Activities" %}
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--impact">
                                {% icon_colored_check project.technology_components_grade %}
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--executiveOwner">
                                {% icon_colored_check project.executive_owners.all|length title=project.executive_owners.all|join:", " %}
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--pm">
                                {% icon_colored_check project.project_managers.all|length title=project.project_managers.all|join:", " %}
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--endDate">
                                <span class="status status--{{ project.end_date_status }}" {% if project.overdue %}title="{{ project.end_date|timesince }} overdue"{% endif %}>{{ project.end_date|date:"m/d/Y" }}</span>
                            </td>
                            <td class="ComplianceTable-cell ComplianceTable-cell--grade ComplianceTable-cell--{% if project.grade >= 80 %}green{% elif project.grade <= 59 %}red{% else %}yellow{% endif %}">
                                {{ project.grade }}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>

            {% include "pagination.html" with label='Projects'%}
        </div>
    </div>
{% endblock %}
