{% extends "base.html" %}

{% load static %}
{% load waffle_tags %}
{% load sitewide_tags %}
{% load notification_tags %}

{% block javascript %}
    {{ block.super }}
    {% set_base_template "project_tracker_base.html" %}
{% endblock %}

{% block title %}{{ user.company.name }} Project Tracker{% endblock %}

{% block theme %}ThemeOrange{% endblock %}

{% block header %}
    <header class="SiteHeader">
        <div class="SiteHeader-top">
            <div class="u-container SiteHeader-top-container">
                <div class="HorizontalStack HorizontalStack--compact u-flex-align-baseline">
                    {% block site_logo %}
                        <a class="SiteLogo-link" href="/">
                            <img class="h-24" src="{{ user.company.logo.url }}" alt="{{ user.company.name }} Project Tracker"  />
                        </a>
                    {% endblock %}
                    <div class="Dropdown">
                        <svg class="Dropdown-toggle SisterSiteDropdown-toggle" width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <g transform="translate(-203.000000, -15.000000)" fill="#FFFFFF" fill-rule="nonzero">
                                <path d="M207.076933,20.6874864 L212.485535,20.6874864 C212.778091,20.6874864 212.925758,21.0429543 212.717956,21.248032 L210.013655,23.9359109 C209.885134,24.0644315 209.680057,24.0644315 209.551536,23.9359109 L206.847262,21.248032 C206.63671,21.0429543 206.784377,20.6874864 207.076933,20.6874864 Z M216.562468,21.7812338 C216.562468,25.5273187 213.527319,28.5624677 209.781234,28.5624677 C206.035149,28.5624677 203,25.5273187 203,21.7812338 C203,18.**********.035149,15 209.781234,15 C213.527319,15 216.562468,18.035149 216.562468,21.7812338 Z M215.249971,21.7812338 C215.249971,18.7597567 212.802711,16.3124969 209.781234,16.3124969 C206.759757,16.3124969 204.312497,18.7597567 204.312497,21.7812338 C204.312497,24.802711 206.759757,27.2499708 209.781234,27.2499708 C212.802711,27.2499708 215.249971,24.802711 215.249971,21.7812338 Z" id="icon-/-icn-site-switcher"></path>
                            </g>
                        </svg>
                    </div>
                </div>
                <div class="HorizontalStack HorizontalStack--compact u-flex-align-center u-align-right">
                    <div class="UserActions Dropdown HorizontalStack HorizontalStack--compact">
                        <span class="UserActions-userName Dropdown-toggle">{{ request.user.first_name}} {{ request.user.get_last_initial }}.</span>
                        <img class="UserActions-icon Dropdown-toggle" src="{% static 'dist/images/icons/icon-user.svg' %}" alt="User" />
                        <div class="Dropdown-box">
                            <div class="UserBadge">
                                <p class="UserBadge-text">Logged in as:</p>
                                <p class="UserBadge-email">{{ request.user.email }}</p>
                                <p class="UserBadge-role">{% if request.user.is_superuser %}Admin{% else %}Basic User{% endif %}</p>
                                <form action="{% url 'account_logout' %}" method="post">
                                    {% csrf_token %}
                                    <button type="submit" class="UserBadge-button">Logout</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="SiteHeader-bottom">
            <div class="u-container SiteHeader-bottom-container">
                <nav class="SiteNav HorizontalStack HorizontalStack--large">
                    <a class="SiteNav-link {% if '/' == request.path %}SiteNav-link--active{% endif %}" href="{% url 'home' %}">Dashboard</a>
                    {% if request.user.is_company_admin %}
                        <a class="SiteNav-link {% if '/users/' in request.path or '/projects/people/' in request.path %}SiteNav-link--active{% endif %}" href="{% url 'person_list' %}">Manage</a>
                    {% endif %}
                </nav>
                <div class="SiteActions HorizontalStack u-align-right">
                    {% if request.user.is_company_admin %}
                        {% include 'sitewide/partials/create_project_program_modal.html' %}
                    {% else %}
                        <a class="SiteActions-link" href="{% url 'project_create' %}">
                            <button class="Button Button--CreateProjectProgram">+ Create</button>
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </header>
    <div class="SiteHeader-spacer"></div>
{% endblock %}

