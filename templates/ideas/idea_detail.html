{% extends 'idea_tracker_base.html' %}
{% load idea_tags project_tags static %}
{% load project_tags %}

{% block main %}
    <header class="PageHeader PageHeader--large">
        <div class="u-container PageHeader-container">
            <div>
                <h1 class="PageTitle">{{ idea.name }}</h1>
                <div class="HorizontalStack HorizontalStack--large">
                    <div class="PageHeader-info">
                        ID: <span class="PageHeader-info--value">{{ idea.id }}</span>
                    </div>
                    {% if idea.primary_division %}
                        <div class="PageHeader-info">
                            Segment: <span class="PageHeader-info--value">{{ idea.primary_division }}</span>
                        </div>
                    {% endif %}
                </div>
            </div>
            <div class="PageHeader-actions">
                <div class="Button Button--secondary SummarySlideDownloadButton"
                     data-filename="{{ presentation_file }}"
                     data-href="{% url 'idea-slide-download' idea.pk %}"
                >
                    Summary Slide
                </div>
                {% if can_edit %}
                    <a id="EditIdeaButton" class="Button Button--primary" href="{% url 'idea-update' idea.pk %}">Edit</a>
                {% endif %}
            </div>
        </div>
    </header>
    <div class="PageHeader-spacer">
    </div>
    <div class="PageContent">
        <div class="PageSection">
            <div class="u-container">
                <div class="PageColumns PageColumns--3-1">
                    <div class="PageColumns-column">
                        <div class="ContentBox">
                            <h1 class="ContentBox-title">Summary</h1>
                            <div class="ContentBox-text">
                                {{ idea.summary|safe }}
                            </div>
                        </div>
                        {% if idea.business_case %}
                            <div class="ContentBox">
                                <h1 class="ContentBox-title">Business Case</h1>
                                <div class="ContentBox-text">
                                    {{ idea.business_case|safe }}
                                </div>
                            </div>
                        {% endif %}
                        {% if display_financial %}
                            <div class="ContentBox">
                                <h1 class="ContentBox-title">Finance</h1>
                                {% if idea.internal_savings_initiative %}
                                    <div class="ContentBox-section">
                                        <div class="SubheadingWithIcon SubheadingWithIcon--InternalSavingsInitiative">Hard Dollar Savings</div>
                                    </div>
                                {% endif %}
                                {% if idea.committed_to_spend %}
                                    <div class="ContentBox-section">
                                        <div class="SubheadingWithIcon SubheadingWithIcon--CommittedToSpend">Committed to Spend</div>
                                    </div>
                                {% endif %}
                                {% if idea.capital_expenditure or idea.opex_expenditure %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Expenditure Type</h4>
                                        <p class="ContentBox-section-text">{% if idea.capital_expenditure %}Capital{% else %}OpEx{% endif %}</p>
                                    </div>
                                {% endif %}
                                {% if idea.funding_size %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Funding Size</h4>
                                        <p class="ContentBox-section-text">{{ idea.funding_size_display }}</p>
                                    </div>
                                {% endif %}
                                {% if idea.annualized_savings %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Annualized Savings</h4>
                                        <p class="ContentBox-section-text">{{ idea.annualized_savings_display }}</p>
                                    </div>
                                {% endif %}
                                {% if idea.estimation_confidence %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Estimation Confidence</h4>
                                        <p class="ContentBox-section-text">{{ idea.estimation_confidence_display }}</p>
                                    </div>
                                {% endif %}
                                {% if idea.funding_source %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Funding Source</h4>
                                        <p class="ContentBox-section-text">{{ idea.funding_source_display }}</p>
                                    </div>
                                {% endif %}
                                {% if idea.payback_period %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Payback Period</h4>
                                        <p class="ContentBox-section-text">{{ idea.payback_period_display }}</p>
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}
                        {% if display_impact %}
                            <div class="ContentBox">
                                <h1 class="ContentBox-title">Project Impact</h1>
                                {% if idea.has_technology_components is not None %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Technology Components</h4>
                                        <p class="ContentBox-section-text">
                                            {% if idea.has_technology_components %}Yes{% else %}No{% endif %}
                                        </p>
                                    </div>
                                    {% if idea.has_technology_components and idea.technology_components %}
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Technology Considerations</h4>
                                            <div class="ContentBox-section-text">
                                                {{ idea.technology_components|safe }}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endif %}
                                {% if idea.response_to_audit is not None %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Audit Finding Response</h4>
                                        <p class="ContentBox-section-text">{{ idea.response_to_audit_display }}</p>
                                    </div>
                                {% endif %}
                                {% if idea.priority %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Priority</h4>
                                        <p class="ContentBox-section-text">{{ idea.priority_display }}</p>
                                    </div>
                                {% endif %}
                                {% if idea.complexity %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Complexity</h4>
                                        <p class="ContentBox-section-text">{{ idea.complexity_display }}</p>
                                    </div>
                                {% endif %}
                                {% if display_current_system %}
                                    <div class="ContentBox-section">
                                        <h1 class="ContentBox-title" style="margin-top: 30px;">Current System</h1>
                                        {% if idea.current_environment is not None and idea.current_environment > -1 %}
                                            <div class="ContentBox-section">
                                                <h4 class="ContentBox-section-title">Failure Risk</h4>
                                                <p class="ContentBox-section-text">{{ idea.current_environment_display }}</p>
                                            </div>
                                        {% endif %}
                                        {% if idea.failure_severity and idea.failure_severity > -1 %}
                                            <div class="ContentBox-section">
                                                <h4 class="ContentBox-section-title">Failure Severity</h4>
                                                <p class="ContentBox-section-text">{{ idea.failure_severity_display }}</p>
                                            </div>
                                        {% endif %}
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="PageColumns-column">
                        <div class="ContentBox StatusCard{% if not idea|is_converted %} {% if not idea.ready_to_convert %}Not{% endif %}ReadyToConvert{% endif %}">
                            <h1 class="ContentBox-title">Status</h1>
                            <div class="ContentBox-section">
                                <h4 class="ContentBox-section-title">Idea #</h4>
                                <p class="ContentBox-section-text">{{ idea.idea_id|idea_friendly_id }}</p>
                            </div>
                            <div class="ContentBox-section">
                                <h4 class="ContentBox-section-title">
                                    Strategic Value Score
                                    <div class="Tooltip Tooltip--inline">
                                        <div class="Tooltip-box Tooltip-box--narrow">
                                            Maximum Value is 40.
                                        </div>
                                    </div>
                                </h4>
                                <p class="ContentBox-section-text">{{ idea.strategic_value|capped_strategic_value }}</p>
                            </div>
                            <div class="ContentBox-section">
                                <h4 class="ContentBox-section-title">State</h4>
                                <p class="ContentBox-section-text">
                                    {% if idea|is_converted %}
                                        <a href="{% url 'project_detail' idea.pk %}" target="_blank" class="u-underline-link u-external-link">Converted - View Project #{{ idea.pk }}</a>
                                    {% else %}
                                        {{ idea.project_state_display|state_display }}
                                    {% endif %}
                                </p>
                            </div>
                            {% if idea.start_date %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Desired Start Date</h4>
                                    <p class="ContentBox-section-text">{{ idea.start_date|date:"n/j/Y" }}</p>
                                </div>
                            {% endif %}
                            <div class="ContentBox-section">
                                <h4 class="ContentBox-section-title">Visibility</h4>
                                <p class="ContentBox-section-text">
                                    {% if idea.private %}
                                        Visible only to people assigned to roles.
                                    {% else %}
                                        Visible to everyone in {{ user.company.name }}.
                                    {% endif %}
                                </p>
                            </div>
                            <div class="ContentBox-section">
                                <h4 class="ContentBox-section-title">{% if idea.state == 'converted' %}Converted to Project{% else %}Last Modified{% endif %}</h4>
                                <p class="ContentBox-section-text">{{ idea.modified|date:"n/j/Y" }}</p>
                            </div>
                            {% if idea.state != 'converted' %}
                                <div class="ContentBox-section">
                                    <div class="StatusChecklist">
                                        <div class="u-margin-top-space">
                                            {% if idea.summary %}<img src="/files/dist/images/icons/icon-check-yes.svg" alt="yes">{% else %}<img src="/files/dist/images/icons/icon-check-no.svg" alt="no">{% endif %}
                                            Defined Business Problem
                                        </div>
                                        <div class="u-margin-top-space">
                                            {% if idea.start_date|within_45_days %}<img src="/files/dist/images/icons/icon-check-yes.svg" alt="yes">{% else %}<img src="/files/dist/images/icons/icon-check-no.svg" alt="no">{% endif %} Start Date Within 45 Days</div>
                                        <div class="u-margin-top-space">
                                            {% if idea.executive_owners.count %}<img src="/files/dist/images/icons/icon-check-yes.svg" alt="yes">{% else %}<img src="/files/dist/images/icons/icon-check-no.svg" alt="no">{% endif %}
                                            Executive Owner Identified
                                        </div>
                                        <div class="u-margin-top-space">
                                            {% if idea.committed_to_spend %}<img src="/files/dist/images/icons/icon-check-yes.svg" alt="yes">{% else %}<img src="/files/dist/images/icons/icon-check-no.svg" alt="no">{% endif %}
                                            Committed to Spend
                                        </div>
                                        <div class="u-margin-top-space">
                                            {% if idea.ready_to_begin %}<img src="/files/dist/images/icons/icon-check-yes.svg" alt="yes">{% else %}<img src="/files/dist/images/icons/icon-check-no.svg" alt="no">{% endif %}
                                            Ready to Begin Project
                                        </div>
                                        <div class="u-margin-top-space">
                                            {% if idea.has_technology_components %}<img src="/files/dist/images/icons/icon-check-yes.svg" alt="yes">{% else %}<img src="/files/dist/images/icons/icon-check-no.svg" alt="no">{% endif %}
                                            Technical Components
                                        </div>
                                    </div>
                                </div>
                                {% if can_convert and can_edit %}
                                    <div id="convertApp">
                                        <form ref="convertForm" class="IdeaDetail--ReadyToConvert" method="post" action="{% url 'idea-convert' object.id %}">
                                            {% csrf_token %}
                                            <button @click.prevent="openConvertModal" type="submit" class="Button Button--neutral" id="convertToProject" data-idea_id="{{ object.id }}">
                                                Convert to Project
                                            </button>
                                            <modal
                                                id="convertModal"
                                                header="Convert Idea"
                                            >
                                                <template v-slot:default>
                                                    <p><strong>For all IT&T impacted projects</strong>, please ensure that you follow the <strong style="display:block">Project Activation Process</strong> prior to starting this Initiative.</p>
                                                </template>

                                                <template v-slot:buttons>
                                                    <button
                                                        type="button"
                                                        class="Button Button--neutral"
                                                        data-micromodal-close
                                                    >
                                                        Cancel
                                                    </button>
                                                    <button
                                                        class="Button Button--primary"
                                                        @click="doConvert"
                                                    >
                                                        Acknowledge
                                                    </button>
                                                </template>
                                            </modal>
                                        </form>
                                    </div>
                                {% endif %}
                                {% if not idea.ready_to_convert %}
                                    <div class="notready">
                                        Not Ready to Convert to a Project
                                    </div>
                                {% endif %}
                            {% endif %}
                        </div>
                        <div class="ContentBox StatusCard">
                            <h1 class="ContentBox-title">Details</h1>

                            {% if idea.project_types.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Type</h4>
                                    <p class="ContentBox-section-text">
                                        {% for project_type in idea.project_types.all %}
                                            {{ project_type.name }}<br/>
                                        {% endfor %}
                                    </p>
                                </div>
                            {% endif %}

                            <div class="ContentBox-section">
                                <h4 class="ContentBox-section-title">Primary Segment</h4>
                                <p class="ContentBox-section-text">
                                    {% if idea.primary_division %}{{ idea.primary_division.name }}{% else %}Unclassified{% endif %}
                                </p>
                            </div>

                            {% if idea.other_involved_divisions.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Other Involved Segments</h4>
                                    <p class="ContentBox-section-text">
                                        {% for segment in idea.other_involved_divisions.all %}
                                            {{ segment.name }}{% if not forloop.last %}, {% endif %}
                                        {% endfor %}
                                    </p>
                                </div>
                            {% endif %}

                            {% if idea.location %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Location</h4>
                                    <p class="ContentBox-section-text">{{ idea.location.name }}</p>
                                </div>
                            {% endif %}

                            {% if idea.strategic_pillars.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Strategic Pillar</h4>
                                    {% for pillar in idea.strategic_pillars.all %}
                                        <p class="ContentBox-section-text">{{ pillar }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}

                            {% if idea.tags.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Tags</h4>
                                    <p class="ContentBox-section-text">{{ idea.tags.all|join:', ' }}</p>
                                </div>
                            {% endif %}
                        </div>
                        <div class="ContentBox">
                            <h1 class="ContentBox-title">People</h1>
                            {% if idea.executive_owners.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Executive Owner</h4>
                                    <p class="ContentBox-section-text">
                                        {% for owner in idea.executive_owners.all %}
                                            {{ owner }}<br/>
                                        {% endfor %}
                                    </p>
                                </div>
                            {% endif %}

                            {% if idea.project_managers.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Project Manager</h4>
                                    <p class="ContentBox-section-text">
                                        {% for mgr in idea.project_managers.all %}
                                            {{ mgr }}<br/>
                                        {% endfor %}
                                    </p>
                                </div>
                            {% endif %}

                            {% if idea.business_leads.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Business Leads</h4>
                                    <p class="ContentBox-section-text">
                                        {% for business_lead in idea.business_leads.all %}
                                            {{ business_lead }}<br/>
                                        {% endfor %}
                                    </p>
                                </div>
                            {% endif %}

                            {% if idea.finance_leads.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Finance Lead</h4>
                                    <p class="ContentBox-section-text">
                                        {% for finance_lead in idea.finance_leads.all %}
                                            {{ finance_lead }}<br/>
                                        {% endfor %}
                                    </p>
                                </div>
                            {% endif %}

                            {% if idea.business_analysts.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Business Analysts</h4>
                                    <p class="ContentBox-section-text">
                                        {% for business_analyst in idea.business_analysts.all %}
                                            {{ business_analyst }}<br/>
                                        {% endfor %}
                                    </p>
                                </div>
                            {% endif %}

                            {% if idea.stakeholders.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Additional Stakeholders</h4>
                                    <p class="ContentBox-section-text">
                                        {% for stakeholder in idea.stakeholders.all %}
                                            {{ stakeholder }}<br/>
                                        {% endfor %}
                                    </p>
                                </div>
                            {% endif %}


                            <div class="ContentBox-section">
                                <h4 class="ContentBox-section-title">Created by</h4>
                                <p class="ContentBox-section-text">{{ idea.created_by }}</p>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="PageSection">
            <div class="u-container">
                {% include "cards/attachments_links.html" with has_perms=idea|has_modify_access_for_project:request.user object_id=idea.id disable_tags=True %}
                {% include "comments/comment_card.html" with object_id=idea.id comments=idea_comments %}
            </div>
        </div>
    </div>
{% endblock %}

{% block javascript %}
    {{ block.super }}
    <script src="{%  static 'dist/javascript/idea-detail.js' %}"></script>
{% endblock %}
