{% extends 'idea_tracker_base.html' %}
{% load static %}

{% block css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'dist/css/project-form.css' %}" />
{% endblock %}

{% block javascript %}
    {{ block.super }}
    <script type="text/javascript">
        requestStorage.tagsWhitelist = {{ tags_whitelist_json|safe }};

    </script>
    <script type="text/javascript" src="{%  static 'dist/javascript/project-form.js' %}"></script>
    <script type="text/javascript" src="{%  static 'dist/javascript/idea-form.js' %}"></script>
{% endblock %}

{% block page-header %}
{% endblock %}

{% block main %}
    <div class="PageHeader PageHeader--large">
        <div class="u-container PageHeader-container">
            <div class="VerticalContainer">
                {% if form.instance.pk %}
                    <h1 class="PageTitle">
                        Edit Idea Details
                    </h1>
                    <div class="HorizontalStack HorizontalStack--large">
                        <div class="PageHeader-info">
                            Idea: <span class="PageHeader-info--value">{{ idea.name }}</span>
                        </div>
                        <div class="PageHeader-info">
                            ID: <span class="PageHeader-info--value">{{ idea.id }}</span>
                        </div>
                        <div class="PageHeader-info">
                            Last Update: <span class="PageHeader-info--value">{{ idea.modified|date:"m/d/Y" }}</span>
                        </div>
                    </div>
                {% else %}
                    <h1 class="PageTitle">
                        Add Idea
                    </h1>
                {% endif %}
            </div>

            <div class="PageHeader-actions">
                {% if form.instance.pk %}
                    <a class="Button Button--text" href="{% url 'idea-delete' form.instance.pk %}">Delete</a>
                {% endif %}
                <a class="Button Button--neutral" href="{% if idea %}{% url 'idea-detail' idea.pk %}{% else %}/idea-tracker/{% endif %}">Cancel</a>
                <span data-submit-for="IdeaForm" class="Button Button--primary">Save</span>
            </div>
        </div>
    </div>
    <div class="PageHeader-spacer--large"></div>

    <div class="Page-links">
        <a class="Page-link" href="#details">Details</a>
        <a class="Page-link" href="#people">People</a>
        <a class="Page-link" href="#finance">Finance</a>
        <a class="Page-link" href="#impact">Project Impact</a>
        <a class="Page-link" href="#links">Attachments</a>
    </div>
    <div class="Page-links-spacer"></div>
    <div class="PageContent">
        <div class="u-container">
            <form id="IdeaForm" class="Form{% if not idea %} AddIdeaForm{% endif %}" action="." method="post" enctype="multipart/form-data">
                {% if form.errors %}
                    <div class="FormErrorBox">
                        <img class="FormErrorBox-icon" src="{% static 'dist/images/icons/icon-error.svg' %}" alt="" />
                        <h1 class="FormErrorBox-message">Please fill out or review the following fields:</h1>
                        <ul class="FormErrorBox-errors">
                            {% for error in form.errors.keys %}
                                <li class="FormErrorBox-errors-item">
                                    <a class="ErrorLink" href="#id_{{ error }}">{{ error|title }}</a>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
                {% csrf_token %}
                {% include "ideas/partials/form_section_details.html" %}
                {% include "ideas/partials/form_section_people.html" %}
                {% include "ideas/partials/form_section_finance.html" %}
                {% include "ideas/partials/form_section_impact.html" %}
                <div class="u-display_none">
                    <select name="attachments" id="id_attachments" multiple></select>
                    <select name="links" id="id_links" multiple></select>
                </div>
            </form>
            {% include "projects/partials/form_section_attachments_links.html" with project=idea %}
        </div>
    </div>
{% endblock %}
