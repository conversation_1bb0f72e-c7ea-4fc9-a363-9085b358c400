{% extends "project_tracker_base.html" %}

{% load static %}
{% load project_tags %}

{% block css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'dist/css/project-form.css' %}" />
{% endblock %}

{% block javascript %}
    {{ block.super }}
    <script type="text/javascript">
        requestStorage.tagsWhitelist = {{ tags_whitelist_json|safe }};

    </script>
    <script type="text/javascript" src="{%  static 'dist/javascript/project-form.js' %}"></script>
{% endblock %}

{% block main %}
    {% person_popup_form %}
    <div class="PageHeader PageHeader--large">

        <div class="u-container PageHeader-container">
            <div class="VerticalContainer">
                <h1 class="PageTitle">
                    Edit Project Details
                </h1>
                <dl class="PageHeader-info">
                    <dt class="PageDetails-label">Project:</dt>
                    <dd class="PageDetails-value">{{ project.name }}</dd>
                    <dt class="PageDetails-label">ID:</dt>
                    <dd class="PageDetails-value">{{ project.id }}</dd>
                    <dt class="PageDetails-label">Last Update:</dt>
                    <dd class="PageDetails-value">{{ project.modified|date:"m/d/Y" }}</dd>
                </dl>
            </div>

            <div class="PageHeader-actions">
                <a class="Button Button--text" href="{% url 'project_detail' project.pk %}">Cancel</a>
                {% if form.instance.pk %}
                    <a class="Button Button--neutral" href="{% url 'project_delete' form.instance.pk %}">Delete</a>
                {% endif %}
                <span data-submit-for="ProjectForm" class="Button Button--primary">Save</span>
            </div>
        </div>
    </div>
    <div class="PageHeader-spacer--large"></div>

    <div class="Page-links">
        <a class="Page-link" href="#status">Status</a>
        <a class="Page-link" href="#details">Details</a>
        <a class="Page-link" href="#scope">Scope</a>
        <a class="Page-link" href="#people">People</a>
        <a class="Page-link" href="#finance">Finance</a>
        <a class="Page-link" href="#time">Time</a>
        <a class="Page-link" href="#impact">Impact</a>
        <a class="Page-link" href="#links">Links</a>
    </div>
    <div class="Page-links-spacer"></div>

    <div class="PageContent">
        <div class="u-container">
            <form id="ProjectForm" class="Form" action="." method="post" enctype="multipart/form-data">
                {% include "projects/partials/form_errors.html" %}

                {% csrf_token %}
                {% include "projects/partials/form_section_status.html" %}
                {% include "projects/partials/form_section_details.html" %}
                {% include "projects/partials/form_section_scope.html" %}
                {% include "projects/partials/form_section_people.html" %}
                {% include "projects/partials/form_section_finance.html" %}
                {% include "projects/partials/form_section_time.html" %}
                {% include "projects/partials/form_section_impact.html" %}
            </form>
            {% include "projects/partials/form_section_attachments_links.html" %}
        </div>
    </div>
{% endblock %}
