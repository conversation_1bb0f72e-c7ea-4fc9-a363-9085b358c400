{% extends manage_base_template %}

{% load static %}

{% block javascript %}
    {{ block.super }}
    <script type="text/javascript" src="{% static 'dist/javascript/person_form.js' %}"></script>
{% endblock %}
{% block css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'dist/css/person_form.css' %}">
{% endblock %}

{% block main %}
    {{ block.super }}
    <div class="PageHeader">
        <div class="u-container PageHeader-container">
            <h1 class="PageTitle">
                {% if form.instance.pk %}Update{% else %}Add{% endif %} Person
            </h1>
            <div class="PageHeader-actions">
                {% if request.user.is_superuser and form.instance.pk %}
                    <a class="Button Button--text" href="{% url 'person_deactivate' form.instance.pk %}">Deactivate</a>
                {% endif %}
                <a class="Button Button--neutral" href="{% url 'person_list' %}">Cancel</a>
                <span data-submit-for="PersonForm" class="Button Button--primary">Save</span>
            </div>
        </div>
    </div>
    <div class="PageHeader-spacer"></div>
    <div class="PageContent">
        <div class="u-container">
            <form id="PersonForm" class="Form" action="." method="post" enctype="multipart/form-data">
                {% csrf_token %}
                {% for field in form.hidden_fields %}
                    {{ field }}
                {% endfor %}
                {% include "forms/form_field.html" with field=form.first_name %}
                {% include "forms/form_field.html" with field=form.last_name %}
                {% if request.user.is_superuser %}
                    {% include "forms/form_field.html" with field=form.email %}
                {% endif %}

                {% if request.user.is_superuser %}
                    <div class="PageColumns PageColumns--1-1">
                        <div class="PageColumns-column">
                            <div class="Form-group">
                                <h3 class="Form-label">Role(s)</h3>
                                <div class="Form-choices">
                                    {% include "forms/form_field.html" with field=form.is_solutions_architecture %}
                                    {% include "forms/form_field.html" with field=form.is_compliance %}
                                    {% include "forms/form_field.html" with field=form.is_finance %}
                                    {% include "forms/form_field.html" with field=form.is_corporate_communications %}
                                    {% include "forms/form_field.html" with field=form.is_segment_legal_reviewer %}
                                    {% include "forms/form_field.html" with field=form.is_corporate_legal_reviewer %}
                                    {% include "forms/form_field.html" with field=form.is_tollgate_board_member %}

                                </div>
                            </div>


                        </div>
                        <div class="PageColumns-column">
                            {% if form.instance.role_list|length %}
                                <h3>Currently Assigned Role(s)</h3>
                                <ul class="RoleList">
                                    {% for role in form.instance.role_list %}
                                        <li class="RoleList-item">{{ role }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </form>
        </div>
    </div>

    <div class="modal modal-tabbed modal-buttons micromodal-slide" id="person-modal" aria-hidden="true">
        <div class="modal__overlay" tabindex="-1" data-micromodal-close>
            <div class="modal__container AttachmentModal--container">
                <form class="AttachmentModalForm">
                    <header class="modal__header titlebar--header">
                        <h2 class="modal__title attachment_modal-title">
                            Segment Assignment Override
                        </h2>
                    </header>
                    <main class="modal__content AddProjectModal--content attachment_modal-content">
                        <div class="u-display_none">
                            {% csrf_token %}
                        </div>

                        <p><span id="person-modal-person"></span> is currently the assigned BRM. Do you want to override
                            the assignment?</p>

                        <div class="AttachmentModal--errors"></div>

                    </main>

                    <footer class="modal__footer AddAttachmentModal--footer">
                        <button type="button" class="Button Button--neutral" aria-label="Close this dialog window"
                                id="no-button">Cancel
                        </button>
                        <button type="button" class="Button Button--primary" id="yes-button">Confirm</button>
                    </footer>
                </form>
            </div>
        </div>
    </div>
{% endblock %}
