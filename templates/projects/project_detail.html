{% extends "project_tracker_base.html" %}

{% load static %}
{% load sitewide_tags %}
{% load project_tags %}
{% load waffle_tags %}

{% block preload %}
    {{ block.super }}
    <link rel="prefetch" href="{% static 'dist/css/project-form.css' %}" as="style" />
    <link rel="prefetch" href="{% static 'dist/javascript/project-form.js' %}" as="script" />
    <script>
        var csrf_token = "{{ csrf_token }}";
        var presentation_file = "{{ presentation_file }}";
    </script>
{% endblock %}

{% block javascript %}
    {{ block.super }}
    <script type="text/javascript" src="{%  static 'dist/javascript/project-details.js' %}"></script>
{% endblock %}

{% block main %}
    {% include "projects/partials/project_page_navigation.html" with active_page="detail"%}

    <div class="PageContent">
        <div class="PageSection">
            <div class="u-container">
                <h1 class="PageSection-title">Current Status</h1>
                <div class="PageColumns PageColumns--2-1">
                    <div class="PageColumns-column">
                        <div class="ContentBox">
                            <h1 class="ContentBox-title">Recent Accomplishments</h1>
                            {% for item in project.recent_accomplishments %}
                                {% if forloop.first %}<ol class="ContentBox-list">{% endif %}
                                <li class="ContentBox-list-item">{{ item.text }}</li>
                                {% if forloop.last %}</ol>{% endif %}
                            {% empty %}
                                <p class="ContentBox-text">No accomplishments.</p>
                            {% endfor %}
                        </div>
                        <div class="ContentBox">
                            <h1 class="ContentBox-title">Planned Activities</h1>
                            {% for item in project.planned_activities %}
                                {% if forloop.first %}<ol class="ContentBox-list">{% endif %}
                                <li class="ContentBox-list-item">{{ item.text }}</li>
                                {% if forloop.last %}</ol>{% endif %}
                            {% empty %}
                                <p class="ContentBox-text">No activities.</p>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="PageColumns-column">
                        {% if project.project_state == 'active' %}
                            <div class="ContentBox">
                                <h1 class="ContentBox-title">Health</h1>
                                <div class="Box">
                                    <div class="Box-item">
                                        <div class="HealthBox-image">
                                            {% health_circle project.current_health.health project.project_state %}
                                        </div>
                                        <h4 class="HealthBox-label">Overall{% if not project.current_health.calculate_overall_health %}<br />(Modified){% endif %}</h4>
                                    </div>
                                    <div class="Box-item">
                                        <div class="HealthBox-image">
                                            {% health_circle project.current_health.budget_health project.project_state %}
                                        </div>
                                        <h4 class="HealthBox-label">Budget</h4>
                                    </div>
                                    <div class="Box-item">
                                        <div class="HealthBox-image">
                                            {% health_circle project.current_health.schedule_health project.project_state %}
                                        </div>
                                        <h4 class="HealthBox-label">Schedule</h4>
                                    </div>
                                    <div class="Box-item">
                                        <div class="HealthBox-image">
                                            {% health_circle project.current_health.scope_health project.project_state %}
                                        </div>
                                        <h4 class="HealthBox-label">Scope</h4>
                                    </div>
                                </div>
                                {% if project.needs_get_to_green and project.get_to_green %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Get to Green</h4>
                                        {{ project.get_to_green }}
                                    </div>
                                {% endif %}
                                {% switch 'raid' %}
                                    <h1 class="ContentBox-title">RAID</h1>
                                    <div class="HorizontalStack HorizontalStack--large">
                                        <div class="VerticalStack">
                                            <div>&nbsp;</div>
                                            <div><a href="{% url 'project_raid_log' project.id %}#RisksTable">Risks </a></div>
                                            <div><a href="{% url 'project_raid_log' project.id %}#AssumptionsTable">Assumptions </a></div>
                                            <div><a href="{% url 'project_raid_log' project.id %}#IssuesTable">Issues </a></div>
                                            <div><a href="{% url 'project_raid_log' project.id %}#DependenciesTable">Dependencies </a></div>
                                        </div>
                                        <div class="Box-item VerticalStack">
                                            <div class="u-bold">High</div>
                                            <div class="u-negative u-center">{{ raid_count.risk_high_count }}</div>
                                            <div class="u-negative u-center">{{ raid_count.assumption_high_count }}</div>
                                            <div class="u-negative u-center">{{ raid_count.issue_high_count }}</div>
                                            <div class="u-negative u-center">{{ raid_count.dependency_high_count }}</div>
                                        </div>
                                        <div class="Box-item VerticalStack">
                                            <div class="u-bold">Medium</div>
                                            <div class="u-neutral u-center">{{ raid_count.risk_medium_count }}</div>
                                            <div class="u-neutral u-center">{{ raid_count.assumption_medium_count }}</div>
                                            <div class="u-neutral u-center">{{ raid_count.issue_medium_count }}</div>
                                            <div class="u-neutral u-center">{{ raid_count.dependency_medium_count }}</div>
                                        </div>
                                        <div class="Box-item VerticalStack">
                                            <div class="u-bold">Low</div>
                                            <div class="u-positive u-center">{{ raid_count.risk_low_count }}</div>
                                            <div class="u-positive u-center">{{ raid_count.assumption_low_count }}</div>
                                            <div class="u-positive u-center">{{ raid_count.issue_low_count }}</div>
                                            <div class="u-positive u-center">{{ raid_count.dependency_low_count }}</div>
                                        </div>
                                    </div>
                                {% endswitch %}
                            </div>
                        {% endif %}
                        <div class="ContentBox">
                            <h1 class="ContentBox-title">Executive Actions</h1>
                            {% for item in project.executive_actions %}
                                {% if forloop.first %}<ul class="ExecutiveAction-list">{% endif %}
                                <li class="ExecutiveAction-list-item">
                                    {% action_icon item.action %}
                                    <h2 class="ExecutiveAction-list-item-title">{{ item.action_display }}</h2>
                                    <div class="ExecutiveAction-list-item-text">{{ item.text }}</div>
                                </li>
                                {% if forloop.last %}</ul>{% endif %}
                            {% empty %}
                                <p class="ContentBox-text">No actions.</p>
                            {% endfor %}
                        </div>
                        {% if project.projectcapitalexpenditure_set.count or project.projectoperationalexpenditure_set.count %}
                            <div class="ContentBox">
                                <h1 class="ContentBox-title">Actual Expenditures</h1>
                                {% if project.projectcapitalexpenditure_set.count %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Capital to Date</h4>
                                        <p class="ContentBox-section-text">{{ project.get_capital_actuals_total|format_money }}</p>
                                    </div>
                                {% endif %}
                                {% if project.projectoperationalexpenditure_set.count %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">OpEx to Date</h4>
                                        <p class="ContentBox-section-text">{{ project.get_operational_actuals_total|format_money }}</p>
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        <div class="PageSection">
            <div class="u-container">
                <h1 class="PageSection-title">Project Details</h1>
                <div class="PageColumns PageColumns--2-1">
                    <div class="PageColumns-column">
                        <div class="ContentBox">
                            <h1 class="ContentBox-title">Summary</h1>
                            <div class="ContentBox-text">
                                {{ project.summary|safe }}
                            </div>
                        </div>
                        <div class="ContentBox">
                            <h1 class="ContentBox-title">Business Case</h1>
                            <div class="ContentBox-text">
                                {{ project.business_case|safe }}
                            </div>
                        </div>
                        <div class="ContentBox">
                            <h1 class="ContentBox-title">Project Scope</h1>
                            <div class="ContentBox-section">
                                <h1 class="ContentBox-section-title">In Scope</h1>
                                <ul>
                                    {% for item in project.in_scope %}
                                        <li class="ContentBox-list-item">
                                            {{ item.text }}
                                        </li>
                                    {% empty %}
                                        <p class="ContentBox-text">Nothing in scope.</p>
                                    {% endfor %}
                                </ul>
                            </div>
                            <div class="ContentBox-section">
                                <h1 class="ContentBox-section-title">Out of Scope</h1>
                                <ul>
                                    {% for item in project.out_of_scope %}
                                        <li class="ContentBox-list-item">
                                            {{ item.text }}
                                        </li>
                                    {% empty %}
                                        <p class="ContentBox-text">Nothing out of scope.</p>
                                    {% endfor %}
                                </ul>
                            </div>
                            <div class="ContentBox-section">
                                <h1 class="ContentBox-section-title">Measure of Success</h1>
                                <ul>
                                    {% for item in project.measure_of_success %}
                                        <li class="ContentBox-list-item">
                                            {{ item.text }}
                                        </li>
                                    {% empty %}
                                        <p class="ContentBox-text">No measure of success.</p>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                        <div class="ContentBox">
                            <h1 class="ContentBox-title">Finance</h1>
                            <div class="SectionColumns SectionColumns--1-1">
                                <div class="SectionColumns-column">
                                    {% if project.internal_savings_initiative != None %}
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Hard Dollar Savings</h4>
                                            <p class="ContentBox-section-text">{% if project.internal_savings_initiative %}Yes{% else %}No{% endif %}</p>
                                        </div>
                                    {% endif %}
                                    {%  if project.committed_to_spend != None %}
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Committed to Spend</h4>
                                            <p class="ContentBox-section-text">{% if project.committed_to_spend %}Yes{% else %}No{% endif %}</p>
                                        </div>
                                    {% endif %}
                                    {% if project.funding_size_display %}
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Funding Size</h4>
                                            <p class="ContentBox-section-text">{{ project.funding_size_display }}</p>
                                        </div>
                                    {% endif %}
                                    {% if project.estimation_confidence_display  %}
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Estimation Confidence</h4>
                                            <p class="ContentBox-section-text">{{ project.estimation_confidence_display }}</p>
                                        </div>
                                    {% endif %}
                                    {% if project.capital_budget_display %}
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Capital Budget</h4>
                                            <p class="ContentBox-section-text">{{ project.capital_budget_display }}</p>
                                        </div>
                                    {% endif %}
                                    {% if project.expense_budget_display %}
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Expense Budget</h4>
                                            <p class="ContentBox-section-text">{{ project.expense_budget_display }}</p>
                                        </div>
                                    {% endif %}
                                    {% if project.funding_source_display %}
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Funding Source</h4>
                                            <p class="ContentBox-section-text">{{ project.funding_source_display }}</p>
                                        </div>
                                    {% endif %}
                                    {% if project.annualized_savings %}
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Annualized Savings</h4>
                                            <p class="ContentBox-section-text">{{ project.annualized_savings_display }}</p>
                                        </div>
                                    {% endif %}
                                    {% if project.payback_period_display %}
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Payback Period</h4>
                                            <p class="ContentBox-section-text">{{ project.payback_period_display }}</p>
                                        </div>
                                    {% endif %}
                                    <div data-conditionally-display-on="payback_period_display" class="{% if project.payback_period_display == "None" %}u-hidden{% endif %}">
                                        {% if project.savings_start_date %}
                                            <div class="ContentBox-section">
                                                <h4 class="ContentBox-section-title">Benefit Tracking Start Date</h4>
                                                <p class="ContentBox-section-text">{{ project.savings_start_date|date:"m/d/Y" }}</p>
                                            </div>
                                        {% endif %}
                                        {% if project.savings_end_date %}
                                            <div class="ContentBox-section">
                                                <h4 class="ContentBox-section-title">Benefit Tracking End Date</h4>
                                                <p class="ContentBox-section-text">{{ project.savings_end_date|date:"m/d/Y" }}</p>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="SectionColumns-column">
                                    {% if project.capital_expenditure %}
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Capital Expenditure</h4>
                                            <p class="ContentBox-section-text">Yes</p>
                                        </div>
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Capital Forecast</h4>
                                            <p class="ContentBox-section-text">{{ project.get_capital_forecast_total|format_money }}</p>
                                        </div>
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">CAR #</h4>
                                            <p class="ContentBox-section-text">{{ project.car_number }}</p>
                                        </div>
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Expense IO #</h4>
                                            <p class="ContentBox-section-text">{{ project.expense_io_number }}</p>
                                        </div>
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Capital IO #</h4>
                                            <p class="ContentBox-section-text">{{ project.capital_io_number }}</p>
                                        </div>
                                    {% endif %}
                                    {% if project.opex_expenditure %}
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">OpEx Expenditure</h4>
                                            <p class="ContentBox-section-text">Yes</p>
                                        </div>
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">OpEx Forecast</h4>
                                            <p class="ContentBox-section-text">{{ project.get_operational_forecast_total|format_money }}</p>
                                        </div>
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Company Code</h4>
                                            <p class="ContentBox-section-text">{{ project.company_code_display }}</p>
                                        </div>
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">Cost Center</h4>
                                            <p class="ContentBox-section-text">{{ project.cost_center }}</p>
                                        </div>
                                        <div class="ContentBox-section">
                                            <h4 class="ContentBox-section-title">GL Account</h4>
                                            <p class="ContentBox-section-text">{{ project.gl_account }}</p>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% if project.has_technology_components != None or project.technology_components or project.sap_impact != None %}
                            <div class="ContentBox">
                                <h1 class="ContentBox-title">Technology Impact</h1>
                                {% if project.has_technology_components != None %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Technology Components</h4>
                                        <p class="ContentBox-section-text">{% if project.has_technology_components %}Yes{% else %}No{% endif %}</p>
                                    </div>
                                {% endif %}
                                {% if project.technology_components %}
                                    <div class="ContentBox-section">
                                        <h1 class="ContentBox-section-title">Technology Considerations</h1>
                                        <div class="ContentBox-section-text">
                                            {{ project.technology_components|safe }}
                                        </div>
                                    </div>
                                {% endif %}
                                {% if project.sap_impact != None %}
                                    <div class="ContentBox-section">
                                        <div class="u-flex-row">
                                            <h4 class="ContentBox-section-title">SAP Impact</h4>
                                            <div class="Tooltip Tooltip--inline">
                                                <div class="Tooltip-box Tooltip-box--narrow">
                                                    Are there any upstream or downstream impacts to SAP?
                                                </div>
                                            </div>
                                        </div>
                                        <p class="ContentBox-section-text">{% if project.sap_impact %}Yes{% else %}No{% endif %}</p>
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}
                        {% if project.corporate_communication_needs %}
                            <div class="ContentBox">
                                <h1 class="ContentBox-title">Corporate Communications Impact</h1>
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Corporate Communication Needs</h4>
                                    <p class="ContentBox-section-text">{{ project.corporate_communications_needs_display }}</p>
                                </div>
                            </div>
                        {% endif %}
                        <div class="ContentBox ContentBox--LegalReview">
                            <div class="SectionColumns SectionColumns--1-1">
                                <div class="SectionColumns-column">
                                    <h1 class="ContentBox-title">Legal Impact</h1>
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Third Party Vendor Engagement</h4>
                                        <p class="ContentBox-section-text">{% if project.products.count %}Yes{% else %}
                                            No{% endif %}</p>
                                    </div>
                                    <div class="ContentBox-section">
                                        <a href="{% url "project_legal" project_pk=project.pk %}"
                                           class="Button Button--secondary Button--large">Review Legal Impact</a>
                                    </div>
                                </div>
                                <div class="SectionColumns-column">
                                    {% if project.products.count %}
                                        <h1 class="ContentBox-title">Legal Review Status</h1>
                                        <table class="InfoTable">
                                            <thead>
                                                <tr class="InfoTable-row">
                                                    <th class="InfoTable-header">Product</th>
                                                    <th class="InfoTable-header">Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for product in project.products.all %}
                                                    <tr class="InfoTable-row">
                                                        <td class="InfoTable-cell">{{ product.name }}</td>
                                                        <td class="InfoTable-cell LegalStatus LegalStatus--{{ product.state.value|lower }}">{{ product.display_status }}</td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% if project.current_environment_display or project.failure_severity_display or project.response_to_audit != None or project.priority or project.complexity %}
                            <div class="ContentBox">
                                <div class="SectionColumns SectionColumns--1-1">
                                    {% if project.response_to_audit != None or project.priority or project.complexity %}
                                        <div class="SectionColumns-column">
                                            <h1 class="ContentBox-title">Risk Assessment</h1>
                                            <div class="ContentBox-section">
                                                {% if project.response_to_audit != None %}
                                                    <div class="ContentBox-section">
                                                        <h4 class="ContentBox-section-title">Audit Finding Response</h4>
                                                        <p class="ContentBox-section-text">{{ project.response_to_audit_display }}
                                                        </div>
                                                {% endif %}
                                                {% if project.priority %}
                                                    <div class="ContentBox-section">
                                                        <h4 class="ContentBox-section-title">Priority</h4>
                                                        <p class="ContentBox-section-text">{{ project.priority_display }}</p>
                                                    </div>
                                                {% endif %}
                                                {% if project.complexity %}
                                                    <div class="ContentBox-section">
                                                        <h4 class="ContentBox-section-title">Complexity</h4>
                                                        <p class="ContentBox-section-text">{{ project.complexity_display }}</p>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}
                                    {% if project.current_environment_display or project.failure_severity_display %}
                                        <div class="SectionColumns-column">
                                            <h1 class="ContentBox-title">Current System</h1>
                                            <div class="ContentBox-section">
                                                {% if project.current_environment_display %}
                                                    <div class="ContentBox-section">
                                                        <h4 class="ContentBox-section-title">Failure Risk</h4>
                                                        <p class="ContentBox-section-text">{{ project.current_environment_display }}</p>
                                                    </div>
                                                {% endif %}
                                                {% if project.failure_severity_display %}
                                                    <div class="ContentBox-section">
                                                        <h4 class="ContentBox-section-title">Failure Severity</h4>
                                                        <p class="ContentBox-section-text">{{ project.failure_severity_display }}</p>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                    <div class="PageColumns-column">
                        <div class="ContentBox">
                            <h1 class="ContentBox-title">Details</h1>
                            <div class="ContentBox-section">
                                <h4 class="ContentBox-section-title">Project #</h4>
                                <p class="ContentBox-section-text">{{ project.id }}</p>
                            </div>
                            {% if project.program and can_view_program and project.program.active %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Program</h4>
                                    <p class="ContentBox-section-text">
                                        <a class="ExternalLink" href="{% url 'program_detail' project.program.id %}" target="_blank">
                                            {{ project.program.name }}
                                            <img alt="link to project page" class="ExternalLinkIcon" src="{% static 'dist/images/icons/icon-arrow.svg' %}"/>
                                        </a>
                                    </p>
                                </div>
                            {% endif %}

                            {% if project.idea_id %}
                                {% include "projects/partials/converted_from_idea.html" %}
                            {% endif %}
                            <div class="ContentBox-section">
                                <div class="u-flex-row">
                                    <h4 class="ContentBox-section-title">Strategic Value</h4>
                                    <div class="Tooltip Tooltip--inline">
                                        <div class="Tooltip-box Tooltip-box--narrow">
                                            Maximum Value is 40.
                                        </div>
                                    </div>
                                </div>

                                <p class="ContentBox-section-text">{{ project.get_strategic_value }}</p>
                            </div>
                            <div class="ContentBox-section">
                                <h4 class="ContentBox-section-title">Project State</h4>
                                <p class="ContentBox-section-text">{{ project.project_state_display }}</p>
                            </div>
                            <div class="ContentBox-section">
                                <h4 class="ContentBox-section-title">Visibility</h4>
                                <p class="ContentBox-section-text">
                                    {% if project.private %}
                                        Project Team
                                    {% else %}
                                        All {{ user.company.name }}
                                    {% endif %}
                                </p>
                            </div>
                            {% if project.phase %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Phase</h4>
                                    <p class="ContentBox-section-text">{{ project.phase_display }}</p>
                                </div>
                            {% endif %}
                            {% if project.project_rigor %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Project Rigor</h4>
                                    <p class="ContentBox-section-text">{{ project.project_rigor_display }}</p>
                                    <div class="ButtonGroup ButtonGroup--left">
                                        <a class="Button Button--secondary" href="{% url 'project_rigor_wizard_review' project.pk %}">Rigor Assessment</a>
                                        <a class="Button Button--secondary" href="{% url 'published_document_list' %}?document_search=&filters=on{% if project.primary_division %}&segment={{ project.primary_division.pk }}{% endif %}&segment={{ epmo_segment }}&rigor={{ document_rigor }}">Get Templates</a>
                                    </div>
                                </div>
                            {% endif %}
                            {% if project.primary_division %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Primary Segment</h4>
                                    <p class="ContentBox-section-text">{{ project.primary_division }}</p>
                                </div>
                            {% endif %}
                            {% if project.other_involved_divisions.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Other Involved Segments</h4>
                                    {% for division in project.other_involved_divisions.all %}
                                        <p class="ContentBox-section-text">{{ division }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if strategic_pillars %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Strategic Pillar</h4>
                                    {% for pillar in strategic_pillars %}
                                        <p class="ContentBox-section-text">{{ pillar }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if project.tags.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Tags</h4>
                                    <p class="ContentBox-section-text">
                                        {% for tag in project.tags.all %}
                                            <span>{{ tag.name }}{% if not forloop.last %}, {% endif %}</span>
                                        {% endfor %}
                                    </p>
                                </div>
                            {% endif %}
                            <div class="ContentBox-section">
                                <h4 class="ContentBox-section-title">Last Update</h4>
                                <p class="ContentBox-section-text">{{ project.modified|date:"m/d/Y" }}</p>
                            </div>
                        </div>
                        <div class="ContentBox">
                            <h1 class="ContentBox-title">People</h1>
                            {% if project.executive_owners.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Executive Owner</h4>
                                    {% for person in project.executive_owners.all %}
                                        <p class="ContentBox-section-text">{{ person }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if project.project_managers.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Project Manager</h4>
                                    {% for person in project.project_managers.all %}
                                        <p class="ContentBox-section-text">{{ person }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if project.business_leads.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Business Leads</h4>
                                    {% for person in project.business_leads.all %}
                                        <p class="ContentBox-section-text">{{ person }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if project.finance_leads.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Finance Lead</h4>
                                    {% for person in project.finance_leads.all %}
                                        <p class="ContentBox-section-text">{{ person }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if project.business_analysts.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Business Analysts</h4>
                                    {% for person in project.business_analysts.all %}
                                        <p class="ContentBox-section-text">{{ person }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if project.other_stakeholders.count %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Additional Stakeholders</h4>
                                    {% for person in project.other_stakeholders.all %}
                                        <p class="ContentBox-section-text">{{ person }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}

                            <div class="ContentBox-section">
                                <h4 class="ContentBox-section-title">Created by</h4>
                                <p class="ContentBox-section-text">{{ project.created_by }}</p>
                            </div>
                        </div>
                        <div class="ContentBox">
                            <h1 class="ContentBox-title">Time</h1>
                            {% if project.expected_duration %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Expected Duration</h4>
                                    <p class="ContentBox-section-text">{{ project.expected_duration_display }}</p>
                                </div>
                            {% endif %}
                            {% if project.start_date %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">Start Date</h4>
                                    <p class="ContentBox-section-text">{{ project.start_date|date:"m/d/Y" }}</p>
                                </div>
                            {% endif %}
                            {% if project.end_date %}
                                <div class="ContentBox-section">
                                    <h4 class="ContentBox-section-title">End Date</h4>
                                    <p class="ContentBox-section-text">{{ project.end_date|date:"m/d/Y" }}</p>
                                </div>
                            {% endif %}
                            {% if project.has_tollgate_dates %}
                                <h1 class="ContentBox-title">Tollgate</h1>
                                {% if project.initiation_date %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Initiation</h4>
                                        <p class="ContentBox-section-text">{{ project.initiation_date|date:"m/d/Y" }}</p>
                                    </div>
                                {% endif %}
                                {% if project.planning_date %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Planning</h4>
                                        <p class="ContentBox-section-text">{{ project.planning_date|date:"m/d/Y" }}</p>
                                    </div>
                                {% endif %}
                                {% if project.execution_date %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Execution</h4>
                                        <p class="ContentBox-section-text">{{ project.execution_date|date:"m/d/Y" }}</p>
                                    </div>
                                {% endif %}
                                {% if project.control_date %}
                                    <div class="ContentBox-section">
                                        <h4 class="ContentBox-section-title">Close</h4>
                                        <p class="ContentBox-section-text">{{ project.control_date|date:"m/d/Y" }}</p>
                                    </div>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="u-container">
                {% include "cards/attachments_links.html" with has_perms=project|has_modify_access_for_project:request.user object_id=project.id %}
                {% include "partials/comments_list.html" with object=project %}
            </div>
        </div>
    </div>
{% endblock %}
