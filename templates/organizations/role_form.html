{% extends "manage_project_tracker_base.html" %}

{% block main %}
    <div class="PageHeader">
        <div class="u-container PageHeader-container">
            <h1 class="PageTitle">{{ page_title }}</h1>
            <div class="PageHeader-actions">
                <a class="Button Button--neutral" href="{% url 'role_list' %}">Cancel</a>
                {% if object.pk %}
                    <a class="Button Button--danger" href="{% url 'role_delete' object.pk %}">Delete</a>
                {% endif %}
                <button type="submit" form="role-form" class="Button Button--primary">
                    {{ submit_text }}
                </button>
            </div>
        </div>
    </div>
    <div class="PageHeader-spacer"></div>

    <div class="u-container">
        <div class="Form-container">
            <form id="role-form" method="post" class="Form">
                {% csrf_token %}

                <div class="Form-section">
                    <h2 class="Form-section-title">Role Information</h2>
                    <div class="Form-section-help">
                        <p class="Form-section-help-text">
                            Define the basic information for this role.
                        </p>
                    </div>

                    <div class="Form-group {% if form.name.errors %}Form-group--errors{% endif %}">
                        <label class="Form-label Form-label--required" for="{{ form.name.id_for_label }}">
                            Role Name
                        </label>
                        <div class="Form-control">
                            {{ form.name }}
                            {% if form.name.help_text %}
                                <p class="Form-help">{{ form.name.help_text }}</p>
                            {% endif %}
                            {% if form.name.errors %}
                                <ul class="Form-errors">
                                    {% for error in form.name.errors %}
                                        <li>{{ error|escape }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>

                    <div class="Form-group {% if form.description.errors %}Form-group--errors{% endif %}">
                        <label class="Form-label" for="{{ form.description.id_for_label }}">
                            Description
                        </label>
                        <div class="Form-control">
                            {{ form.description }}
                            {% if form.description.help_text %}
                                <p class="Form-help">{{ form.description.help_text }}</p>
                            {% endif %}
                            {% if form.description.errors %}
                                <ul class="Form-errors">
                                    {% for error in form.description.errors %}
                                        <li>{{ error|escape }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>
                </div>

                {% if object.pk %}
                    <div class="Form-section">
                        <h2 class="Form-section-title">Quick Actions</h2>
                        <div class="Form-section-help">
                            <p class="Form-section-help-text">
                                After saving, you can manage detailed permissions and user assignments.
                            </p>
                        </div>

                        <div class="ButtonGroup">
                            <a href="{% url 'role_detail' object.pk %}" class="Button Button--secondary">
                                Manage Permissions & Users
                            </a>
                        </div>
                    </div>
                {% endif %}
            </form>
        </div>
    </div>
{% endblock %}
