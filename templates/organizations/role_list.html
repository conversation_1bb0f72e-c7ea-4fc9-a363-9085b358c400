{% extends "manage_project_tracker_base.html" %}

{% block main %}
    {{block.super}}
    <div class="PageHeader">
        <div class="u-container PageHeader-container">
            <h1 class="PageTitle">{{ page_title }}</h1>
            <div class="PageHeader-actions">
                <a class="Button Button--primary" href="{% url 'role_create' %}">
                    + Create Role
                </a>
            </div>
        </div>
    </div>
    <div class="PageHeader-spacer"></div>

    <div class="u-container">
        {% if roles %}
            <div class="Card">
                <div class="Card-header">
                    <h2 class="Card-title">Company Roles</h2>
                    <p class="Card-subtitle">Manage roles and permissions for your organization</p>
                </div>

                <div class="Table-container">
                    <table class="Table">
                        <thead>
                            <tr>
                                <th class="Table-header">Role Name</th>
                                <th class="Table-header">Description</th>
                                <th class="Table-header">Assigned Users</th>
                                <th class="Table-header">Permissions</th>
                                <th class="Table-header">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for role in roles %}
                                <tr class="Table-row">
                                    <td class="Table-cell">
                                        <strong>{{ role.name }}</strong>
                                    </td>
                                    <td class="Table-cell">
                                        {% if role.description %}
                                            {{ role.description|truncatewords:10 }}
                                        {% else %}
                                            <em class="text-gray-500">No description</em>
                                        {% endif %}
                                    </td>
                                    <td class="Table-cell">
                                        {% with role.roleassignment_set.count as user_count %}
                                            {% if user_count > 0 %}
                                                <span class="Badge Badge--success">{{ user_count }} user{{ user_count|pluralize }}</span>
                                            {% else %}
                                                <span class="Badge Badge--neutral">No users</span>
                                            {% endif %}
                                        {% endwith %}
                                    </td>
                                    <td class="Table-cell">
                                        {% with role.permissions.count as perm_count %}
                                            {% if perm_count > 0 %}
                                                <span class="Badge Badge--info">{{ perm_count }} permission{{ perm_count|pluralize }}</span>
                                            {% else %}
                                                <span class="Badge Badge--warning">No permissions</span>
                                            {% endif %}
                                        {% endwith %}
                                    </td>
                                    <td class="Table-cell">
                                        <div class="ButtonGroup">
                                            <a href="{% url 'role_detail' role.pk %}"
                                               class="Button Button--small Button--primary">
                                                Manage
                                            </a>
                                            <a href="{% url 'role_update' role.pk %}"
                                               class="Button Button--small Button--neutral">
                                                Edit
                                            </a>
                                            <a href="{% url 'role_delete' role.pk %}"
                                               class="Button Button--small Button--danger">
                                                Delete
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            {% if is_paginated %}
                <div class="Pagination">
                    <div class="Pagination-info">
                        Showing {{ page_obj.start_index }} to {{ page_obj.end_index }}
                        of {{ page_obj.paginator.count }} roles
                    </div>
                    <div class="Pagination-controls">
                        {% if page_obj.has_previous %}
                            <a href="?page=1" class="Pagination-link">First</a>
                            <a href="?page={{ page_obj.previous_page_number }}" class="Pagination-link">Previous</a>
                        {% endif %}

                        <span class="Pagination-current">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>

                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="Pagination-link">Next</a>
                            <a href="?page={{ page_obj.paginator.num_pages }}" class="Pagination-link">Last</a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="EmptyState">
                <div class="EmptyState-content">
                    <h2 class="EmptyState-title">No Roles Found</h2>
                    <p class="EmptyState-description">
                        You haven't created any roles yet. Roles help you organize permissions and user access within your organization.
                    </p>
                    <a href="{% url 'role_create' %}" class="Button Button--primary">
                        Create Your First Role
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
{% endblock %}
