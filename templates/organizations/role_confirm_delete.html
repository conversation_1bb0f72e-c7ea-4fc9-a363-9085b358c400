{% extends "manage_project_tracker_base.html" %}

{% block main %}
    <div class="PageHeader">
        <div class="u-container PageHeader-container">
            <h1 class="PageTitle">{{ page_title }}</h1>
            <div class="PageHeader-actions">
                <a class="Button Button--neutral" href="{% url 'role_list' %}">Cancel</a>
                <button type="submit" form="delete-form" class="Button Button--danger">
                    Delete Role
                </button>
            </div>
        </div>
    </div>
    <div class="PageHeader-spacer"></div>

    <div class="u-container">
        <div class="Card Card--danger">
            <div class="Card-header">
                <h2 class="Card-title">Confirm Role Deletion</h2>
                <p class="Card-subtitle">This action cannot be undone</p>
            </div>

            <div class="Card-content">
                <div class="Alert Alert--danger">
                    <div class="Alert-content">
                        <h3 class="Alert-title">Are you sure you want to delete this role?</h3>
                        <p class="Alert-description">
                            You are about to permanently delete the role <strong>"{{ object.name }}"</strong>.
                        </p>

                        {% if assigned_users_count > 0 %}
                            <div class="Alert Alert--warning" style="margin-top: 1rem;">
                                <div class="Alert-content">
                                    <h4 class="Alert-title">Warning: Users Currently Assigned</h4>
                                    <p class="Alert-description">
                                        This role is currently assigned to <strong>{{ assigned_users_count }} user{{ assigned_users_count|pluralize }}</strong>.
                                        Deleting this role will remove these assignments and may affect user access permissions.
                                    </p>
                                </div>
                            </div>
                        {% endif %}

                        <div class="mt-4">
                            <h4 class="font-semibold">What will happen when you delete this role:</h4>
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li>The role "{{ object.name }}" will be permanently removed</li>
                                <li>All user assignments to this role will be deleted</li>
                                <li>All permissions associated with this role will be deleted</li>
                                <li>Users who had this role may lose access to certain features</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="Role-details mt-6">
                    <h3 class="text-lg font-semibold mb-3">Role Details</h3>
                    <div class="bg-gray-50 p-4 rounded">
                        <dl class="space-y-2">
                            <div>
                                <dt class="font-medium text-gray-700">Name:</dt>
                                <dd class="text-gray-900">{{ object.name }}</dd>
                            </div>
                            {% if object.description %}
                                <div>
                                    <dt class="font-medium text-gray-700">Description:</dt>
                                    <dd class="text-gray-900">{{ object.description }}</dd>
                                </div>
                            {% endif %}
                            <div>
                                <dt class="font-medium text-gray-700">Company:</dt>
                                <dd class="text-gray-900">{{ object.company.name }}</dd>
                            </div>
                            <div>
                                <dt class="font-medium text-gray-700">Assigned Users:</dt>
                                <dd class="text-gray-900">{{ assigned_users_count }} user{{ assigned_users_count|pluralize }}</dd>
                            </div>
                            <div>
                                <dt class="font-medium text-gray-700">Permissions:</dt>
                                <dd class="text-gray-900">{{ object.permissions.count }} permission{{ object.permissions.count|pluralize }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <form id="delete-form" method="post" class="mt-6">
            {% csrf_token %}
            <div class="flex justify-end space-x-3">
                <a href="{% url 'role_list' %}" class="Button Button--neutral">
                    Cancel
                </a>
                <button type="submit" class="Button Button--danger">
                    Yes, Delete Role
                </button>
            </div>
        </form>
    </div>
{% endblock %}
