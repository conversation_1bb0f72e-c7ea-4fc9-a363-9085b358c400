{% extends "manage_project_tracker_base.html" %}
{% load role_tags %}

{% block main %}
    <div class="PageHeader">
        <div class="u-container PageHeader-container">
            <h1 class="PageTitle">{{ page_title }}</h1>
            <div class="PageHeader-actions">
                <a class="Button Button--neutral" href="{% url 'role_list' %}">Back to Roles</a>
                <a class="Button Button--secondary" href="{% url 'role_update' object.pk %}">Edit Role Info</a>
                <button type="submit" form="role-detail-form" class="Button Button--primary">
                    Save Changes
                </button>
            </div>
        </div>
    </div>
    <div class="PageHeader-spacer"></div>

    <div class="u-container">
        <form id="role-detail-form" method="post" class="Form">
            {% csrf_token %}

            <!-- Role Basic Info Section -->
            <div class="Form-section">
                <h2 class="Form-section-title">Role Information</h2>
                <div class="Form-section-help">
                    <p class="Form-section-help-text">
                        Basic information about this role.
                    </p>
                </div>

                {% include 'forms/form_field.html' with field=form.name %}
                {% include 'forms/form_field.html' with field=form.description %}
            </div>

            <!-- User Assignment Section -->
            <div class="Form-section">
                <h2 class="Form-section-title">User Assignments</h2>
                <div class="Form-section-help">
                    <p class="Form-section-help-text">
                        Select users who should have this role. Only users from your company are shown.
                    </p>
                </div>

                <div class="Form-group">
                    <label class="Form-label">Assigned Users</label>
                    <div class="Form-control">
                        <div class="CheckboxGrid">
                            {% for user in company_users %}
                                <label class="CheckboxGrid-item">
                                    <input type="checkbox"
                                           name="assigned_users"
                                           value="{{ user.id }}"
                                           {% if user in assigned_users %}checked{% endif %}
                                           class="Form-checkbox">
                                    <span class="CheckboxGrid-label">
                                        {{ user.full_name|default:user.email }}
                                        {% if user.email != user.full_name %}
                                            <small class="text-gray-500">({{ user.email }})</small>
                                        {% endif %}
                                    </span>
                                </label>
                            {% empty %}
                                <p class="text-gray-500">No users available in your company.</p>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Permissions Section -->
            <div class="Form-section">
                <h2 class="Form-section-title">Model Permissions</h2>
                <div class="Form-section-help">
                    <p class="Form-section-help-text">
                        Configure what actions users with this role can perform on different types of data.
                    </p>
                </div>

                <div class="PermissionsTable">
                    <table class="Table">
                        <thead>
                            <tr>
                                <th class="Table-header">Model</th>
                                <th class="Table-header">Create</th>
                                <th class="Table-header">Read</th>
                                <th class="Table-header">Update</th>
                                <th class="Table-header">Delete</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for content_type in available_content_types %}
                                <tr class="Table-row">
                                    <td class="Table-cell">
                                        <strong>{{ content_type.model|title }}</strong>
                                        <small class="text-gray-500 block">{{ content_type.app_label }}</small>
                                    </td>
                                    <td class="Table-cell">
                                        <input type="checkbox"
                                               name="permission_{{ content_type.id }}_create"
                                               {% permission_checked current_permissions content_type.id "create" %}
                                               class="Form-checkbox">
                                    </td>
                                    <td class="Table-cell">
                                        <input type="checkbox"
                                               name="permission_{{ content_type.id }}_read"
                                               {% permission_checked current_permissions content_type.id "read" %}
                                               class="Form-checkbox">
                                    </td>
                                    <td class="Table-cell">
                                        <input type="checkbox"
                                               name="permission_{{ content_type.id }}_update"
                                               {% permission_checked current_permissions content_type.id "update" %}
                                               class="Form-checkbox">
                                    </td>
                                    <td class="Table-cell">
                                        <input type="checkbox"
                                               name="permission_{{ content_type.id }}_delete"
                                               {% permission_checked current_permissions content_type.id "delete" %}
                                               class="Form-checkbox">
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </form>
    </div>
{% endblock %}

{% block javascript %}
    {{ block.super }}
    <script>
        // Add some JavaScript for better UX
        document.addEventListener('DOMContentLoaded', function() {
            // Add select all functionality for permissions
            const permissionTable = document.querySelector('.PermissionsTable table');
            if (permissionTable) {
                // Add select all buttons for each permission type
                const headers = permissionTable.querySelectorAll('th');
                headers.forEach((header, index) => {
                    if (index > 0) { // Skip the first column (Model name)
                        const button = document.createElement('button');
                        button.type = 'button';
                        button.className = 'Button Button--small Button--neutral';
                        button.textContent = 'All';
                        button.style.marginLeft = '8px';

                        button.addEventListener('click', function() {
                            const columnIndex = index;
                            const checkboxes = permissionTable.querySelectorAll(`tbody tr td:nth-child(${columnIndex + 1}) input[type="checkbox"]`);
                            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

                            checkboxes.forEach(checkbox => {
                                checkbox.checked = !allChecked;
                            });

                            button.textContent = allChecked ? 'All' : 'None';
                        });

                        header.appendChild(button);
                    }
                });
            }
        });
    </script>
{% endblock %}
